<?php

use Illuminate\Database\Migrations\Migration; // <-- CORRECTED
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sph', function (Blueprint $table) {
            // Check if column doesn't exist before adding it
            if (!Schema::hasColumn('sph', 'letter_setting_id')) {
                $table->foreignId('letter_setting_id')
                    ->nullable()
                    ->after('customer_id') // Place it logically with other IDs
                    ->constrained('letter_settings')
                    ->nullOnDelete(); // If a setting is deleted, don't delete the SPH
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sph', function (Blueprint $table) {
            $table->dropForeign(['letter_setting_id']);
            $table->dropColumn('letter_setting_id');
        });
    }
};
