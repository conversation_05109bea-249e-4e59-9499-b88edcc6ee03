<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationSetting;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class TransaksiJasaNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Creates notification settings specifically for transaksi jasa events.
     */
    public function run(): void
    {
        Log::info('Starting TransaksiJasaNotificationSeeder...');

        // Cari user dengan role manager atau admin yang memiliki nomor HP
        $managers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super_admin', 'admin', 'manager']);
        })
            ->whereNotNull('hp')
            ->where('hp', '!=', '')
            ->get();

        if ($managers->isEmpty()) {
            Log::warning('No managers/admins with phone numbers found. Creating notification settings for all users with phone numbers.');

            // Jika tidak ada manager, ambil semua user yang punya HP
            $managers = User::whereNot<PERSON>ull('hp')
                ->where('hp', '!=', '')
                ->limit(3) // Batasi maksimal 3 user untuk menghindari spam
                ->get();
        }

        // Event-event khusus untuk transaksi jasa
        $jasaEvents = [
            'penjualan_baru_jasa' => 'Transaksi Jasa Baru',
            'penjualan_approved_jasa' => 'Transaksi Jasa Disetujui',
            'penjualan_rejected_jasa' => 'Transaksi Jasa Ditolak',
            'penjualan_revision_jasa' => 'Transaksi Jasa Perlu Revisi',
            'delivery_order_created_jasa' => 'Delivery Order Jasa Dibuat',
            'delivery_order_completed_jasa' => 'Delivery Order Jasa Selesai',
        ];

        $createdCount = 0;
        $existingCount = 0;

        foreach ($managers as $manager) {
            foreach ($jasaEvents as $eventName => $description) {
                // Check if notification setting already exists
                $existing = NotificationSetting::where('event_name', $eventName)
                    ->where('user_id', $manager->id)
                    ->where('channel', 'whatsapp')
                    ->first();

                if ($existing) {
                    $existingCount++;
                    Log::info("Notification setting already exists: {$eventName} for user {$manager->name}");
                    continue;
                }

                $notificationSetting = NotificationSetting::create([
                    'event_name' => $eventName,
                    'user_id' => $manager->id,
                    'channel' => 'whatsapp',
                    'is_active' => true,
                ]);

                $createdCount++;
                Log::info("Created jasa notification setting: {$eventName} for user {$manager->name} ({$manager->hp})");
            }
        }

        Log::info("TransaksiJasaNotificationSeeder completed. Created {$createdCount} new notification settings, {$existingCount} already existed for " . $managers->count() . " users.");

        // Tampilkan ringkasan
        $this->command->info("✅ Created {$createdCount} new jasa notification settings");
        if ($existingCount > 0) {
            $this->command->info("ℹ️  {$existingCount} settings already existed");
        }
        $this->command->info("📱 Configured for " . $managers->count() . " users:");

        foreach ($managers as $manager) {
            $this->command->info("   - {$manager->name} ({$manager->hp})");
        }

        $this->command->info("🔔 Jasa Events configured:");
        foreach ($jasaEvents as $eventName => $description) {
            $this->command->info("   - {$eventName}: {$description}");
        }

        // Tampilkan total notification settings untuk jasa
        $totalJasaSettings = NotificationSetting::whereIn('event_name', array_keys($jasaEvents))
            ->where('is_active', true)
            ->count();
        
        $this->command->info("📊 Total active jasa notification settings: {$totalJasaSettings}");
    }

    /**
     * Get the list of jasa events for reference
     */
    public static function getJasaEvents(): array
    {
        return [
            'penjualan_baru_jasa' => 'Transaksi Jasa Baru',
            'penjualan_approved_jasa' => 'Transaksi Jasa Disetujui',
            'penjualan_rejected_jasa' => 'Transaksi Jasa Ditolak',
            'penjualan_revision_jasa' => 'Transaksi Jasa Perlu Revisi',
            'delivery_order_created_jasa' => 'Delivery Order Jasa Dibuat',
            'delivery_order_completed_jasa' => 'Delivery Order Jasa Selesai',
        ];
    }
}
