<x-filament-panels::page>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter Sales & Marketing</h3>
            {{ $this->form }}
        </div>

        <!-- KPI Cards -->
        @php
            $kpiData = $this->getSalesMarketingKpiData();
            $funnelData = $this->getSalesFunnelData();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total SPH -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-document-text class="h-6 w-6 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Total SPH
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['total_sph']) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conversion Rate -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-arrow-trending-up class="h-6 w-6 text-green-500 dark:text-green-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Conversion Rate
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $kpiData['conversion_rate'] }}%
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Customers -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-user-plus class="h-6 w-6 text-purple-500 dark:text-purple-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Customer Baru
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['new_customers']) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Order Value -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-banknotes class="h-6 w-6 text-orange-500 dark:text-orange-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Rata-rata Nilai Order
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['avg_order_value'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Active Customers -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-users class="h-6 w-6 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Customer Aktif
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['active_customers']) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-currency-dollar class="h-6 w-6 text-green-600" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Total Revenue
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['total_revenue'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Territory Count -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-map class="h-6 w-6 text-teal-500 dark:text-teal-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Wilayah Aktif
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['territory_count']) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Sales Orders -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-shopping-cart class="h-6 w-6 text-red-500 dark:text-red-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Total Sales Order
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['total_so']) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sales Funnel Chart -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sales Funnel</h3>
                <div class="h-64">
                    <canvas id="salesFunnelChart"></canvas>
                </div>
            </div>

            <!-- Customer Acquisition Trend -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tren Akuisisi Customer</h3>
                <div class="h-64">
                    <canvas id="customerAcquisitionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Territory Performance Chart -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performa per Wilayah</h3>
            <div class="h-80">
                <canvas id="territoryPerformanceChart"></canvas>
            </div>
        </div>

        <!-- Top Customers Table -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Top 15 Customer</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Customer
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Tipe
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Order
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Revenue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Avg Order Value
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Volume
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($this->getTopCustomersData() as $customer)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $customer->customer_name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($customer->customer_type === 'corporate') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($customer->customer_type) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ number_format($customer->total_orders) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($customer->total_revenue, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($customer->avg_order_value, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ number_format($customer->total_volume, 2) }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            let salesFunnelChart;
            let customerAcquisitionChart;
            let territoryPerformanceChart;

            function initializeCharts() {
                const funnelData = @json($this->getSalesFunnelData());

                // Sales Funnel Chart
                const funnelCtx = document.getElementById('salesFunnelChart').getContext('2d');
                
                if (salesFunnelChart) {
                    salesFunnelChart.destroy();
                }
                
                salesFunnelChart = new Chart(funnelCtx, {
                    type: 'bar',
                    data: {
                        labels: ['SPH', 'Sales Order', 'Delivered'],
                        datasets: [{
                            label: 'Count',
                            data: [funnelData.sph, funnelData.so, funnelData.delivered],
                            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B'],
                            borderColor: ['#2563EB', '#059669', '#D97706'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Customer Acquisition Chart
                const acquisitionData = @json($this->getCustomerAcquisitionData());
                const acquisitionCtx = document.getElementById('customerAcquisitionChart').getContext('2d');
                
                if (customerAcquisitionChart) {
                    customerAcquisitionChart.destroy();
                }
                
                customerAcquisitionChart = new Chart(acquisitionCtx, {
                    type: 'line',
                    data: {
                        labels: acquisitionData.map(d => d.month),
                        datasets: [{
                            label: 'Customer Baru',
                            data: acquisitionData.map(d => d.new_customers),
                            borderColor: 'rgb(139, 92, 246)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            tension: 0.1
                        }, {
                            label: 'Total Customer',
                            data: acquisitionData.map(d => d.total_customers),
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Territory Performance Chart
                const territoryData = @json($this->getTerritoryPerformanceData());
                const territoryCtx = document.getElementById('territoryPerformanceChart').getContext('2d');
                
                if (territoryPerformanceChart) {
                    territoryPerformanceChart.destroy();
                }
                
                territoryPerformanceChart = new Chart(territoryCtx, {
                    type: 'bar',
                    data: {
                        labels: territoryData.map(t => t.territory_name),
                        datasets: [{
                            label: 'Total Revenue',
                            data: territoryData.map(t => t.total_revenue),
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgb(59, 130, 246)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        }, {
                            label: 'Total Orders',
                            data: territoryData.map(t => t.total_orders),
                            type: 'line',
                            borderColor: 'rgb(249, 115, 22)',
                            backgroundColor: 'rgba(249, 115, 22, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
            }

            // Initialize charts on page load
            document.addEventListener('DOMContentLoaded', function() {
                initializeCharts();
            });

            // Listen for refresh events from Livewire
            document.addEventListener('livewire:init', () => {
                Livewire.on('refresh-charts', () => {
                    setTimeout(() => {
                        location.reload();
                    }, 100);
                });
            });
        </script>
    @endpush
</x-filament-panels::page>
