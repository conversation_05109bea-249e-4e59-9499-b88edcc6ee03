<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExpenseCategory;
use App\Models\Akun;
use Illuminate\Support\Facades\DB;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate existing data
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        ExpenseCategory::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Get specific accounts for different expense types
        $vehicleMaintenanceAccount = Akun::where('kode_akun', '608.3')->first(); // Beban Maintanance Kendaraan Service
        $fuelAccount = Akun::where('kode_akun', '612')->first(); // BBM/Tol Operasional
        $businessTravelAccount = Akun::where('kode_akun', '610')->first(); // Perjalanan Dinas
        $officeUtilitiesAccount = Akun::where('kode_akun', '606')->first(); // Beban Listrik
        $officeSuppliesAccount = Akun::where('kode_akun', '604.1')->first(); // ATK Kantor
        $licenseAccount = Akun::where('kode_akun', '619')->first(); // Lisensi
        $consultationAccount = Akun::where('kode_akun', '624')->first(); // Jasa Konsultasi
        $equipmentMaintenanceAccount = Akun::where('kode_akun', '609')->first(); // Beban Maintanance Inventaris
        $communicationAccount = Akun::where('kode_akun', '607')->first(); // Beban Telepon
        $marketingAccount = Akun::where('kode_akun', '615')->first(); // Beban Marketing
        $trainingAccount = Akun::where('kode_akun', '617')->first(); // Beban Operasional
        $consumptionAccount = Akun::where('kode_akun', '614')->first(); // Konsumsi
        $miscellaneousAccount = Akun::where('kode_akun', '699')->first(); // Beban Lain-lain
        $transportationAccount = Akun::where('kode_akun', '613')->first(); // Beban Jasa Transport
        $feeAccount = Akun::where('kode_akun', '611')->first(); // Fee
        $hppJasaAngkutAccount = Akun::where('kode_akun', '506')->first(); // HPP Jasa Angkut APMS

        // Fallback to first Beban account if specific accounts not found
        $defaultAccount = Akun::where('kategori_akun', 'Beban')->first();
        $defaultAccountId = $defaultAccount ? $defaultAccount->id : 1;

        $categories = [
            // Vehicle Related Expenses
            [
                'name' => 'Ganti Oli Kendaraan',
                'code' => 'ganti_oli',
                'description' => 'Biaya penggantian oli mesin kendaraan operasional',
                'default_account_id' => $vehicleMaintenanceAccount ? $vehicleMaintenanceAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'jenis_oli' => 'Jenis oli yang digunakan',
                    'kilometer' => 'Kilometer saat ganti oli',
                    'bengkel' => 'Nama bengkel/tempat service',
                    'catatan' => 'Catatan tambahan'
                ]
            ],
            [
                'name' => 'Perawatan Kendaraan',
                'code' => 'vehicle_maintenance',
                'description' => 'Biaya perawatan dan perbaikan kendaraan operasional',
                'default_account_id' => $vehicleMaintenanceAccount ? $vehicleMaintenanceAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'jenis_perawatan' => 'Jenis perawatan yang dilakukan',
                    'suku_cadang' => 'Suku cadang yang diganti',
                    'bengkel' => 'Nama bengkel/tempat service',
                    'kilometer' => 'Kilometer saat service',
                    'garansi' => 'Masa garansi service/suku cadang'
                ]
            ],
            [
                'name' => 'Bahan Bakar Kendaraan',
                'code' => 'vehicle_fuel',
                'description' => 'Biaya pengisian bahan bakar kendaraan operasional',
                'default_account_id' => $fuelAccount ? $fuelAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'jenis_bbm' => 'Jenis bahan bakar (Pertalite, Pertamax, Solar)',
                    'liter' => 'Jumlah liter yang diisi',
                    'spbu' => 'Nama SPBU tempat pengisian',
                    'kilometer' => 'Kilometer saat pengisian',
                    'tujuan' => 'Tujuan perjalanan'
                ]
            ],
            [
                'name' => 'Pajak dan STNK Kendaraan',
                'code' => 'vehicle_tax',
                'description' => 'Biaya pajak kendaraan, STNK, dan administrasi kendaraan',
                'default_account_id' => $licenseAccount ? $licenseAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'jenis_pajak' => 'Jenis pajak (Pajak Tahunan, STNK, BPKB)',
                    'masa_berlaku' => 'Masa berlaku pajak/STNK',
                    'samsat' => 'Kantor Samsat tempat pembayaran',
                    'denda' => 'Jumlah denda (jika ada)'
                ]
            ],

            // Business Travel Expenses
            [
                'name' => 'Perjalanan Dinas',
                'code' => 'business_travel',
                'description' => 'Biaya perjalanan dinas karyawan',
                'default_account_id' => $businessTravelAccount ? $businessTravelAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'tujuan' => 'Tujuan perjalanan dinas',
                    'tanggal_berangkat' => 'Tanggal keberangkatan',
                    'tanggal_kembali' => 'Tanggal kepulangan',
                    'transportasi' => 'Jenis transportasi yang digunakan',
                    'akomodasi' => 'Hotel/penginapan yang digunakan',
                    'keperluan' => 'Keperluan/tujuan perjalanan dinas'
                ]
            ],
            [
                'name' => 'Transportasi',
                'code' => 'transportation',
                'description' => 'Biaya transportasi untuk keperluan operasional',
                'default_account_id' => $transportationAccount ? $transportationAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_transportasi' => 'Jenis transportasi (Taksi, Ojek, Bus, dll)',
                    'rute' => 'Rute perjalanan',
                    'keperluan' => 'Keperluan perjalanan',
                    'waktu' => 'Waktu perjalanan'
                ]
            ],

            // Office & Operational Expenses
            [
                'name' => 'Utilitas Kantor',
                'code' => 'office_utilities',
                'description' => 'Biaya listrik, air, internet, dan utilitas kantor lainnya',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_utilitas' => 'Jenis utilitas (Listrik, Air, Internet, Telepon)',
                    'periode' => 'Periode tagihan',
                    'meter_awal' => 'Meter awal (untuk listrik/air)',
                    'meter_akhir' => 'Meter akhir (untuk listrik/air)',
                    'provider' => 'Nama provider/perusahaan'
                ]
            ],
            [
                'name' => 'Perlengkapan Kantor',
                'code' => 'office_supplies',
                'description' => 'Biaya pembelian perlengkapan dan alat tulis kantor',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_barang' => 'Jenis barang yang dibeli',
                    'jumlah' => 'Jumlah barang',
                    'supplier' => 'Nama supplier/toko',
                    'spesifikasi' => 'Spesifikasi barang'
                ]
            ],

            // License & Legal Expenses
            [
                'name' => 'Biaya Lisensi',
                'code' => 'license_fee',
                'description' => 'Biaya lisensi software, perizinan, dan sertifikasi',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_lisensi' => 'Jenis lisensi/perizinan',
                    'masa_berlaku' => 'Masa berlaku lisensi',
                    'instansi' => 'Instansi penerbit lisensi',
                    'nomor_lisensi' => 'Nomor lisensi/sertifikat'
                ]
            ],
            [
                'name' => 'Biaya Legal',
                'code' => 'legal_fee',
                'description' => 'Biaya konsultasi hukum, notaris, dan administrasi legal',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_layanan' => 'Jenis layanan legal',
                    'notaris_lawyer' => 'Nama notaris/lawyer',
                    'dokumen' => 'Dokumen yang diurus',
                    'keperluan' => 'Keperluan layanan legal'
                ]
            ],

            // Maintenance & Repair
            [
                'name' => 'Perawatan Peralatan',
                'code' => 'equipment_maintenance',
                'description' => 'Biaya perawatan dan perbaikan peralatan kantor/operasional',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_peralatan' => 'Jenis peralatan yang diperbaiki',
                    'kerusakan' => 'Jenis kerusakan/masalah',
                    'teknisi' => 'Nama teknisi/service center',
                    'suku_cadang' => 'Suku cadang yang diganti',
                    'garansi' => 'Masa garansi perbaikan'
                ]
            ],

            // Communication & Marketing
            [
                'name' => 'Komunikasi',
                'code' => 'communication',
                'description' => 'Biaya telepon, pulsa, dan komunikasi operasional',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_komunikasi' => 'Jenis komunikasi (Telepon, Pulsa, Internet)',
                    'provider' => 'Nama provider',
                    'nomor' => 'Nomor telepon/kartu',
                    'periode' => 'Periode tagihan'
                ]
            ],
            [
                'name' => 'Promosi dan Marketing',
                'code' => 'marketing',
                'description' => 'Biaya promosi, iklan, dan kegiatan marketing',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_promosi' => 'Jenis promosi/iklan',
                    'media' => 'Media promosi yang digunakan',
                    'target_audience' => 'Target audience',
                    'periode_kampanye' => 'Periode kampanye',
                    'vendor' => 'Vendor/agency yang digunakan'
                ]
            ],

            // Training & Development
            [
                'name' => 'Pelatihan dan Pengembangan',
                'code' => 'training',
                'description' => 'Biaya pelatihan, seminar, dan pengembangan SDM',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_pelatihan' => 'Jenis pelatihan/seminar',
                    'penyelenggara' => 'Penyelenggara pelatihan',
                    'peserta' => 'Nama peserta',
                    'lokasi' => 'Lokasi pelatihan',
                    'sertifikat' => 'Sertifikat yang diperoleh'
                ]
            ],
            [
                'name' => 'Reimburs Delivery Order',
                'code' => 'delivery_reimbursement',
                'description' => 'Reimburs biaya delivery order dengan tracking odometer kendaraan',
                'default_account_id' => $vehicleMaintenanceAccount ? $vehicleMaintenanceAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'odometer_awal' => 'Angka odometer sebelum delivery',
                    'odometer_akhir' => 'Angka odometer setelah delivery',
                    'jarak_tempuh' => 'Total jarak tempuh (km)',
                    'tujuan_delivery' => 'Alamat/lokasi tujuan delivery',
                    'no_delivery_order' => 'Nomor delivery order',
                    'nama_customer' => 'Nama customer/penerima',
                    'jenis_produk' => 'Jenis produk yang dikirim',
                    'biaya_tol' => 'Biaya tol (jika ada)',
                    'biaya_parkir' => 'Biaya parkir (jika ada)',
                    'catatan_perjalanan' => 'Catatan khusus perjalanan'
                ]
            ],

            // Commission & Fee Expenses
            [
                'name' => 'Biaya Komisi',
                'code' => 'commission_fee',
                'description' => 'Biaya komisi dengan perhitungan PPh komisi',
                'default_account_id' => $feeAccount ? $feeAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'nama_penerima_komisi' => 'Nama penerima komisi',
                    'npwp_penerima' => 'NPWP penerima komisi',
                    'jenis_komisi' => 'Jenis komisi (penjualan, referral, dll)',
                    'dasar_perhitungan' => 'Dasar perhitungan komisi',
                    'persentase_komisi' => 'Persentase komisi (%)',
                    'nilai_transaksi' => 'Nilai transaksi dasar komisi',
                    'komisi_bruto' => 'Komisi sebelum pajak',
                    'pph_komisi' => 'PPh komisi yang dipotong',
                    'komisi_netto' => 'Komisi setelah pajak',
                    'periode_komisi' => 'Periode komisi',
                    'bukti_transaksi' => 'Nomor bukti transaksi',
                    'catatan_pajak' => 'Catatan perhitungan pajak'
                ]
            ],

            // Operational Expenses
            [
                'name' => 'Uang Jalan SC',
                'code' => 'uang_jalan_sc',
                'description' => 'Uang jalan untuk supir dan crew operasional',
                'default_account_id' => $hppJasaAngkutAccount ? $hppJasaAngkutAccount->id : $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => true,
                'attributes' => [
                    'nama_supir' => 'Nama supir',
                    'nama_crew' => 'Nama crew/helper',
                    'no_kendaraan' => 'Nomor kendaraan yang digunakan',
                    'rute_perjalanan' => 'Rute perjalanan',
                    'tujuan' => 'Tujuan perjalanan',
                    'tanggal_berangkat' => 'Tanggal keberangkatan',
                    'tanggal_kembali' => 'Tanggal kepulangan',
                    'jarak_tempuh' => 'Estimasi jarak tempuh (km)',
                    'uang_makan' => 'Uang makan per hari',
                    'uang_inap' => 'Uang penginapan (jika menginap)',
                    'uang_bensin' => 'Uang bensin/solar',
                    'uang_tol' => 'Uang tol',
                    'uang_parkir' => 'Uang parkir',
                    'lain_lain' => 'Biaya lain-lain',
                    'total_uang_jalan' => 'Total uang jalan',
                    'catatan' => 'Catatan tambahan'
                ]
            ],

            // Miscellaneous
            [
                'name' => 'Konsumsi',
                'code' => 'consumption',
                'description' => 'Biaya konsumsi rapat, acara, dan operasional',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'jenis_acara' => 'Jenis acara/rapat',
                    'jumlah_peserta' => 'Jumlah peserta',
                    'menu' => 'Menu yang disajikan',
                    'catering' => 'Nama catering/restoran',
                    'lokasi_acara' => 'Lokasi acara'
                ]
            ],
            [
                'name' => 'Lain-lain',
                'code' => 'miscellaneous',
                'description' => 'Biaya operasional lainnya yang tidak termasuk kategori di atas',
                'default_account_id' => $defaultAccountId,
                'is_active' => true,
                'is_vehicle_expense' => false,
                'attributes' => [
                    'keterangan' => 'Keterangan detail pengeluaran',
                    'kategori_internal' => 'Kategori internal perusahaan',
                    'approval_khusus' => 'Memerlukan approval khusus'
                ]
            ]
        ];

        foreach ($categories as $category) {
            ExpenseCategory::create($category);
        }

        $this->command->info('✅ Expense Categories seeded successfully!');
        $this->command->info('📊 Total categories created: ' . count($categories));
        $this->command->info('🚗 Vehicle expense categories: ' . collect($categories)->where('is_vehicle_expense', true)->count());
        $this->command->info('🏢 Non-vehicle expense categories: ' . collect($categories)->where('is_vehicle_expense', false)->count());
        $this->command->info('');
        $this->command->info('🆕 New Categories Added:');
        $this->command->info('   - Biaya Komisi (commission_fee) → 611 (Fee)');
        $this->command->info('   - Uang Jalan SC (uang_jalan_sc) → 506 (HPP Jasa Angkut)');
        $this->command->info('');
        $this->command->info('📋 Special Features:');
        $this->command->info('   - Biaya Komisi: PPh calculation fields');
        $this->command->info('   - Uang Jalan SC: Detailed crew & route tracking');
        $this->command->info('   - Marketing categories: Enhanced for digital marketing');
    }
}
