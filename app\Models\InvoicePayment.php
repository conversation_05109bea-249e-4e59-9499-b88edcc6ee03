<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;
use App\Services\JournalingService;

class InvoicePayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'invoice_payments';

    protected $fillable = [
        'invoice_id',
        'payment_date',
        'amount',
        'payment_method_id',
        'reference_number',
        'notes',
        'status',
        'journal_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'payment_date' => 'datetime',
        'amount' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Payment status constants
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_ACCEPTED = 'accepted';
    public const STATUS_REJECTED = 'rejected';
    public const STATUS_CANCELLED = 'cancelled';

    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_ACCEPTED => 'Diterima',
            self::STATUS_REJECTED => 'Ditolak',
            self::STATUS_CANCELLED => 'Dibatalkan',
        ];
    }

    /**
     * Relationships
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scopes
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', self::STATUS_ACCEPTED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Auto-post to journal when payment is accepted
     */
    public function createJournalEntry(): ?Journal
    {
        if ($this->journal_id || $this->status !== self::STATUS_ACCEPTED) {
            return $this->journal; // Journal already exists or payment not accepted
        }

        try {
            $journalingService = new JournalingService();
            $journalingService->postTransaction('InvoicePayment', $this);

            // Find the created journal
            $journal = Journal::where('source_type', 'InvoicePayment')
                ->where('source_id', $this->id)
                ->latest()
                ->first();

            if ($journal) {
                // Update journal entries to use the payment method's account
                $this->updateJournalEntriesWithPaymentMethod($journal);

                $this->update(['journal_id' => $journal->id]);
                return $journal;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to create journal entry for invoice payment', [
                'payment_id' => $this->id,
                'invoice_id' => $this->invoice_id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update journal entries to use the payment method's COA account
     */
    private function updateJournalEntriesWithPaymentMethod(Journal $journal): void
    {
        $paymentMethodAccount = $this->paymentMethod?->akun;

        if (!$paymentMethodAccount) {
            return;
        }

        // Find the debit entry (cash/bank account) and update it
        $debitEntry = $journal->journalEntries()
            ->where('debit', '>', 0)
            ->first();

        if ($debitEntry) {
            $debitEntry->update([
                'account_id' => $paymentMethodAccount->id,
                'description' => "Penerimaan pembayaran invoice {$this->invoice->nomor_invoice} via {$this->paymentMethod->method_display_name}" .
                    ($this->reference_number ? " - Ref: {$this->reference_number}" : "")
            ]);
        }
    }

    /**
     * Get transaction amount for posting rules
     */
    public function getTransactionAmount(): float
    {
        return (float) $this->amount;
    }

    /**
     * Boot method for handling status changes
     */
    protected static function booted(): void
    {
        static::updating(function (InvoicePayment $payment) {
            // Auto-post to journal when status changes to accepted
            if ($payment->isDirty('status') && $payment->status === self::STATUS_ACCEPTED) {
                $payment->createJournalEntry();
            }
        });

        static::created(function (InvoicePayment $payment) {
            // Auto-post to journal if created with accepted status
            if ($payment->status === self::STATUS_ACCEPTED) {
                $payment->createJournalEntry();
            }
        });
    }

    /**
     * Format currency display
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_ACCEPTED => 'success',
            self::STATUS_REJECTED => 'danger',
            self::STATUS_CANCELLED => 'gray',
            default => 'gray',
        };
    }
}
