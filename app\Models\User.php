<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Spatie\Permission\Traits\HasRoles;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Storage;


class User extends Authenticatable implements FilamentUser, HasMedia
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes, HasRoles, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
        'no_induk',
        'hp',
        'id_jabatan',
        'id_divisi',
        'id_entitas',
        'created_by',
        'signature_path'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'hp' => 'string',
        ];
    }

    // filament isAdmin
    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    // Employee-related relationships (merged from Karyawan)
    public function jabatan()
    {
        return $this->belongsTo(Jabatan::class, 'id_jabatan');
    }

    public function divisi()
    {
        return $this->belongsTo(Divisi::class, 'id_divisi');
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'id_entitas');
    }

    // Operational relationships (using User instead of Karyawan)
    public function deliveryOrders()
    {
        return $this->hasMany(DeliveryOrder::class, 'id_user');
    }

    public function uangJalan()
    {
        return $this->hasMany(UangJalan::class, 'id_user');
    }

    public function expenseRequests()
    {
        return $this->hasMany(ExpenseRequest::class, 'user_id');
    }


    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function createdUsers()
    {
        return $this->hasMany(User::class, 'created_by');
    }

    /**
     * Get the karyawan record for this user
     */
    public function karyawan()
    {
        return $this->hasOne(Karyawan::class, 'id_user');
    }

    /**
     * Get employees supervised by this user
     */
    public function supervisedEmployees()
    {
        return $this->hasMany(Karyawan::class, 'supervisor_id');
    }

    /**
     * Get schedules supervised by this user
     */
    public function supervisedSchedules()
    {
        return $this->hasMany(Schedule::class, 'supervisor_id');
    }

    /**
     * Get attendance records approved by this user
     */
    public function approvedAttendance()
    {
        return $this->hasMany(Absensi::class, 'approved_by');
    }

    /**
     * Register media collections for the User model
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png']);

        $this->addMediaCollection('standalone')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']);
    }

    /**
     * Register media conversions for the User model
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->performOnCollections('avatar', 'standalone');

        $this->addMediaConversion('preview')
            ->width(300)
            ->height(300)
            ->performOnCollections('avatar', 'standalone');
    }

    // role
    public function isAdmin(): bool
    {
        return $this->hasRole('super_admin');
    }

    // get role from  spatie
    public function getRoleNameAttribute()
    {
        return $this->roles->first()->name ?? '';
    }

    public function getSignatureUrlAttribute(): ?string
    {
        if ($this->signature_path) {
            // This is where the conversion happens
            return Storage::disk('public')->url($this->signature_path);
        }
        return null;
    }

    /**
     * Get the user's formatted position, localized for the given language.
     *
     * @param string|null $locale The locale to use ('id' or 'en'). Defaults to the current app locale.
     * @return string The formatted position string.
     */
    public function getPosition(string $locale = null): string
    {
        // Use the provided locale or fall back to the application's current locale.
        $locale = $locale ?? 'id';

        $jabatan = $this->jabatan;
        $divisi = $this->divisi;

        // Return a default string if the user has no position.
        if (!$jabatan || !$divisi) {
            return 'Staff';
        }

        if ($locale === 'id') {
            // English format: Division, Jabatan (using the _en columns)
            $divisiName = $divisi->nama;
            $jabatanName = $jabatan->nama;
            return "{$jabatanName} {$divisiName}";

        } else if ($locale === 'en') {
            // English format: Division, Jabatan (using the _en columns)
            $divisiName = $divisi->nama_en;
            $jabatanName = $jabatan->jabatan_en;
            return "{$divisiName} {$jabatanName}";
        }

        // Default Indonesian format: Jabatan, Divisi
        return "{$jabatan->nama} {$divisi->nama}";
    }
}
