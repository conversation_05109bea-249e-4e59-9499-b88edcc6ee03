<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Creates the alamat_transaksi table for managing transaction-specific addresses.
     */
    public function up(): void
    {
        Schema::create('alamat_transaksi', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to transaksi_penjualan
            $table->foreignId('id_transaksi_penjualan')
                ->constrained('transaksi_penjualan')
                ->onDelete('cascade');
            
            // Address information
            $table->text('alamat')
                ->comment('Alamat lengkap untuk transaksi ini');
            
            $table->text('keterangan')
                ->nullable()
                ->comment('Keterangan tambahan untuk alamat');
            
            $table->integer('urutan')
                ->default(1)
                ->comment('Urutan alamat dalam transaksi');
            
            // Audit fields
            $table->foreignId('created_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->foreignId('updated_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['id_transaksi_penjualan', 'urutan']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alamat_transaksi');
    }
};
