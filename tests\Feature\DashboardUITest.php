<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DashboardUITest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with admin role
        $this->user = User::factory()->create();
    }

    /** @test */
    public function executive_summary_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Executive Summary Dashboard');
        $response->assertSee('Total Revenue');
        $response->assertSee('Revenue by Type');
    }

    /** @test */
    public function customer_analytics_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/customer-analytics-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Dashboard Customer Analytics');
        $response->assertSee('Filter Customer Analytics');
        $response->assertSee('Total Customers');
    }

    /** @test */
    public function sales_by_type_location_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/sales-by-type-location-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Sales by Type & Location Dashboard');
        $response->assertSee('Filter Sales Data');
    }

    /** @test */
    public function detailed_sales_report_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/detailed-sales-report-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Detailed Sales Report Dashboard');
        $response->assertSee('Filter Sales Report');
    }

    /** @test */
    public function operational_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/operational-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Operational Dashboard');
        $response->assertSee('Filter Operational Data');
    }

    /** @test */
    public function sales_marketing_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/sales-marketing-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Sales & Marketing Dashboard');
        $response->assertSee('Filter Sales & Marketing');
    }

    /** @test */
    public function financial_performance_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/financial-performance-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Financial Performance Dashboard');
        $response->assertSee('Filter Financial Data');
    }

    /** @test */
    public function driver_fleet_management_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/driver-fleet-management-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Driver & Fleet Management Dashboard');
        $response->assertSee('Filter Driver & Fleet');
    }

    /** @test */
    public function monthly_sales_realization_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/monthly-sales-realization-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Monthly Sales Realization Dashboard');
    }

    /** @test */
    public function monthly_delivery_report_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/monthly-delivery-report-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Monthly Delivery Report Dashboard');
    }

    /** @test */
    public function accounts_receivable_dashboard_renders_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/accounts-receivable-dashboard');

        $response->assertStatus(200);
        $response->assertSee('Accounts Receivable Dashboard');
    }

    /** @test */
    public function dashboard_components_have_proper_styling()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        
        // Check for proper CSS classes for light/dark mode compatibility
        $response->assertSee('bg-white dark:bg-gray-800');
        $response->assertSee('border border-gray-200 dark:border-gray-700');
        $response->assertSee('text-gray-900 dark:text-white');
    }

    /** @test */
    public function dashboard_charts_containers_are_present()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        
        // Check for chart containers
        $response->assertSee('<canvas id="revenueByTypeChart"></canvas>', false);
        $response->assertSee('<canvas id="monthlyTrendChart"></canvas>', false);
    }

    /** @test */
    public function dashboard_kpi_cards_are_responsive()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        
        // Check for responsive grid classes
        $response->assertSee('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3');
        $response->assertSee('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4');
    }

    /** @test */
    public function dashboard_tables_have_proper_styling()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        
        // Check for table styling
        $response->assertSee('min-w-full divide-y divide-gray-200 dark:divide-gray-700');
        $response->assertSee('bg-gray-50 dark:bg-gray-700');
    }

    /** @test */
    public function dashboard_filters_are_functional()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/customer-analytics-dashboard');

        $response->assertStatus(200);
        
        // Check for filter form elements
        $response->assertSee('selectedPeriod');
        $response->assertSee('selectedCustomerType');
        $response->assertSee('selectedTbbm');
    }

    /** @test */
    public function dashboard_icons_have_proper_colors()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
        
        // Check for icon color classes with dark mode variants
        $response->assertSee('text-blue-500 dark:text-blue-400');
        $response->assertSee('text-green-500 dark:text-green-400');
    }
}
