<?php

namespace App\Http\Controllers;

use App\Models\Surat;
use Barryvdh\DomPDF\Facade\Pdf;

class SuratPdfController extends Controller
{
    /**
     * Preview PDF in browser
     */
    public function preview(Surat $surat)
    {
        try {
            // Load relationships
            $surat->load(['letterSetting', 'createdBy', 'signedBy']);

            // Generate PDF
            $pdf = $this->generatePdf($surat);

            // Create safe filename (remove problematic characters)
            $safeNumber = str_replace(['/', '\\'], '-', $surat->surat_number);
            $filename = 'surat-' . $safeNumber . '.pdf';

            // Return PDF for preview in browser (inline)
            return response($pdf->output())
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="' . $filename . '"');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('PDF Preview Error', [
                'surat_id' => $surat->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return simple error response instead of view
            return response('Error generating PDF: ' . $e->getMessage(), 500)
                ->header('Content-Type', 'text/plain');
        }
    }

    /**
     * Download PDF file
     */
    public function download(Surat $surat)
    {
        try {
            // Load relationships
            $surat->load(['letterSetting', 'createdBy', 'signedBy']);

            // Generate PDF
            $pdf = $this->generatePdf($surat);

            // Create safe filename (remove problematic characters)
            $safeNumber = str_replace(['/', '\\'], '-', $surat->surat_number);
            $filename = 'surat-' . $safeNumber . '.pdf';

            // Return PDF for download (attachment)
            return response($pdf->output())
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('PDF Download Error', [
                'surat_id' => $surat->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->view('errors.pdf-error', [
                'message' => 'Gagal mendownload PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate PDF from surat
     */
    private function generatePdf(Surat $surat)
    {
        // Determine template based on locale
        $template = $surat->letterSetting && $surat->letterSetting->locale === 'en'
            ? 'surat.surat-pdf-en'
            : 'surat.surat-pdf-id';

        // Clean content to prevent UTF-8 issues
        $cleanSurat = clone $surat;
        if ($cleanSurat->content) {
            $cleanSurat->content = $this->cleanUtf8Content($cleanSurat->content);
        }
        if ($cleanSurat->title) {
            $cleanSurat->title = $this->cleanUtf8Content($cleanSurat->title);
        }

        // Generate PDF with template
        $pdf = Pdf::loadView($template, [
            'record' => $cleanSurat,
            'letterSetting' => $surat->letterSetting,
        ]);

        // Set PDF options
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
        ]);

        return $pdf;
    }

    /**
     * Clean UTF-8 content to prevent encoding issues
     */
    private function cleanUtf8Content($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Remove any problematic control characters
        $content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

        // Ensure proper UTF-8 encoding
        if (!mb_check_encoding($content, 'UTF-8')) {
            $content = mb_convert_encoding($content, 'UTF-8', 'UTF-8');
        }

        // Clean HTML entities
        $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $content;
    }
}
