<?php

namespace App\Filament\Resources\SuratResource\Pages;

use App\Filament\Resources\SuratResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;

class ViewSurat extends ViewRecord
{
    protected static string $resource = SuratResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview_pdf')
                ->label('Preview PDF')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn() => route('surat.preview-pdf', $this->record))
                ->openUrlInNewTab(),

            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->url(fn() => route('surat.download-pdf', $this->record)),

            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Informasi Surat')
                            ->schema([
                                TextEntry::make('surat_number')
                                    ->label('Nomor Surat')
                                    ->weight('bold'),

                                TextEntry::make('title')
                                    ->label('Judul Surat')
                                    ->weight('bold'),

                                TextEntry::make('letterSetting.name')
                                    ->label('Penerbitan Surat'),

                                TextEntry::make('surat_date')
                                    ->label('Tanggal Surat')
                                    ->date('d F Y'),

                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($record) => $record->status_color)
                                    ->formatStateUsing(fn($record) => $record->status_label),

                                TextEntry::make('signedBy.name')
                                    ->label('Ditandatangani Oleh')
                                    ->placeholder('Belum ditentukan'),

                                TextEntry::make('createdBy.name')
                                    ->label('Dibuat Oleh'),

                                TextEntry::make('created_at')
                                    ->label('Dibuat Pada')
                                    ->dateTime('d F Y H:i'),
                            ])->columns(2),

                        Section::make('Konten Surat')
                            ->schema([
                                TextEntry::make('content')
                                    ->label('Isi Surat')
                                    ->html()
                                    ->columnSpanFull(),
                            ]),

                        Section::make('Dokumen & Catatan')
                            ->schema([
                                SpatieMediaLibraryImageEntry::make('dokumen_surat')
                                    ->label('Dokumen Surat')
                                    ->collection('dokumen_surat')
                                    ->placeholder('Belum ada dokumen'),

                                TextEntry::make('notes_internal')
                                    ->label('Catatan Internal')
                                    ->placeholder('Tidak ada catatan internal')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),
                    ])->columnSpan(2),
            ])->columns(2);
    }
}
