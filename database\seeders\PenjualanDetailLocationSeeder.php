<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PenjualanDetail;
use App\Models\TransaksiPenjualan;

class PenjualanDetailLocationSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get some penjualan details for testing
        $penjualanDetails = PenjualanDetail::with('transaksiPenjualan')->take(10)->get();

        if ($penjualanDetails->isEmpty()) {
            $this->command->info('No PenjualanDetail records found. Please run TransaksiPenjualanSeeder first.');
            return;
        }

        // Sample locations in Indonesia
        $sampleLocations = [
            [
                'location' => ['lat' => -6.2088, 'lng' => 106.8456], // Jakarta
                'alamat_pengiriman' => 'Jl. Sudirman No. 123, Jakarta Pusat, DKI Jakarta 10220',
                'keterangan_lokasi' => 'Gedung perkantoran, lantai 5, dekat stasiun MRT Bundaran HI'
            ],
            [
                'location' => ['lat' => -7.2575, 'lng' => 112.7521], // Surabaya
                'alamat_pengiriman' => 'Jl. Raya Darmo No. 456, Surabaya, Jawa Timur 60264',
                'keterangan_lokasi' => 'SPBU Shell, sebelah mall Tunjungan Plaza'
            ],
            [
                'location' => ['lat' => -7.7956, 'lng' => 110.3695], // Yogyakarta
                'alamat_pengiriman' => 'Jl. Malioboro No. 789, Yogyakarta, DIY 55213',
                'keterangan_lokasi' => 'Dekat Tugu Yogyakarta, akses mudah dari jalan utama'
            ],
            [
                'location' => ['lat' => -6.9175, 'lng' => 107.6191], // Bandung
                'alamat_pengiriman' => 'Jl. Asia Afrika No. 321, Bandung, Jawa Barat 40111',
                'keterangan_lokasi' => 'Kawasan Gedung Sate, dekat alun-alun Bandung'
            ],
            [
                'location' => ['lat' => -8.6500, 'lng' => 115.2167], // Denpasar
                'alamat_pengiriman' => 'Jl. Gajah Mada No. 654, Denpasar, Bali 80119',
                'keterangan_lokasi' => 'Pusat kota Denpasar, dekat pasar Badung'
            ],
            [
                'location' => ['lat' => 3.5952, 'lng' => 98.6722], // Medan
                'alamat_pengiriman' => 'Jl. Sisingamangaraja No. 987, Medan, Sumatera Utara 20212',
                'keterangan_lokasi' => 'Kawasan bisnis Medan, dekat Merdeka Walk'
            ],
            [
                'location' => ['lat' => -5.1477, 'lng' => 119.4327], // Makassar
                'alamat_pengiriman' => 'Jl. Jenderal Sudirman No. 147, Makassar, Sulawesi Selatan 90233',
                'keterangan_lokasi' => 'Pusat kota Makassar, dekat pelabuhan Paotere'
            ],
            [
                'location' => ['lat' => 0.5394, 'lng' => 101.4519], // Pekanbaru (LRP area)
                'alamat_pengiriman' => 'Jl. Jenderal Sudirman No. 258, Pekanbaru, Riau 28116',
                'keterangan_lokasi' => 'Dekat kantor LRP, akses mudah dari jalan utama'
            ],
            [
                'location' => null, // No location
                'alamat_pengiriman' => 'Jl. Veteran No. 369, Palembang, Sumatera Selatan 30126',
                'keterangan_lokasi' => 'Alamat saja tanpa koordinat GPS'
            ],
            [
                'location' => ['lat' => -2.5489, 'lng' => 118.0149], // Makassar alternative
                'alamat_pengiriman' => null,
                'keterangan_lokasi' => 'Hanya koordinat GPS, alamat akan dikonfirmasi kemudian'
            ],
        ];

        foreach ($penjualanDetails as $index => $detail) {
            $locationData = $sampleLocations[$index % count($sampleLocations)];
            
            $detail->update([
                'location' => $locationData['location'],
                'alamat_pengiriman' => $locationData['alamat_pengiriman'],
                'keterangan_lokasi' => $locationData['keterangan_lokasi'],
            ]);
        }

        $this->command->info('PenjualanDetailLocation seeder completed successfully!');
        $this->command->info('Updated ' . $penjualanDetails->count() . ' penjualan details with location data:');
        $this->command->info('- 8 items with GPS coordinates and addresses');
        $this->command->info('- 1 item with address only (no GPS)');
        $this->command->info('- 1 item with GPS only (no address)');
    }
}
