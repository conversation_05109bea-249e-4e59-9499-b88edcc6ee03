# Flexible Print Rows for Delivery Order

## Overview

Sistem print DO telah diubah dari fixed 5 rows menjadi fleksibel mengikuti jumlah item yang ada di delivery order. Perubahan ini membuat tampilan PDF lebih efisien dan sesuai dengan data aktual.

## Perubahan yang Dilakukan

### 1. Template PDF Updates

#### Sebelum (Fixed Rows)
```php
// Fixed 5 rows dengan empty rows
@for ($i = $itemNumber; $i <= 5; $i++)
    <tr>
        <td>{{ $i }}.</td>
        <td class="text-left">-</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
@endfor
```

#### Sesudah (Dynamic Rows)
```php
// Hanya menampilkan item yang ada, tanpa empty rows
@if ($record->transaksi && $record->transaksi->penjualanDetails->count() > 0)
    @foreach ($record->transaksi->penjualanDetails as $index => $detail)
        <tr>
            <td>{{ $index + 1 }}.</td>
            <td class="text-left">{{ $detail->item->name ?? 'Unknown Item' }}</td>
            <td>{{ number_format($detail->volume_item, 0) }} ltr</td>
            <td>{{ $record->seals->pluck('nomor_segel')->implode(', ') }}</td>
            <td>{{ $record->transaksi->pelanggan->pic_nama ?? '-' }}</td>
            <td></td>
        </tr>
    @endforeach
@else
    <tr>
        <td>1.</td>
        <td class="text-left">No items found</td>
        <td>0 ltr</td>
        <td>{{ $record->seals->pluck('nomor_segel')->implode(', ') }}</td>
        <td>{{ $record->transaksi->pelanggan->pic_nama ?? '-' }}</td>
        <td></td>
    </tr>
@endif
```

### 2. Multiple Seals Integration

#### Update Nomor Segel Display
```php
// Sebelum (single seal)
@if ($record->no_segel)
    {{ $record->no_segel }}
@else
    002360,002361,002362,002363
@endif

// Sesudah (multiple seals)
@if ($record->seals && $record->seals->count() > 0)
    {{ $record->seals->pluck('nomor_segel')->implode(', ') }}
@elseif ($record->no_segel)
    {{ $record->no_segel }}
@else
    -
@endif
```

### 3. PDF Generation Updates

#### Enhanced PDF Action
```php
Tables\Actions\Action::make('pdf')
    ->label('Download PDF')
    ->color('success')
    ->icon('heroicon-o-document-arrow-down')
    ->action(function (DeliveryOrder $record) {
        // Load record with all necessary relationships
        $deliveryOrder = DeliveryOrder::with([
            'transaksi.pelanggan.alamatPelanggan',
            'transaksi.penjualanDetails.item.satuan',
            'kendaraan',
            'user',
            'uangJalan',
            'pengirimanDriver',
            'seals'  // ← Added seals relationship
        ])->find($record->id);

        // Generate PDF with proper options
        $pdf = Pdf::loadView('pdf.delivery_order', [
            'record' => $deliveryOrder, 
            'logoBase64' => $logoBase64
        ])
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial'
            ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $filename);
    })
```

### 4. Benefits

#### Space Efficiency
- **Sebelum**: Selalu 5 rows (termasuk empty rows)
- **Sesudah**: Hanya rows yang diperlukan sesuai jumlah item

#### Data Accuracy
- **Sebelum**: Placeholder data untuk empty rows
- **Sesudah**: Hanya data aktual yang ditampilkan

#### Professional Appearance
- **Sebelum**: Banyak baris kosong dengan tanda "-"
- **Sesudah**: Tampilan bersih tanpa baris kosong

### 5. Test Cases

#### Test Data Created
```bash
php artisan db:seed --class=FlexiblePrintTestSeeder
```

**DO-TEST-001**: 1 item, 2 seals
- PDF akan menampilkan 1 row item
- Seals: "SGL-001-A, SGL-001-B"

**DO-TEST-002**: 3 items, 4 seals  
- PDF akan menampilkan 3 rows item
- Seals: "SGL-002-1, SGL-002-2, SGL-002-3, SGL-002-4"

**DO-TEST-003**: 7 items, 6 seals
- PDF akan menampilkan 7 rows item
- Seals: "SGL-003-1, SGL-003-2, SGL-003-3, SGL-003-4, SGL-003-5, SGL-003-6"

### 6. File Changes

```
resources/views/pdf/
└── delivery_order.blade.php (UPDATED)
    ├── Removed fixed empty rows loop
    ├── Updated seal display logic
    └── Enhanced item display logic

app/Filament/Resources/
├── DeliveryOrderResource.php (UPDATED)
│   └── Enhanced PDF action with seals relationship
└── DeliveryOrderResource/Pages/
    └── ViewDeliveryOrder.php (UPDATED)
        └── Added seals relationship loading

database/seeders/
└── FlexiblePrintTestSeeder.php (NEW)
    └── Test data for different item counts
```

### 7. Usage Examples

#### Single Item DO
```
+-----+------------------+----------+------------------+----------+-----+
| No. | Item Description | Volume   | Seal Numbers     | PIC      | TTD |
+-----+------------------+----------+------------------+----------+-----+
| 1.  | Premium Gasoline | 5,000 ltr| SGL-001-A, SGL-001-B | John | |
+-----+------------------+----------+------------------+----------+-----+
```

#### Multiple Items DO
```
+-----+------------------+----------+------------------+----------+-----+
| No. | Item Description | Volume   | Seal Numbers     | PIC      | TTD |
+-----+------------------+----------+------------------+----------+-----+
| 1.  | Premium Gasoline | 3,000 ltr| SGL-002-1, SGL-002-2,| John | |
| 2.  | Premium Gasoline | 3,000 ltr| SGL-002-3, SGL-002-4 |      | |
| 3.  | Premium Gasoline | 3,000 ltr|                   |      | |
+-----+------------------+----------+------------------+----------+-----+
```

### 8. Backward Compatibility

- ✅ Existing DOs tetap dapat di-print dengan benar
- ✅ Support untuk old `no_segel` field sebagai fallback
- ✅ Graceful handling untuk DO tanpa items
- ✅ Proper error handling untuk missing relationships

### 9. Performance Considerations

- **Eager Loading**: Semua relationships dimuat sekaligus
- **Efficient Queries**: Menghindari N+1 query problem
- **Memory Usage**: Lebih efisien karena tidak ada empty rows
- **PDF Size**: Ukuran file lebih kecil untuk DO dengan sedikit item

## Summary

Sistem print DO sekarang **fleksibel dan mengikuti jumlah item aktual** dalam delivery order:

✅ **Dynamic Rows**: Jumlah row sesuai jumlah item
✅ **Multiple Seals**: Terintegrasi dengan sistem segel baru  
✅ **Clean Layout**: Tidak ada empty rows yang tidak perlu
✅ **Better Performance**: Eager loading dan efficient queries
✅ **Professional Output**: Tampilan PDF yang lebih rapi dan akurat
