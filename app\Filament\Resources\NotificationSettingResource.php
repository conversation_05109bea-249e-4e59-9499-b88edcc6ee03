<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationSettingResource\Pages;
use App\Models\NotificationSetting;
use App\Services\NotificationTemplateService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class NotificationSettingResource extends Resource
{
    protected static ?string $model = NotificationSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell-alert'; // <-- Icon diubah agar lebih sesuai

    protected static ?string $navigationGroup = 'Settings'; // <-- Mengelompokkan menu navigasi

    // Definisikan event yang valid sebagai konstanta untuk konsistensi
    public const VALID_EVENTS = [
        // Transaksi Penjualan/Dagang
        'penjualan_baru' => 'Transaksi Penjualan Baru',
        'penjualan_approved' => 'Transaksi Penjualan Disetujui',
        'penjualan_rejected' => 'Transaksi Penjualan Ditolak',
        'penjualan_revision' => 'Transaksi Penjualan Perlu Revisi',

        // Transaksi Jasa
        'penjualan_baru_jasa' => 'Transaksi Jasa Baru',
        'penjualan_approved_jasa' => 'Transaksi Jasa Disetujui',
        'penjualan_rejected_jasa' => 'Transaksi Jasa Ditolak',
        'penjualan_revision_jasa' => 'Transaksi Jasa Perlu Revisi',
        'delivery_order_created_jasa' => 'Delivery Order Jasa Dibuat',
        'delivery_order_completed_jasa' => 'Delivery Order Jasa Selesai',

        // Expense Request
        'expense_baru' => 'Permintaan Biaya Baru',
        'expense_approved' => 'Permintaan Biaya Disetujui',
        'expense_rejected' => 'Permintaan Biaya Ditolak',
        'expense_revision' => 'Permintaan Biaya Perlu Revisi',

        // SPH
        'sph_baru' => 'SPH Baru',

        // Invoice
        'invoice_baru' => 'Invoice Baru Dikirim',
        'invoice_confirmation' => 'Konfirmasi Penerimaan Invoice',

        // Payment
        'pembayaran_diterima' => 'Pembayaran Diterima',

        // Legacy events (untuk backward compatibility)
        'penjualan_disetujui' => 'Penjualan Disetujui (Legacy)',
        'penjualan_ditolak' => 'Penjualan Ditolak (Legacy)',
        'penjualan_membutuhkan_revisi' => 'Penjualan Butuh Revisi (Legacy)',
        'expense_approved_for_finance' => 'Notif Persetujuan Expense Ke Finance',
        'expense_manager_update_direksi' => 'Notif Manajer: Update Permintaan Biaya (Direksi)',
        'expense_manager_update_sales' => 'Notif Manajer: Update Permintaan Biaya (Sales)',
        'expense_manager_update_operasional' => 'Notif Manajer: Update Permintaan Biaya (Operasional)',
        'expense_manager_update_administrasi' => 'Notif Manajer: Update Permintaan Biaya (Administrasi)',
        'expense_manager_update_keuangan' => 'Notif Manajer: Update Permintaan Biaya (Keuangan)',
        'expense_manager_update_hrd' => 'Notif Manajer: Update Permintaan Biaya (HRD)',
        'expense_manager_update_it' => 'Notif Manajer: Update Permintaan Biaya (IT)',
        'sph_manager_update_sales' => 'Notif Manajer: SPH Baru (Sales)',
        'expense_request_created' => 'Expense Request Dibuat',
        'expense_request_approved' => 'Expense Request Disetujui',
        'expense_request_rejected' => 'Expense Request Ditolak',
        'delivery_order_created' => 'Delivery Order Dibuat',
        'delivery_order_completed' => 'Delivery Order Selesai',
        'invoice_created' => 'Invoice Dibuat',
        'payment_received' => 'Pembayaran Diterima',
    ];

    public const CHANNEL_OPTIONS = [
        'whatsapp' => 'WhatsApp',
        'email' => 'Email',
        'database' => 'Database (Notifikasi di aplikasi)',
    ];

    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                // Menggunakan Section untuk tampilan yang lebih rapi
                Forms\Components\Section::make('Aturan Notifikasi')
                    ->description('Tentukan event apa yang akan mengirim notifikasi ke user mana.')
                    ->schema([
                        // Dropdown untuk memilih event. Mencegah typo.
                        Forms\Components\Select::make('event_name')
                            ->label('Nama Event')
                            ->options(self::VALID_EVENTS)
                            ->required()
                            ->searchable()
                            ->helperText('Pilih event yang akan memicu notifikasi.')
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                                if ($state) {
                                    $defaultTemplates = NotificationTemplateService::getDefaultTemplates();
                                    if (isset($defaultTemplates[$state])) {
                                        $template = $defaultTemplates[$state];
                                        $set('template_variables', $template['variables']);

                                        // Set default template if not already set
                                        $set('message_template', $template['template']);

                                        // Generate preview
                                        $preview = NotificationTemplateService::generatePreview($state, $template['template']);
                                        $set('message_preview', $preview);
                                    }
                                }
                            }),

                        // Dropdown untuk memilih user. Searchable agar mudah dicari.
                        Forms\Components\Select::make('user_id')
                            ->label('User Penerima Notifikasi')
                            ->relationship('user', 'name') // Mengambil 'name' dari relasi 'user'
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Pilih user yang akan menerima pesan.'),

                        // Opsi channel, jika nanti ingin menambah email, dll.
                        Forms\Components\Select::make('channel')
                            ->label('Kirim Melalui')
                            ->options(self::CHANNEL_OPTIONS)
                            ->default('whatsapp')
                            ->required(),

                        // Toggle untuk mengaktifkan/menonaktifkan aturan
                        Forms\Components\Toggle::make('is_active')
                            ->label('Status Aturan Aktif')
                            ->default(true)
                            ->required(),
                    ])->columns(2), // Membuat form menjadi 2 kolom

                // Section untuk template pesan
                Forms\Components\Section::make('Template Pesan')
                    ->description('Kustomisasi template pesan yang akan dikirim. Gunakan placeholder variables untuk data dinamis.')
                    ->schema([
                        Forms\Components\Textarea::make('message_template')
                            ->label('Template Pesan')
                            ->rows(8)
                            ->placeholder('Masukkan template pesan dengan placeholder variables...')
                            ->helperText('Gunakan placeholder seperti {kode_transaksi}, {nama_pelanggan}, dll. Lihat preview di bawah.')
                            ->live(debounce: 500)
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, ?string $state) {
                                if ($state && $get('event_name')) {
                                    $preview = NotificationTemplateService::generatePreview($get('event_name'), $state);
                                    $set('message_preview', $preview);
                                }
                            }),

                        Forms\Components\Textarea::make('message_preview')
                            ->label('Preview Pesan')
                            ->rows(6)
                            ->disabled()
                            ->helperText('Preview pesan dengan sample data')
                            ->placeholder('Preview akan muncul setelah Anda mengisi template...'),

                        Forms\Components\TagsInput::make('template_variables')
                            ->label('Available Variables')
                            ->disabled()
                            ->helperText('Variables yang tersedia untuk event ini')
                            ->placeholder('Variables akan muncul berdasarkan event yang dipilih'),
                    ])
                    ->collapsible()
                    ->collapsed(fn(?NotificationSetting $record) => $record && empty($record->message_template))
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Menampilkan nama event dengan badge agar lebih menarik
                Tables\Columns\TextColumn::make('event_name')
                    ->label('Event')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'penjualan_baru' => 'info',
                        'penjualan_disetujui' => 'success',
                        'penjualan_ditolak' => 'danger',
                        'penjualan_membutuhkan_revisi' => 'warning',
                        default => 'gray',
                    })
                    ->searchable(),

                // Menampilkan nama user dari relasi
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User Penerima')
                    ->searchable()
                    ->sortable(),

                // Menampilkan channel
                Tables\Columns\TextColumn::make('channel')
                    ->label('Channel')
                    ->badge(),

                // Menampilkan status aktif dengan toggle yang bisa di-klik langsung
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Aktif'),

                // Menampilkan status template
                Tables\Columns\IconColumn::make('has_template')
                    ->label('Template')
                    ->getStateUsing(fn($record) => !empty($record->message_template))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->tooltip(fn($record) => !empty($record->message_template) ? 'Template tersedia' : 'Menggunakan template default'),

                // Menampilkan tanggal dibuat
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true), // Sembunyikan default
            ])
            ->filters([
                // Filter berdasarkan nama event
                Tables\Filters\SelectFilter::make('event_name')
                    ->options([
                        'penjualan_baru' => 'Penjualan Baru',
                        'penjualan_disetujui' => 'Penjualan Disetujui',
                        'penjualan_ditolak' => 'Penjualan Ditolak',
                        'penjualan_membutuhkan_revisi' => 'Penjualan Butuh Revisi',
                    ]),
                // Filter berdasarkan user
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                // Action untuk reset template ke default
                Tables\Actions\Action::make('resetTemplate')
                    ->label('Reset Template')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function (NotificationSetting $record): void {
                        $defaultTemplates = NotificationTemplateService::getDefaultTemplates();
                        if (isset($defaultTemplates[$record->event_name])) {
                            $template = $defaultTemplates[$record->event_name];
                            $record->update([
                                'message_template' => $template['template'],
                                'template_variables' => $template['variables'],
                                'message_preview' => NotificationTemplateService::generatePreview($record->event_name, $template['template']),
                            ]);

                            \Filament\Notifications\Notification::make()
                                ->title('Template Direset')
                                ->body('Template berhasil direset ke default.')
                                ->success()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Reset Template ke Default')
                    ->modalDescription('Apakah Anda yakin ingin mereset template ke default? Perubahan custom akan hilang.')
                    ->modalSubmitActionLabel('Ya, Reset')
                    ->modalCancelActionLabel('Batal')
                    ->visible(fn(NotificationSetting $record) => !empty($record->message_template)),

                // Quick Duplicate Action (exact copy)
                Tables\Actions\Action::make('quickDuplicate')
                    ->label('Duplikasi Cepat')
                    ->icon('heroicon-o-squares-plus')
                    ->color('gray')
                    ->action(function (NotificationSetting $record): void {
                        // Check if exact duplicate already exists
                        $existing = NotificationSetting::where([
                            'event_name' => $record->event_name,
                            'user_id' => $record->user_id,
                            'channel' => $record->channel,
                        ])->where('id', '!=', $record->id)->first();

                        if ($existing) {
                            \Filament\Notifications\Notification::make()
                                ->title('Duplikasi Gagal')
                                ->body('Pengaturan yang sama sudah ada.')
                                ->warning()
                                ->send();
                            return;
                        }

                        // Create exact duplicate
                        $duplicate = NotificationSetting::create([
                            'event_name' => $record->event_name,
                            'user_id' => $record->user_id,
                            'channel' => $record->channel,
                            'is_active' => $record->is_active,
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Berhasil Menduplikasi')
                            ->body("Pengaturan berhasil diduplikasi dengan ID: {$duplicate->id}")
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Konfirmasi Duplikasi Cepat')
                    ->modalDescription('Apakah Anda yakin ingin membuat salinan persis dari pengaturan ini?')
                    ->modalSubmitActionLabel('Ya, Duplikasi')
                    ->modalCancelActionLabel('Batal'),

                // Duplicate Action with Modal
                Tables\Actions\Action::make('duplicate')
                    ->label('Duplikasi')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->form([
                        Forms\Components\Section::make('Duplikasi Pengaturan Notifikasi')
                            ->description('Buat salinan pengaturan notifikasi dengan modifikasi yang diperlukan.')
                            ->schema([
                                // Event name - editable
                                Forms\Components\Select::make('event_name')
                                    ->label('Nama Event')
                                    ->options(self::VALID_EVENTS)
                                    ->required()
                                    ->searchable()
                                    ->helperText('Pilih event untuk pengaturan yang diduplikasi.'),

                                // User selection - editable
                                Forms\Components\Select::make('user_id')
                                    ->label('User Penerima Notifikasi')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->helperText('Pilih user yang akan menerima notifikasi.'),

                                // Channel - editable
                                Forms\Components\Select::make('channel')
                                    ->label('Kirim Melalui')
                                    ->options(self::CHANNEL_OPTIONS)
                                    ->required(),

                                // Status - editable
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Status Aturan Aktif')
                                    ->default(true)
                                    ->required(),
                            ])->columns(2),
                    ])
                    ->fillForm(fn(NotificationSetting $record): array => [
                        'event_name' => $record->event_name,
                        'user_id' => $record->user_id,
                        'channel' => $record->channel,
                        'is_active' => $record->is_active,
                    ])
                    ->action(function (array $data): void {
                        // Check if the combination already exists
                        $existing = NotificationSetting::where([
                            'event_name' => $data['event_name'],
                            'user_id' => $data['user_id'],
                            'channel' => $data['channel'],
                        ])->first();

                        if ($existing) {
                            \Filament\Notifications\Notification::make()
                                ->title('Duplikasi Gagal')
                                ->body('Kombinasi event, user, dan channel sudah ada.')
                                ->warning()
                                ->send();
                            return;
                        }

                        // Create a new notification setting with the form data
                        NotificationSetting::create([
                            'event_name' => $data['event_name'],
                            'user_id' => $data['user_id'],
                            'channel' => $data['channel'],
                            'is_active' => $data['is_active'],
                        ]);

                        // Show success notification
                        \Filament\Notifications\Notification::make()
                            ->title('Berhasil Menduplikasi')
                            ->body('Pengaturan notifikasi berhasil diduplikasi.')
                            ->success()
                            ->send();
                    })
                    ->modalHeading('Duplikasi Pengaturan Notifikasi')
                    ->modalSubmitActionLabel('Duplikasi')
                    ->modalCancelActionLabel('Batal')
                    ->modalWidth('2xl'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    // Bulk Duplicate Action
                    Tables\Actions\BulkAction::make('bulkDuplicate')
                        ->label('Duplikasi Terpilih')
                        ->icon('heroicon-o-document-duplicate')
                        ->color('info')
                        ->form([
                            Forms\Components\Section::make('Duplikasi Massal Pengaturan Notifikasi')
                                ->description('Duplikasi semua pengaturan yang dipilih dengan modifikasi yang sama.')
                                ->schema([
                                    Forms\Components\Select::make('target_user_id')
                                        ->label('User Target (Opsional)')
                                        ->relationship('user', 'name')
                                        ->searchable()
                                        ->preload()
                                        ->helperText('Kosongkan untuk mempertahankan user asli dari setiap pengaturan.'),

                                    Forms\Components\Select::make('target_channel')
                                        ->label('Channel Target (Opsional)')
                                        ->options(self::CHANNEL_OPTIONS)
                                        ->helperText('Kosongkan untuk mempertahankan channel asli dari setiap pengaturan.'),

                                    Forms\Components\Toggle::make('target_is_active')
                                        ->label('Status Aktif untuk Duplikasi')
                                        ->default(true),
                                ])->columns(2),
                        ])
                        ->action(function (array $data, \Illuminate\Database\Eloquent\Collection $records): void {
                            $duplicatedCount = 0;
                            $skippedCount = 0;

                            foreach ($records as $record) {
                                // Determine values for the duplicate
                                $duplicateData = [
                                    'event_name' => $record->event_name,
                                    'user_id' => $data['target_user_id'] ?? $record->user_id,
                                    'channel' => $data['target_channel'] ?? $record->channel,
                                    'is_active' => $data['target_is_active'],
                                ];

                                // Check if combination already exists
                                $existing = NotificationSetting::where([
                                    'event_name' => $duplicateData['event_name'],
                                    'user_id' => $duplicateData['user_id'],
                                    'channel' => $duplicateData['channel'],
                                ])->first();

                                if (!$existing) {
                                    NotificationSetting::create($duplicateData);
                                    $duplicatedCount++;
                                } else {
                                    $skippedCount++;
                                }
                            }

                            // Show result notification
                            $message = "Berhasil menduplikasi {$duplicatedCount} pengaturan.";
                            if ($skippedCount > 0) {
                                $message .= " {$skippedCount} pengaturan dilewati karena sudah ada.";
                            }

                            \Filament\Notifications\Notification::make()
                                ->title('Duplikasi Massal Selesai')
                                ->body($message)
                                ->success()
                                ->send();
                        })
                        ->modalHeading('Duplikasi Massal Pengaturan Notifikasi')
                        ->modalSubmitActionLabel('Duplikasi Semua')
                        ->modalCancelActionLabel('Batal')
                        ->modalWidth('2xl')
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationSettings::route('/'),
            'create' => Pages\CreateNotificationSetting::route('/create'),
            'edit' => Pages\EditNotificationSetting::route('/{record}/edit'),
            'view' => Pages\ViewNotificationSetting::route('/{record}'), // <-- Tambahkan halaman view
        ];
    }
}
