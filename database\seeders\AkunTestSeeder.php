<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Akun;

class AkunTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $akuns = [
            // Kas & Bank
            ['100', 'Cash in Hand', 'Aset', 'Debit', 1000000],
            ['101', 'Cash in bank mandiri', 'Aset', 'Debit', 5000000],
            ['102', 'Cash in bank BNI', 'Aset', 'Debit', 3000000],
            ['103', 'Cash in Bank BNI USD', 'Aset', 'Debit', 2000000],
            ['104', 'Deposito', 'Aset', 'Debit', ********],

            // Piutang
            ['200.1', 'Piutang Usaha PT. ABC', 'Aset', 'Debit', 2500000],
            ['200.2', 'Piutang Usaha PT. DEF', 'Aset', 'Debit', 1500000],
            ['201.1', '<PERSON>utang Pemegang sa<PERSON>. John', 'Aset', 'Debit', 500000],
            ['202.1', '<PERSON><PERSON>ng Karyawan An. <PERSON>', 'Aset', 'Debit', 300000],
            ['203', 'PPN Keluaran', 'Aset', 'Debit', 100000],
            ['204', 'PPN Masukan', 'Aset', 'Debit', 150000],
            ['299', 'Piutang Lain - lain', 'Aset', 'Debit', 200000],

            // Persediaan
            ['500', 'Persediaan Awal', 'Aset', 'Debit', 5000000],
            ['504', 'Persediaan Akhir', 'Aset', 'Debit', 4500000],

            // Aktiva Tetap
            ['300', 'Tanah', 'Aset', 'Debit', ********],
            ['301', 'Bangunan', 'Aset', 'Debit', ********],
            ['302', 'Kendaraan', 'Aset', 'Debit', ********],
            ['303', 'Akumulasi Penyusutan Kendaraan', 'Aset', 'Credit', 3000000],
            ['304', 'Inventaris', 'Aset', 'Debit', 5000000],
            ['305', 'Akumulasi Penyusutan Inventaris', 'Aset', 'Credit', 1000000],

            // Hutang
            ['306', 'Hutang Usaha', 'Kewajiban', 'Credit', 2000000],
            ['307', 'Hutang Bank', 'Kewajiban', 'Credit', ********],
            ['308', 'Hutang Leasing/Kendaraan', 'Kewajiban', 'Credit', 5000000],
            ['309', 'Hutang Pajak', 'Kewajiban', 'Credit', 500000],

            // Modal
            ['310', 'Modal Disetor', 'Ekuitas', 'Credit', ********],
            ['311', 'Laba ditahan sd tahun 2023', 'Ekuitas', 'Credit', ********],
            ['312', 'Dividen', 'Ekuitas', 'Debit', 2000000],
            ['313', 'Laba tahun berjalan', 'Ekuitas', 'Credit', 5000000],
        ];

        foreach ($akuns as $akun) {
            Akun::updateOrCreate(
                ['kode_akun' => $akun[0]],
                [
                    'nama_akun' => $akun[1],
                    'kategori_akun' => $akun[2],
                    'tipe_akun' => $akun[3],
                    'saldo_awal' => $akun[4],
                ]
            );
        }
    }
}
