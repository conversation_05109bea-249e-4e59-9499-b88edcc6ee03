<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration cleans up the invoice table by:
     * 1. Adding missing fields that exist in model but not in database
     * 2. Removing legacy fields that are no longer used
     * 3. Ensuring schema consistency
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Add missing fields that exist in model fillable but not in database
            if (!Schema::hasColumn('invoice', 'total_amount')) {
                $table->decimal('total_amount', 15, 2)->nullable()->after('total_invoice');
            }
            
            if (!Schema::hasColumn('invoice', 'status_invoice')) {
                $table->string('status_invoice')->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('invoice', 'status_kirim')) {
                $table->string('status_kirim')->nullable()->after('status_invoice');
            }
            
            if (!Schema::hasColumn('invoice', 'status_arsip')) {
                $table->string('status_arsip')->nullable()->after('status_kirim');
            }
            
            if (!Schema::hasColumn('invoice', 'status_konfirmasi')) {
                $table->string('status_konfirmasi')->nullable()->after('status_arsip');
            }
            
            if (!Schema::hasColumn('invoice', 'status_bayar')) {
                $table->string('status_bayar')->nullable()->after('status_konfirmasi');
            }
            
            if (!Schema::hasColumn('invoice', 'tanggal_kirim')) {
                $table->datetime('tanggal_kirim')->nullable()->after('tanggal_jatuh_tempo');
            }
            
            if (!Schema::hasColumn('invoice', 'metode_kirim')) {
                $table->string('metode_kirim')->nullable()->after('tanggal_kirim');
            }
            
            if (!Schema::hasColumn('invoice', 'penerima')) {
                $table->string('penerima')->nullable()->after('metode_kirim');
            }
            
            if (!Schema::hasColumn('invoice', 'lokasi_arsip')) {
                $table->string('lokasi_arsip')->nullable()->after('penerima');
            }
            
            if (!Schema::hasColumn('invoice', 'catatan_arsip')) {
                $table->text('catatan_arsip')->nullable()->after('lokasi_arsip');
            }
            
            if (!Schema::hasColumn('invoice', 'tanggal_arsip')) {
                $table->datetime('tanggal_arsip')->nullable()->after('catatan_arsip');
            }
            
            if (!Schema::hasColumn('invoice', 'tanggal_konfirmasi_diterima')) {
                $table->datetime('tanggal_konfirmasi_diterima')->nullable()->after('tanggal_arsip');
            }
            
            if (!Schema::hasColumn('invoice', 'nominal_bayar')) {
                $table->decimal('nominal_bayar', 15, 2)->nullable()->after('tanggal_konfirmasi_diterima');
            }
            
            if (!Schema::hasColumn('invoice', 'tanggal_bayar')) {
                $table->date('tanggal_bayar')->nullable()->after('nominal_bayar');
            }
            
            if (!Schema::hasColumn('invoice', 'metode_bayar')) {
                $table->string('metode_bayar')->nullable()->after('tanggal_bayar');
            }
            
            if (!Schema::hasColumn('invoice', 'referensi_bayar')) {
                $table->string('referensi_bayar')->nullable()->after('metode_bayar');
            }
            
            if (!Schema::hasColumn('invoice', 'journal_id')) {
                $table->unsignedBigInteger('journal_id')->nullable()->after('referensi_bayar');
            }
            
            if (!Schema::hasColumn('invoice', 'updated_by')) {
                $table->unsignedBigInteger('updated_by')->nullable()->after('created_by');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->dropColumn([
                'total_amount',
                'status_invoice',
                'status_kirim', 
                'status_arsip',
                'status_konfirmasi',
                'status_bayar',
                'tanggal_kirim',
                'metode_kirim',
                'penerima',
                'lokasi_arsip',
                'catatan_arsip',
                'tanggal_arsip',
                'tanggal_konfirmasi_diterima',
                'nominal_bayar',
                'tanggal_bayar',
                'metode_bayar',
                'referensi_bayar',
                'journal_id',
                'updated_by',
            ]);
        });
    }
};
