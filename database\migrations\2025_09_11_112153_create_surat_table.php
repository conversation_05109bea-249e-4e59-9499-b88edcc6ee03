<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('surat', function (Blueprint $table) {
            $table->id();
            $table->string('surat_number')->unique();
            $table->foreignId('letter_setting_id')->constrained('letter_settings')->restrictOnDelete();
            $table->string('title');
            $table->longText('content');
            $table->date('surat_date');
            $table->enum('status', ['draft', 'sent', 'archived'])->default('draft');
            $table->foreignId('created_by')->constrained('users');
            $table->text('notes_internal')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('surat');
    }
};
