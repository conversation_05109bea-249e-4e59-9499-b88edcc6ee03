<?php

namespace App\Filament\Pages;

use App\Models\DeliveryOrder;
use App\Models\Kendaraan;
use App\Models\User;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class DriverFleetManagementDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static ?string $navigationLabel = 'Driver & Fleet Management';
    protected static ?string $title = 'Dashboard Driver & Fleet Management';
    protected static string $view = 'filament.pages.driver-fleet-management-dashboard';
    protected static ?int $navigationSort = 10;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedDriver = null;
    #[Url(keep: true)]

    public ?string $selectedVehicle = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('director') ?? false;
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_month';
        $this->startDate = $this->startDate ?? now()->startOfMonth()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_week' => 'Minggu Ini',
                        'current_month' => 'Bulan Ini',
                        'current_quarter' => 'Kuartal Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_month')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedDriver')
                    ->label('Driver (Opsional)')
                    ->options(User::whereHas('roles', function ($query) {
                        $query->where('name', 'driver');
                    })->pluck('name', 'id'))
                    ->searchable()
                    ->placeholder('Semua Driver')
                    ->live(),

                Select::make('selectedVehicle')
                    ->label('Kendaraan (Opsional)')
                    ->options(Kendaraan::pluck('no_pol_kendaraan', 'id'))
                    ->searchable()
                    ->placeholder('Semua Kendaraan')
                    ->live(),
            ])
            ->columns(5);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_week' => [
                $this->startDate = $now->startOfWeek()->format('Y-m-d'),
                $this->endDate = $now->endOfWeek()->format('Y-m-d')
            ],
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getFleetKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $baseQuery = DeliveryOrder::query()
            ->whereBetween('tanggal_delivery', [$startDate, $endDate]);

        if ($this->selectedDriver) {
            $baseQuery->where('id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $baseQuery->where('id_kendaraan', $this->selectedVehicle);
        }

        // Basic metrics
        $totalTrips = $baseQuery->count();
        $completedTrips = (clone $baseQuery)->where('status_muat', 'selesai')->count();
        $activeDrivers = (clone $baseQuery)->distinct('id_user')->count();
        $activeVehicles = (clone $baseQuery)->distinct('id_kendaraan')->count();

        // Performance metrics
        $completionRate = $totalTrips > 0 ? round(($completedTrips / $totalTrips) * 100, 1) : 0;

        $onTimeTrips = (clone $baseQuery)
            ->where('status_muat', 'selesai')
            ->whereRaw('DATE(waktu_selesai_muat) <= DATE(tanggal_delivery)')
            ->count();

        $onTimeRate = $completedTrips > 0 ? round(($onTimeTrips / $completedTrips) * 100, 1) : 0;

        // Average delivery time
        $avgDeliveryTime = (clone $baseQuery)
            ->where('status_muat', 'selesai')
            ->whereNotNull('waktu_muat')
            ->whereNotNull('waktu_selesai_muat')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, waktu_muat, waktu_selesai_muat)) as avg_time')
            ->value('avg_time') ?? 0;

        // Fleet utilization
        $totalVehicles = Kendaraan::count();
        $fleetUtilization = $totalVehicles > 0 ? round(($activeVehicles / $totalVehicles) * 100, 1) : 0;

        // Total volume delivered
        $totalVolumeDelivered = (clone $baseQuery)
            ->where('status_muat', 'selesai')
            ->sum('volume_do') ?? 0;

        return [
            'total_trips' => $totalTrips,
            'completed_trips' => $completedTrips,
            'completion_rate' => $completionRate,
            'active_drivers' => $activeDrivers,
            'active_vehicles' => $activeVehicles,
            'total_vehicles' => $totalVehicles,
            'fleet_utilization' => $fleetUtilization,
            'on_time_trips' => $onTimeTrips,
            'on_time_rate' => $onTimeRate,
            'avg_delivery_time' => round($avgDeliveryTime, 1),
            'total_volume_delivered' => $totalVolumeDelivered,
        ];
    }

    public function getTopDriversData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('users', 'delivery_order.id_user', '=', 'users.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate]);

        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        return $query->select([
            'users.name as driver_name',
            'users.no_induk as driver_id',
            DB::raw('COUNT(delivery_order.id) as total_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
            DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as completion_rate'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" AND DATE(delivery_order.waktu_selesai_muat) <= DATE(delivery_order.tanggal_delivery) THEN 1 END) as on_time_trips'),
            DB::raw('AVG(CASE WHEN delivery_order.status_muat = "selesai" AND delivery_order.waktu_muat IS NOT NULL AND delivery_order.waktu_selesai_muat IS NOT NULL THEN TIMESTAMPDIFF(HOUR, delivery_order.waktu_muat, delivery_order.waktu_selesai_muat) END) as avg_delivery_time'),
            DB::raw('SUM(CASE WHEN delivery_order.status_muat = "selesai" THEN delivery_order.volume_do ELSE 0 END) as total_volume_delivered'),
        ])
            ->groupBy('users.id', 'users.name', 'users.no_induk')
            ->orderBy('total_trips', 'desc')
            ->limit(15)
            ->get()
            ->toArray();
    }

    public function getVehiclePerformanceData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('kendaraans', 'delivery_order.id_kendaraan', '=', 'kendaraans.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate]);

        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        return $query->select([
            'kendaraans.no_pol_kendaraan as vehicle_plate',
            'kendaraans.tipe as vehicle_type',
            'kendaraans.kapasitas as vehicle_capacity',
            DB::raw('COUNT(delivery_order.id) as total_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
            DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as utilization_rate'),
            DB::raw('SUM(CASE WHEN delivery_order.status_muat = "selesai" THEN delivery_order.volume_do ELSE 0 END) as total_volume_delivered'),
            DB::raw('ROUND((SUM(CASE WHEN delivery_order.status_muat = "selesai" THEN delivery_order.volume_do ELSE 0 END) / kendaraans.kapasitas) * 100, 1) as capacity_utilization'),
        ])
            ->groupBy('kendaraans.id', 'kendaraans.no_pol_kendaraan', 'kendaraans.tipe', 'kendaraans.kapasitas')
            ->orderBy('total_trips', 'desc')
            ->limit(15)
            ->get()
            ->toArray();
    }

    public function getDriverEfficiencyTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('users', 'delivery_order.id_user', '=', 'users.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate]);

        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        return $query->select([
            DB::raw('DATE(delivery_order.tanggal_delivery) as date'),
            DB::raw('COUNT(delivery_order.id) as total_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" AND DATE(delivery_order.waktu_selesai_muat) <= DATE(delivery_order.tanggal_delivery) THEN 1 END) as on_time_trips'),
            DB::raw('COUNT(DISTINCT delivery_order.id_user) as active_drivers'),
            DB::raw('COUNT(DISTINCT delivery_order.id_kendaraan) as active_vehicles'),
        ])
            ->groupBy(DB::raw('DATE(delivery_order.tanggal_delivery)'))
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    public function getMaintenanceAlertsData(): array
    {
        // Simulated maintenance alerts based on vehicle usage
        $vehicles = Kendaraan::all();
        $alerts = [];

        foreach ($vehicles as $vehicle) {
            $totalTrips = DeliveryOrder::where('id_kendaraan', $vehicle->id)
                ->whereBetween('tanggal_delivery', $this->getDateRange())
                ->count();

            $lastMaintenanceDate = $vehicle->updated_at ?? $vehicle->created_at;
            $daysSinceLastMaintenance = Carbon::now()->diffInDays($lastMaintenanceDate);

            $alertLevel = 'low';
            $alertMessage = 'Normal';

            if ($daysSinceLastMaintenance > 90 || $totalTrips > 100) {
                $alertLevel = 'high';
                $alertMessage = 'Maintenance Required';
            } elseif ($daysSinceLastMaintenance > 60 || $totalTrips > 50) {
                $alertLevel = 'medium';
                $alertMessage = 'Maintenance Due Soon';
            }

            $alerts[] = [
                'vehicle_plate' => $vehicle->no_pol_kendaraan,
                'vehicle_type' => $vehicle->tipe,
                'total_trips' => $totalTrips,
                'days_since_maintenance' => $daysSinceLastMaintenance,
                'alert_level' => $alertLevel,
                'alert_message' => $alertMessage,
            ];
        }

        // Sort by alert level (high first)
        usort($alerts, function ($a, $b) {
            $order = ['high' => 3, 'medium' => 2, 'low' => 1];
            return $order[$b['alert_level']] - $order[$a['alert_level']];
        });

        return array_slice($alerts, 0, 10); // Return top 10
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedDriver(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedVehicle(): void
    {
        $this->dispatch('refresh-charts');
    }
}
