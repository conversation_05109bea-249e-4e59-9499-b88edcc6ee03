<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use App\Models\Pelanggan;
use App\Models\Item;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class MonthlySalesRealizationDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-pie';
    protected static ?string $navigationLabel = 'Realisasi Penjualan Bulanan';
    protected static ?string $title = 'Laporan Realisasi Pesanan Penjualan Bulanan';
    protected static string $view = 'filament.pages.monthly-sales-realization-dashboard';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedMonth = null;

    #[Url(keep: true)]
    public ?string $selectedYear = null;

    #[Url(keep: true)]
    public ?string $selectedCustomer = null;

    #[Url(keep: true)]
    public ?string $selectedProduct = null;

    public array $data = [];

    public static function canAccess(): bool
    {
        return Auth::user()?->can('page_MonthlySalesRealizationDashboard') ?? false;
    }

    public function mount(): void
    {
        // Set defaults to current month/year instead of future dates
        $this->selectedMonth = $this->selectedMonth ?? now()->format('m');
        $this->selectedYear = $this->selectedYear ?? now()->format('Y');

        // Initialize form data
        $this->data = [
            'selectedMonth' => $this->selectedMonth,
            'selectedYear' => $this->selectedYear,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedProduct' => $this->selectedProduct,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedMonth')
                    ->label('Bulan')
                    ->options([
                        '01' => 'Januari',
                        '02' => 'Februari',
                        '03' => 'Maret',
                        '04' => 'April',
                        '05' => 'Mei',
                        '06' => 'Juni',
                        '07' => 'Juli',
                        '08' => 'Agustus',
                        '09' => 'September',
                        '10' => 'Oktober',
                        '11' => 'November',
                        '12' => 'Desember',
                    ])
                    ->default(now()->format('m'))
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedMonth = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedYear')
                    ->label('Tahun')
                    ->options(collect(range(now()->year - 2, now()->year + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->default(now()->format('Y'))
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedYear = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedCustomer = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedProduct')
                    ->label('Jenis Produk (Opsional)')
                    ->options(Item::whereHas('kategori', function ($query) {
                        $query->where('nama', 'like', '%BBM%');
                    })->pluck('name', 'id'))
                    ->searchable()
                    ->placeholder('Semua Produk')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedProduct = $state;
                        $this->dispatch('refresh-charts');
                    }),
            ])
            ->columns(4)
            ->statePath('data');
    }

    public function getSalesOrdersData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $query = TransaksiPenjualan::query()
            ->with(['pelanggan', 'penjualanDetails.item'])
            ->whereBetween('tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomer) {
            $query->where('id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedProduct) {
            $query->whereHas('penjualanDetails', function ($q) {
                $q->where('id_item', $this->selectedProduct);
            });
        }

        return $query->get()->toArray();
    }

    public function getDeliveryRealizationData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $query = DeliveryOrder::query()
            ->with(['transaksi.pelanggan', 'transaksi.penjualanDetails.item'])
            ->whereBetween('tanggal_delivery', [$startDate, $endDate]);

        if ($this->selectedCustomer) {
            $query->whereHas('transaksi', function ($q) {
                $q->where('id_pelanggan', $this->selectedCustomer);
            });
        }

        return $query->get()->toArray();
    }

    public function getKpiData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        // Base query for Sales Orders with filters
        $soQuery = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomer) {
            $soQuery->where('id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedProduct) {
            $soQuery->whereHas('penjualanDetails', function ($q) {
                $q->where('id_item', $this->selectedProduct);
            });
        }

        $totalSO = $soQuery->count();

        // Base query for Delivery Orders with filters
        // PERBAIKAN: Hitung delivery berdasarkan transaksi dalam periode, bukan tanggal delivery
        $doQuery = DB::table('transaksi_penjualan')
            ->leftJoin('delivery_order', 'transaksi_penjualan.id', '=', 'delivery_order.id_transaksi')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomer) {
            $doQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedProduct) {
            $doQuery->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('penjualan_detail')
                    ->whereColumn('penjualan_detail.id_transaksi_penjualan', 'transaksi_penjualan.id')
                    ->where('penjualan_detail.id_item', $this->selectedProduct);
            });
        }

        // Completed Deliveries - berdasarkan transaksi dalam periode yang sudah selesai dikirim
        $completedDeliveries = (clone $doQuery)->where('delivery_order.status_muat', 'selesai')->count();

        // Pending Deliveries - transaksi yang belum selesai dikirim
        $pendingDeliveries = (clone $doQuery)->where(function ($q) {
            $q->whereIn('delivery_order.status_muat', ['pending', 'muat'])
                ->orWhereNull('delivery_order.id');
        })->count();

        // Total Volume with filters
        $volumeQuery = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomer) {
            $volumeQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedProduct) {
            $volumeQuery->where('penjualan_detail.id_item', $this->selectedProduct);
        }

        $totalVolume = $volumeQuery->sum('penjualan_detail.volume_item');

        // Delivered Volume with filters
        // PERBAIKAN: Hitung volume berdasarkan transaksi dalam periode yang sudah selesai dikirim
        $deliveredVolumeQuery = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->join('delivery_order', 'transaksi_penjualan.id', '=', 'delivery_order.id_transaksi')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->where('delivery_order.status_muat', 'selesai');

        if ($this->selectedCustomer) {
            $deliveredVolumeQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedProduct) {
            $deliveredVolumeQuery->where('penjualan_detail.id_item', $this->selectedProduct);
        }

        $deliveredVolume = $deliveredVolumeQuery->sum('penjualan_detail.volume_item');

        // Completion Rate
        $completionRate = $totalSO > 0 ? round(($completedDeliveries / $totalSO) * 100, 1) : 0;

        // Volume Realization Rate
        $volumeRealizationRate = $totalVolume > 0 ? round(($deliveredVolume / $totalVolume) * 100, 1) : 0;

        return [
            'total_so' => $totalSO,
            'completed_deliveries' => $completedDeliveries,
            'pending_deliveries' => $pendingDeliveries,
            'total_volume' => $totalVolume,
            'delivered_volume' => $deliveredVolume,
            'completion_rate' => $completionRate,
            'volume_realization_rate' => $volumeRealizationRate,
        ];
    }

    public function getCustomerPerformanceData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $query = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->leftJoin('delivery_order', function ($join) {
                $join->on('transaksi_penjualan.id', '=', 'delivery_order.id_transaksi');
            })
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply customer filter
        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        // Apply product filter
        if ($this->selectedProduct) {
            $query->whereExists(function ($subQuery) {
                $subQuery->select(DB::raw(1))
                    ->from('penjualan_detail')
                    ->whereColumn('penjualan_detail.id_transaksi_penjualan', 'transaksi_penjualan.id')
                    ->where('penjualan_detail.id_item', $this->selectedProduct);
            });
        }

        return $query->select([
            'pelanggan.nama as customer_name',
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('COUNT(DISTINCT CASE WHEN delivery_order.status_muat = "selesai" THEN delivery_order.id END) as completed_orders'),
            DB::raw('ROUND((COUNT(DISTINCT CASE WHEN delivery_order.status_muat = "selesai" THEN delivery_order.id END) / COUNT(DISTINCT transaksi_penjualan.id)) * 100, 1) as completion_rate')
        ])
            ->groupBy('pelanggan.id', 'pelanggan.nama')
            ->orderBy('total_orders', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function getDailyTrendData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $dailyData = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            // Orders query with filters
            $ordersQuery = TransaksiPenjualan::whereDate('tanggal', $current);

            if ($this->selectedCustomer) {
                $ordersQuery->where('id_pelanggan', $this->selectedCustomer);
            }

            if ($this->selectedProduct) {
                $ordersQuery->whereHas('penjualanDetails', function ($q) {
                    $q->where('id_item', $this->selectedProduct);
                });
            }

            $dayOrders = $ordersQuery->count();

            // Deliveries query with filters
            $deliveriesQuery = DB::table('delivery_order')
                ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
                ->whereDate('delivery_order.tanggal_delivery', $current)
                ->where('delivery_order.status_muat', 'selesai');

            if ($this->selectedCustomer) {
                $deliveriesQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
            }

            if ($this->selectedProduct) {
                $deliveriesQuery->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('penjualan_detail')
                        ->whereColumn('penjualan_detail.id_transaksi_penjualan', 'transaksi_penjualan.id')
                        ->where('penjualan_detail.id_item', $this->selectedProduct);
                });
            }

            $dayDeliveries = $deliveriesQuery->count();

            $dailyData[] = [
                'date' => $current->format('Y-m-d'),
                'day' => $current->format('d'),
                'orders' => $dayOrders,
                'deliveries' => $dayDeliveries,
            ];

            $current->addDay();
        }

        return $dailyData;
    }

    // Method to refresh data when filters change
    public function refreshData()
    {
        $this->dispatch('refresh-charts');
    }
}
