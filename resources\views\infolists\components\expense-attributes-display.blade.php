@php
    $record = $getRecord();
    $attributes = $record->attributes ?? [];
@endphp

@if(!empty($attributes))
    <div class="space-y-3">
        @foreach($attributes as $key => $value)
            @if(!empty($value))
                <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                    <div class="font-medium text-gray-700 dark:text-gray-300 min-w-0 sm:w-1/3">
                        {{ ucfirst(str_replace('_', ' ', $key)) }}:
                    </div>
                    <div class="text-gray-900 dark:text-gray-100 flex-1">
                        @if(is_array($value))
                            <ul class="list-disc list-inside space-y-1">
                                @foreach($value as $item)
                                    <li>{{ $item }}</li>
                                @endforeach
                            </ul>
                        @else
                            {{ $value }}
                        @endif
                    </div>
                </div>
            @endif
        @endforeach
    </div>
@else
    <div class="text-gray-500 dark:text-gray-400 italic">
        Tidak ada data pengeluaran tambahan.
    </div>
@endif
