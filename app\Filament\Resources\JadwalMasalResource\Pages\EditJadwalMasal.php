<?php

namespace App\Filament\Resources\JadwalMasalResource\Pages;

use App\Filament\Resources\JadwalMasalResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditJadwalMasal extends EditRecord
{
    protected static string $resource = JadwalMasalResource::class;

    protected array $karyawanData = [];

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing karyawan relationships
        $data['karyawan'] = $this->record->karyawan()->pluck('users.id')->toArray();

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Store karyawan data temporarily for afterSave
        $this->karyawanData = $data['karyawan'] ?? [];

        // Remove karyawan from main data as it's not a direct field
        unset($data['karyawan']);
        unset($data['entitas_filter']); // Remove filter field too

        return $data;
    }

    protected function afterSave(): void
    {
        // Sync selected karyawan to the jadwal masal
        $this->record->karyawan()->sync($this->karyawanData);

        Notification::make()
            ->title('Jadwal masal berhasil diupdate')
            ->body('Jadwal masal dengan ' . count($this->karyawanData) . ' karyawan berhasil diupdate.')
            ->success()
            ->send();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('generate')
                ->label('Generate Jadwal')
                ->icon('heroicon-o-play-circle')
                ->color('success')
                ->visible(fn() => !$this->record->isGenerated())
                ->requiresConfirmation()
                ->modalHeading('Generate Jadwal Individual')
                ->modalDescription('Apakah Anda yakin ingin membuat jadwal individual untuk semua karyawan dalam periode ini? Setelah di-generate, jadwal masal ini tidak bisa di-generate ulang.')
                ->modalSubmitActionLabel('Ya, Generate')
                ->action(function () {
                    try {
                        $result = $this->record->generateSchedules();

                        Notification::make()
                            ->title('Jadwal berhasil digenerate!')
                            ->body("✅ {$result['generated']} jadwal baru dibuat\n⚠️ {$result['skipped']} jadwal sudah ada\n📅 {$result['total_days']} hari untuk {$result['total_employees']} karyawan")
                            ->success()
                            ->duration(8000)
                            ->send();

                        // Refresh the page to update the UI
                        $this->redirect($this->getResource()::getUrl('edit', ['record' => $this->record]));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Error saat generate jadwal')
                            ->body('Error: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
