<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jadwal_kerja', function (Blueprint $table) {
            $table->dropForeign(['karyawan_id']);
            $table->foreign('karyawan_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_kerja', function (Blueprint $table) {
            $table->dropForeign(['entitas_id', 'karyawan_id']);

            $table->foreign('karyawan_id')->references('id')->on('karyawan')->onDelete('cascade');
            $table->dropColumn(['entitas_id']);
        });
    }
};
