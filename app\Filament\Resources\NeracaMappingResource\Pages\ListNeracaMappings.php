<?php

namespace App\Filament\Resources\NeracaMappingResource\Pages;

use App\Filament\Resources\NeracaMappingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNeracaMappings extends ListRecords
{
    protected static string $resource = NeracaMappingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
