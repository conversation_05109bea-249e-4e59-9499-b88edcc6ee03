<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UangJalan;
use App\Models\DeliveryOrder;
use App\Models\User;

class UangJalanBreakdownSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $user = User::first();
        $deliveryOrders = DeliveryOrder::take(3)->get();

        if ($deliveryOrders->isEmpty()) {
            $this->command->info('No DeliveryOrder records found. Please run DeliveryOrderSeeder first.');
            return;
        }

        // Test Case 1: Breakdown lengkap
        $do1 = $deliveryOrders->first();
        UangJalan::updateOrCreate(
            ['id_do' => $do1->id],
            [
                'uang_depot' => 200000,
                'uang_jalan_amount' => 300000,
                'uang_bongkar' => 150000,
                'uang_pas' => 50000,
                'uang_lembur' => 100000,
                'uang_bbm' => 250000,
                'uang_tol' => 75000,
                'keterangan_depot' => 'Biaya depot untuk loading BBM',
                'keterangan_jalan' => 'Uang jalan perjalanan Jakarta-Surabaya',
                'keterangan_bongkar' => 'Biaya bongkar di SPBU tujuan',
                'keterangan_pas' => 'Uang pas tol dan parkir',
                'keterangan_lembur' => 'Lembur karena perjalanan malam',
                'keterangan_bbm' => 'BBM untuk perjalanan pulang pergi',
                'keterangan_tol' => 'Biaya tol Jakarta-Surabaya PP',
                'status_kirim' => 'kirim',
                'status_terima' => 'terima',
                'id_user' => $user->id,
                'created_by' => $user->id,
            ]
        );

        // Test Case 2: Breakdown sebagian
        if ($deliveryOrders->count() > 1) {
            $do2 = $deliveryOrders->skip(1)->first();
            UangJalan::updateOrCreate(
                ['id_do' => $do2->id],
                [
                    'uang_depot' => 150000,
                    'uang_jalan_amount' => 400000,
                    'uang_bongkar' => 0,
                    'uang_pas' => 30000,
                    'uang_lembur' => 0,
                    'uang_bbm' => 200000,
                    'uang_tol' => 60000,
                    'keterangan_depot' => 'Biaya depot standar',
                    'keterangan_jalan' => 'Uang jalan perjalanan lokal',
                    'keterangan_pas' => 'Parkir dan administrasi',
                    'keterangan_bbm' => 'BBM perjalanan lokal',
                    'keterangan_tol' => 'Tol dalam kota',
                    'status_kirim' => 'pending',
                    'status_terima' => 'pending',
                    'id_user' => $user->id,
                    'created_by' => $user->id,
                ]
            );
        }

        // Test Case 3: Hanya beberapa komponen
        if ($deliveryOrders->count() > 2) {
            $do3 = $deliveryOrders->skip(2)->first();
            UangJalan::updateOrCreate(
                ['id_do' => $do3->id],
                [
                    'uang_depot' => 0,
                    'uang_jalan_amount' => 500000,
                    'uang_bongkar' => 100000,
                    'uang_pas' => 0,
                    'uang_lembur' => 200000,
                    'uang_bbm' => 0,
                    'uang_tol' => 0,
                    'keterangan_jalan' => 'Uang jalan perjalanan jauh',
                    'keterangan_bongkar' => 'Bongkar manual di lokasi terpencil',
                    'keterangan_lembur' => 'Lembur karena kondisi jalan sulit',
                    'status_kirim' => 'kirim',
                    'status_terima' => 'pending',
                    'id_user' => $user->id,
                    'created_by' => $user->id,
                ]
            );
        }

        $this->command->info('UangJalanBreakdown seeder completed successfully!');
        $this->command->info('Created breakdown data for ' . min(3, $deliveryOrders->count()) . ' delivery orders:');
        $this->command->info('- DO 1: Full breakdown (Total: Rp 1,125,000)');
        $this->command->info('- DO 2: Partial breakdown (Total: Rp 840,000)');
        $this->command->info('- DO 3: Selected components (Total: Rp 800,000)');
    }
}
