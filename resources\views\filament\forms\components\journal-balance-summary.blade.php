@php
    $entries = $getState() ?? [];
    $totalDebit = 0;
    $totalCredit = 0;

    // Get journal entries from the form state
    $formData = $this->form->getState();
    $journalEntries = $formData['journalEntries'] ?? [];

    foreach ($journalEntries as $entry) {
        $totalDebit += (float) ($entry['debit'] ?? 0);
        $totalCredit += (float) ($entry['credit'] ?? 0);
    }

    $difference = $totalDebit - $totalCredit;
    $isBalanced = abs($difference) < 0.01;

    $debitFormatted = 'Rp ' . number_format($totalDebit, 0, ',', '.');
    $creditFormatted = 'Rp ' . number_format($totalCredit, 0, ',', '.');
    $differenceFormatted = 'Rp ' . number_format(abs($difference), 0, ',', '.');

    $status = $isBalanced ? '✅ Journal Balance' : '❌ Journal Tidak Balance';
    $statusColor = $isBalanced ? 'text-green-600' : 'text-red-600';
@endphp

<div class="grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
    <div class="text-center">
        <div class="text-sm text-gray-500 dark:text-gray-400 font-medium">Total Debit</div>
        <div class="text-lg font-semibold text-green-600 mt-1">{{ $debitFormatted }}</div>
    </div>
    
    <div class="text-center">
        <div class="text-sm text-gray-500 dark:text-gray-400 font-medium">Total Kredit</div>
        <div class="text-lg font-semibold text-red-600 mt-1">{{ $creditFormatted }}</div>
    </div>
    
    <div class="text-center">
        <div class="text-sm text-gray-500 dark:text-gray-400 font-medium">Status</div>
        <div class="text-lg font-semibold {{ $statusColor }} mt-1">{{ $status }}</div>
        @if(!$isBalanced)
            <div class="text-sm text-orange-600 mt-1">Selisih: {{ $differenceFormatted }}</div>
        @endif
    </div>
</div>

@if(!$isBalanced)
    <div class="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span class="text-sm text-orange-700 dark:text-orange-300">
                Journal tidak balance. Pastikan total debit sama dengan total kredit.
            </span>
        </div>
    </div>
@endif
