<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Remove unused unit_price and total_amount columns from delivery_order_details table
     */
    public function up(): void
    {
        Schema::table('delivery_order_details', function (Blueprint $table) {
            $table->dropColumn(['unit_price', 'total_amount']);
        });
    }

    /**
     * Reverse the migrations.
     * Add back the columns if rollback is needed
     */
    public function down(): void
    {
        Schema::table('delivery_order_details', function (Blueprint $table) {
            $table->decimal('unit_price', 15, 2)->default(0)->after('unit');
            $table->decimal('total_amount', 15, 2)->default(0)->after('unit_price');
        });
    }
};
