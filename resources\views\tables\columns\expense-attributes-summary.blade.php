@php
    $record = $getRecord();
    $attributes = $record->attributes ?? [];
    $category = $record->category;

    // Debug: pastikan data ada
    if (empty($attributes)) {
        $attributes = [];
    }

    // Jika attributes adalah string JSON, decode
    if (is_string($attributes)) {
        $attributes = json_decode($attributes, true) ?? [];
    }
@endphp

<div class="text-xs space-y-1">
    @if (!empty($attributes))
        @switch($category)
            @case('ganti_oli')
                @if (isset($attributes['jenis_oli']))
                    <div><span class="font-medium text-gray-600">Oli:</span> {{ $attributes['jenis_oli'] }}</div>
                @endif
                @if (isset($attributes['kilometer']))
                    <div><span class="font-medium text-gray-600">KM:</span> {{ number_format($attributes['kilometer']) }}</div>
                @endif
                @if (isset($attributes['bengkel']))
                    <div><span class="font-medium text-gray-600">Bengkel:</span> {{ $attributes['bengkel'] }}</div>
                @endif
            @break

            @case('vehicle_fuel')
                @if (isset($attributes['jenis_bbm']))
                    <div><span class="font-medium text-gray-600">BBM:</span> {{ $attributes['jenis_bbm'] }}</div>
                @endif
                @if (isset($attributes['liter']))
                    <div><span class="font-medium text-gray-600">Liter:</span> {{ number_format($attributes['liter']) }}L</div>
                @endif
                @if (isset($attributes['spbu']))
                    <div><span class="font-medium text-gray-600">SPBU:</span> {{ $attributes['spbu'] }}</div>
                @endif
            @break

            @case('delivery_reimbursement')
                @if (isset($attributes['tujuan_delivery']))
                    <div><span class="font-medium text-gray-600">Tujuan:</span> {{ $attributes['tujuan_delivery'] }}</div>
                @endif
                @if (isset($attributes['jarak_tempuh']))
                    <div><span class="font-medium text-gray-600">Jarak:</span> {{ number_format($attributes['jarak_tempuh']) }}
                        km</div>
                @endif
                @if (isset($attributes['no_delivery_order']))
                    <div><span class="font-medium text-gray-600">DO:</span> {{ $attributes['no_delivery_order'] }}</div>
                @endif
            @break

            @case('uang_jalan_sc')
                @if (isset($attributes['nama_supir']))
                    <div><span class="font-medium text-gray-600">Supir:</span> {{ $attributes['nama_supir'] }}</div>
                @endif
                @if (isset($attributes['rute_perjalanan']))
                    <div><span class="font-medium text-gray-600">Rute:</span> {{ $attributes['rute_perjalanan'] }}</div>
                @endif
                @if (isset($attributes['total_uang_jalan']))
                    <div><span class="font-medium text-gray-600">Total:</span> Rp
                        {{ number_format($attributes['total_uang_jalan']) }}</div>
                @endif
            @break

            @default
                @php
                    $count = 0;
                    $maxDisplay = 3;
                @endphp
                @foreach ($attributes as $key => $value)
                    @if ($count < $maxDisplay && !empty($value))
                        <div><span class="font-medium text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                            {{ is_array($value) ? implode(', ', $value) : $value }}</div>
                        @php $count++; @endphp
                    @endif
                @endforeach
                @if (count($attributes) > $maxDisplay)
                    <div class="text-gray-500 italic">+{{ count($attributes) - $maxDisplay }} lainnya</div>
                @endif
        @endswitch
    @else
        <div class="text-gray-400 italic">Tidak ada data tambahan</div>
    @endif
</div>
