<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Models\Jabatan;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\User;

class ProfilePage extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    protected static ?string $navigationLabel = 'Profile Saya';
    protected static ?string $title = 'Profile Saya';
    protected static string $view = 'filament.pages.profile-page';
    protected static ?string $slug = 'profile';
    protected static ?int $navigationSort = 99;
    protected static ?string $navigationGroup = 'Pengaturan';

    public ?array $data = [];

    public static function canAccess(): bool
    {
        return true;
    }

    public function mount(): void
    {
        $user = Auth::user();
        $this->form->fill([
            'name' => $user->name,
            'email' => $user->email,
            'hp' => $user->hp,
            'no_induk' => $user->no_induk,
            'id_jabatan' => $user->id_jabatan,
            'id_divisi' => $user->id_divisi,
            'id_entitas' => $user->id_entitas,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Foto Profil')
                    ->description('Unggah atau ubah foto profil Anda')
                    ->icon('heroicon-o-camera')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('avatar')
                            ->label('Foto Profil')
                            ->collection('avatar')
                            ->image()
                            ->imageEditor()
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('300')
                            ->imageResizeTargetHeight('300')
                            ->helperText('Unggah foto profil (disarankan: gambar persegi, maksimal 2MB)')
                            ->maxSize(2048)
                            ->columnSpanFull(),
                    ])
                    ->columnSpanFull(),

                Forms\Components\Section::make('Informasi Pribadi')
                    ->description('Perbarui informasi pribadi Anda')
                    ->icon('heroicon-o-identification')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Nama Lengkap')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                Forms\Components\TextInput::make('hp')
                                    ->label('Nomor Telepon')
                                    ->tel()
                                    ->maxLength(100)
                                    ->placeholder('contoh: +62 81234567890')
                                    ->columnSpan(1),

                                Forms\Components\TextInput::make('no_induk')
                                    ->label('ID Karyawan')
                                    ->maxLength(100)
                                    ->placeholder('contoh: EMP001')
                                    ->columnSpan(1),
                            ]),
                    ])
                    ->columnSpanFull(),

                Forms\Components\Section::make('Informasi Organisasi')
                    ->description('Informasi jabatan dan divisi (hanya bisa diubah oleh admin)')
                    ->icon('heroicon-o-building-office')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('id_jabatan')
                                    ->label('Jabatan')
                                    ->options(Jabatan::pluck('nama', 'id'))
                                    ->searchable()
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->columnSpan(1),

                                Forms\Components\Select::make('id_divisi')
                                    ->label('Divisi')
                                    ->options(Divisi::pluck('nama', 'id'))
                                    ->searchable()
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->columnSpan(1),

                                Forms\Components\Select::make('id_entitas')
                                    ->label('Entitas')
                                    ->options(Entitas::pluck('nama', 'id'))
                                    ->searchable()
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->columnSpan(1),
                            ]),
                    ])
                    ->columnSpanFull(),

                Forms\Components\Section::make('Ubah Password')
                    ->description('Kosongkan jika tidak ingin mengubah password')
                    ->icon('heroicon-o-lock-closed')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('current_password')
                                    ->label('Password Saat Ini')
                                    ->password()
                                    ->revealable()
                                    ->requiredWith('password')
                                    ->rules([
                                        function () {
                                            return function (string $attribute, $value, \Closure $fail) {
                                                if (!empty($value) && !Hash::check($value, Auth::user()->password)) {
                                                    $fail('Password saat ini tidak sesuai.');
                                                }
                                            };
                                        },
                                    ])
                                    ->columnSpan(2),

                                Forms\Components\TextInput::make('password')
                                    ->label('Password Baru')
                                    ->password()
                                    ->revealable()
                                    ->rule(Password::default())
                                    ->same('password_confirmation')
                                    ->columnSpan(1),

                                Forms\Components\TextInput::make('password_confirmation')
                                    ->label('Konfirmasi Password Baru')
                                    ->password()
                                    ->revealable()
                                    ->requiredWith('password')
                                    ->columnSpan(1),
                            ]),
                    ])
                    ->columnSpanFull(),
            ])
            ->statePath('data')
            ->model(Auth::user());
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $userId = Auth::id();

        // Prepare update data
        $updateData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'hp' => $data['hp'],
            'no_induk' => $data['no_induk'],
        ];

        // Add password if provided
        if (!empty($data['password'])) {
            $updateData['password'] = Hash::make($data['password']);
        }

        // Update user using User model
        User::where('id', $userId)->update($updateData);

        Notification::make()
            ->title('Profile berhasil diperbarui')
            ->body('Informasi profile Anda telah berhasil disimpan.')
            ->success()
            ->send();

        // Clear password fields and refresh form with updated data
        $updatedUser = User::find($userId);
        $this->form->fill([
            'name' => $updatedUser->name,
            'email' => $updatedUser->email,
            'hp' => $updatedUser->hp,
            'no_induk' => $updatedUser->no_induk,
            'id_jabatan' => $updatedUser->id_jabatan,
            'id_divisi' => $updatedUser->id_divisi,
            'id_entitas' => $updatedUser->id_entitas,
            'current_password' => '',
            'password' => '',
            'password_confirmation' => '',
        ]);
    }
}
