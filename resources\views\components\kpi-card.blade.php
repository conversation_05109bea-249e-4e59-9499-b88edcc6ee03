@props([
    'title',
    'value',
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'gradient' => false,
    'gradientFrom' => 'blue-500',
    'gradientTo' => 'blue-600',
    'change' => null,
    'changeType' => 'neutral' // positive, negative, neutral
])

@php
    $cardClasses = $gradient 
        ? "bg-gradient-to-r from-{$gradientFrom} to-{$gradientTo} kpi-card-gradient"
        : 'kpi-card';
    
    $iconColorClass = $gradient ? 'text-white' : "icon-{$iconColor}";
    
    $titleClass = $gradient 
        ? 'text-sm font-medium text-white/90 truncate'
        : 'kpi-card-title';
    
    $valueClass = $gradient 
        ? 'text-2xl font-bold text-white'
        : 'kpi-card-value';
    
    $subtitleClass = $gradient 
        ? 'text-sm text-white/80'
        : 'kpi-card-subtitle';
@endphp

<div class="{{ $cardClasses }}">
    <div class="kpi-card-content">
        <div class="flex items-center">
            @if($icon)
                <div class="kpi-card-icon">
                    <x-dynamic-component :component="$icon" class="h-8 w-8 {{ $iconColorClass }}" />
                </div>
            @endif
            <div class="kpi-card-body">
                <dl>
                    <dt class="{{ $titleClass }}">
                        {{ $title }}
                    </dt>
                    <dd class="{{ $valueClass }}">
                        {{ $value }}
                    </dd>
                    @if($subtitle || $change)
                        <dd class="{{ $subtitleClass }}">
                            @if($change)
                                @php
                                    $changeColor = match($changeType) {
                                        'positive' => $gradient ? 'text-green-200' : 'text-green-600 dark:text-green-400',
                                        'negative' => $gradient ? 'text-red-200' : 'text-red-600 dark:text-red-400',
                                        default => $gradient ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'
                                    };
                                    
                                    $changeIcon = match($changeType) {
                                        'positive' => '↗',
                                        'negative' => '↘',
                                        default => ''
                                    };
                                @endphp
                                <span class="{{ $changeColor }}">
                                    {{ $changeIcon }} {{ $change }}
                                </span>
                                @if($subtitle)
                                    <span class="ml-1">{{ $subtitle }}</span>
                                @endif
                            @else
                                {{ $subtitle }}
                            @endif
                        </dd>
                    @endif
                </dl>
            </div>
        </div>
    </div>
</div>
