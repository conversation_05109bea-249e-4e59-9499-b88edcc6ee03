<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LabaRugiMappingResource\Pages;
use App\Filament\Resources\LabaRugiMappingResource\RelationManagers;
use App\Models\LabaRugiMapping;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;

class LabaRugiMappingResource extends Resource
{
    protected static ?string $model = LabaRugiMapping::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Mapping Laba Rugi';

    protected static ?string $modelLabel = 'Mapping Laba Rugi';

    protected static ?string $pluralModelLabel = 'Mapping Laba Rugi';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Akun')
                    ->schema([
                        Forms\Components\Select::make('kode_akun')
                            ->label('Kode Akun')
                            ->options(function ($record) {
                                // Get already mapped account codes, excluding current record if editing
                                $mappedCodes = LabaRugiMapping::when($record, function ($query) use ($record) {
                                    return $query->where('id', '!=', $record->id);
                                })->pluck('kode_akun')->toArray();

                                // Get available accounts (not yet mapped)
                                return Akun::whereNotIn('kode_akun', $mappedCodes)
                                    ->get()
                                    ->pluck('nama_akun', 'kode_akun')
                                    ->map(fn($nama, $kode) => "$kode - $nama");
                            })
                            ->searchable()
                            ->required()
                            ->live()
                            ->helperText('Hanya menampilkan akun yang belum di-mapping')
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $akun = Akun::where('kode_akun', $state)->first();
                                    if ($akun) {
                                        $set('nama_akun', $akun->nama_akun);
                                    }
                                }
                            }),

                        Forms\Components\TextInput::make('nama_akun')
                            ->label('Nama Akun')
                            ->required()
                            ->disabled()
                            ->dehydrated(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Kategori Laba Rugi')
                    ->schema([
                        Forms\Components\Select::make('kategori_laba_rugi')
                            ->label('Kategori Laba Rugi')
                            ->options([
                                // PENDAPATAN
                                'Pendapatan Penjualan BBM' => 'Pendapatan Penjualan BBM',
                                'Pendapatan Jasa Angkut' => 'Pendapatan Jasa Angkut',

                                // HPP
                                'HPP BBM' => 'HPP BBM',
                                'HPP Jasa Angkut' => 'HPP Jasa Angkut',
                                'Persediaan' => 'Persediaan',

                                // BEBAN OPERASIONAL
                                'Gaji Karyawan' => 'Gaji Karyawan',
                                'Beban Utilitas' => 'Beban Utilitas',
                                'ATK' => 'ATK',
                                'Ekspedisi' => 'Ekspedisi',
                                'Beban Perjalanan Dinas' => 'Beban Perjalanan Dinas',
                                'Beban pengurusan izin/lisensi' => 'Beban pengurusan izin/lisensi',
                                'BBM mobil operasional kantor' => 'BBM mobil operasional kantor',
                                'Beban Lain-lain' => 'Beban Lain-lain',
                                'Beban lain - lain' => 'Beban lain - lain',
                                'Beban lain -lain' => 'Beban lain -lain',
                                'Lain-lain' => 'Lain-lain',

                                // PENDAPATAN LUAR USAHA
                                'Pendapatan Jasa Giro' => 'Pendapatan Jasa Giro',
                                'Pendapatan Bunga Bank' => 'Pendapatan Bunga Bank',
                                'Pendapatan Lain Lain' => 'Pendapatan Lain Lain',

                                // BEBAN LUAR USAHA
                                'Adm Bank' => 'Adm Bank',
                                'Pajak Bank' => 'Pajak Bank',
                                'Bunga Bank' => 'Bunga Bank',

                                // PAJAK
                                'PBB' => 'PBB',
                                'PPN' => 'PPN',
                                'Pajak Pasal 21' => 'Pajak Pasal 21',
                                'Pajak Pasal 22' => 'Pajak Pasal 22',
                                'Pajak Pasal 23' => 'Pajak Pasal 23',
                                'Pajak Pasal 25' => 'Pajak Pasal 25',
                                'Pajak Pasal 29' => 'Pajak Pasal 29',
                                'Pajak Lainnnya' => 'Pajak Lainnnya',
                            ])
                            ->searchable()
                            ->required()
                            ->helperText('Pilih kategori untuk pengelompokan dalam laporan laba rugi'),

                        Forms\Components\TextInput::make('urutan')
                            ->label('Urutan')
                            ->numeric()
                            ->default(0)
                            ->required()
                            ->helperText('Urutan tampil dalam laporan (angka kecil tampil lebih dulu)'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true)
                            ->helperText('Hanya mapping aktif yang akan muncul dalam laporan'),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_akun')
                    ->label('Kode Akun')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('nama_akun')
                    ->label('Nama Akun')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('kategori_laba_rugi')
                    ->label('Kategori Laba Rugi')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        str_contains($state, 'Pendapatan') => 'success',
                        str_contains($state, 'HPP') => 'warning',
                        str_contains($state, 'Beban') => 'danger',
                        str_contains($state, 'Pajak') => 'info',
                        str_contains($state, 'PPN') || str_contains($state, 'PBB') => 'info',
                        default => 'gray',
                    })
                    ->wrap(),

                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('kategori_laba_rugi')
                    ->label('Kategori')
                    ->options([
                        'Pendapatan Penjualan BBM' => 'Pendapatan Penjualan BBM',
                        'HPP BBM' => 'HPP BBM',
                        'HPP Jasa Angkut' => 'HPP Jasa Angkut',
                        'Gaji Karyawan' => 'Gaji Karyawan',
                        'Beban Utilitas' => 'Beban Utilitas',
                        'ATK' => 'ATK',
                        'Ekspedisi' => 'Ekspedisi',
                        'Beban Lain-lain' => 'Beban Lain-lain',
                        'Pendapatan Jasa Giro' => 'Pendapatan Jasa Giro',
                        'Adm Bank' => 'Adm Bank',
                        'PBB' => 'PBB',
                        'PPN' => 'PPN',
                    ])
                    ->multiple(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Aktifkan')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(fn($record) => $record->update(['is_active' => true]));
                        })
                        ->requiresConfirmation(),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Nonaktifkan')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each(fn($record) => $record->update(['is_active' => false]));
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->striped();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLabaRugiMappings::route('/'),
            'create' => Pages\CreateLabaRugiMapping::route('/create'),
            'edit' => Pages\EditLabaRugiMapping::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'success';
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['akun']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['kode_akun', 'nama_akun', 'kategori_laba_rugi'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Kode Akun' => $record->kode_akun,
            'Kategori' => $record->kategori_laba_rugi,
        ];
    }
}
