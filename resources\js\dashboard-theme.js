// Dashboard Theme JavaScript for Enhanced Light/Dark Mode Support

class DashboardTheme {
    constructor() {
        this.init();
    }

    init() {
        this.setupThemeToggle();
        this.setupChartThemes();
        this.setupResponsiveCharts();
        this.setupTableEnhancements();
        this.setupLoadingStates();
    }

    // Theme Toggle Support
    setupThemeToggle() {
        // Listen for theme changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    this.updateChartThemes();
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // Chart Theme Configuration
    setupChartThemes() {
        if (typeof Chart !== 'undefined') {
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
            
            // Set default colors based on theme
            this.updateChartDefaults();
        }
    }

    updateChartDefaults() {
        if (typeof Chart === 'undefined') return;

        const isDark = document.documentElement.classList.contains('dark');
        
        Chart.defaults.color = isDark ? '#E5E7EB' : '#374151';
        Chart.defaults.borderColor = isDark ? '#374151' : '#E5E7EB';
        Chart.defaults.backgroundColor = isDark ? '#1F2937' : '#FFFFFF';

        // Update grid colors
        Chart.defaults.scales.linear.grid.color = isDark ? '#374151' : '#E5E7EB';
        Chart.defaults.scales.category.grid.color = isDark ? '#374151' : '#E5E7EB';
    }

    updateChartThemes() {
        this.updateChartDefaults();
        
        // Refresh all existing charts
        Object.values(Chart.instances).forEach(chart => {
            if (chart) {
                chart.update('none');
            }
        });
    }

    // Responsive Chart Setup
    setupResponsiveCharts() {
        const resizeObserver = new ResizeObserver(entries => {
            entries.forEach(entry => {
                const canvas = entry.target.querySelector('canvas');
                if (canvas && canvas.chart) {
                    canvas.chart.resize();
                }
            });
        });

        // Observe all chart containers
        document.querySelectorAll('.chart-container').forEach(container => {
            resizeObserver.observe(container);
        });
    }

    // Table Enhancements
    setupTableEnhancements() {
        // Add custom scrollbar to tables
        document.querySelectorAll('.dashboard-table-content').forEach(table => {
            table.classList.add('custom-scrollbar');
        });

        // Add hover effects to table rows
        document.querySelectorAll('.dashboard-table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.classList.add('bg-gray-50', 'dark:bg-gray-700');
            });
            
            row.addEventListener('mouseleave', () => {
                row.classList.remove('bg-gray-50', 'dark:bg-gray-700');
            });
        });
    }

    // Loading States
    setupLoadingStates() {
        // Show loading skeleton while charts are loading
        document.querySelectorAll('canvas').forEach(canvas => {
            const container = canvas.closest('.chart-container');
            if (container) {
                this.showChartLoading(container);
                
                // Hide loading when chart is ready
                canvas.addEventListener('chartjs-render-complete', () => {
                    this.hideChartLoading(container);
                });
            }
        });
    }

    showChartLoading(container) {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
            
            const skeleton = document.createElement('div');
            skeleton.className = 'loading-skeleton h-64 rounded';
            skeleton.setAttribute('data-loading', 'true');
            
            container.appendChild(skeleton);
        }
    }

    hideChartLoading(container) {
        const skeleton = container.querySelector('[data-loading="true"]');
        const canvas = container.querySelector('canvas');
        
        if (skeleton) {
            skeleton.remove();
        }
        
        if (canvas) {
            canvas.style.display = 'block';
        }
    }

    // Utility Methods
    static getThemeColors(isDark = null) {
        if (isDark === null) {
            isDark = document.documentElement.classList.contains('dark');
        }

        return {
            primary: isDark ? '#3B82F6' : '#2563EB',
            secondary: isDark ? '#10B981' : '#059669',
            success: isDark ? '#10B981' : '#059669',
            warning: isDark ? '#F59E0B' : '#D97706',
            danger: isDark ? '#EF4444' : '#DC2626',
            info: isDark ? '#06B6D4' : '#0891B2',
            light: isDark ? '#F3F4F6' : '#F9FAFB',
            dark: isDark ? '#1F2937' : '#111827',
            text: isDark ? '#E5E7EB' : '#374151',
            textMuted: isDark ? '#9CA3AF' : '#6B7280',
            border: isDark ? '#374151' : '#E5E7EB',
            background: isDark ? '#1F2937' : '#FFFFFF'
        };
    }

    static formatNumber(number, options = {}) {
        const defaults = {
            style: 'decimal',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        };

        const config = { ...defaults, ...options };
        
        return new Intl.NumberFormat('id-ID', config).format(number);
    }

    static formatCurrency(amount, currency = 'IDR') {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    static formatPercentage(value, decimals = 1) {
        return new Intl.NumberFormat('id-ID', {
            style: 'percent',
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(value / 100);
    }

    // Chart Helper Methods
    static createGradient(ctx, color1, color2) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    }

    static getChartOptions(type = 'line', options = {}) {
        const colors = this.getThemeColors();
        
        const baseOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: colors.text,
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: colors.background,
                    titleColor: colors.text,
                    bodyColor: colors.text,
                    borderColor: colors.border,
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    grid: {
                        color: colors.border
                    },
                    ticks: {
                        color: colors.textMuted
                    }
                },
                y: {
                    grid: {
                        color: colors.border
                    },
                    ticks: {
                        color: colors.textMuted
                    }
                }
            }
        };

        return { ...baseOptions, ...options };
    }
}

// Initialize dashboard theme when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardTheme = new DashboardTheme();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardTheme;
}
