<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DeliveryOrder;
use App\Models\DeliveryOrderDetail;
use App\Models\PenjualanDetail;

class CreateDeliveryOrderDetailsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Creating delivery order details for existing DOs...');

        $deliveryOrders = DeliveryOrder::with('transaksi.penjualanDetails.item')->get();
        $created = 0;

        foreach ($deliveryOrders as $do) {
            // Skip if already has details
            if ($do->details()->exists()) {
                continue;
            }

            $transaksi = $do->transaksi;
            if (!$transaksi || !$transaksi->penjualanDetails->count()) {
                continue;
            }

            // Create details based on penjualan details
            foreach ($transaksi->penjualanDetails as $penjualanDetail) {
                // Calculate proportional volume based on DO volume vs total transaction volume
                $totalTransaksiVolume = $transaksi->penjualanDetails->sum('volume_item');
                $itemProportion = $totalTransaksiVolume > 0 ? ($penjualanDetail->volume_item / $totalTransaksiVolume) : 0;
                $volumeDelivered = $do->volume_do * $itemProportion;

                DeliveryOrderDetail::create([
                    'id_delivery_order' => $do->id,
                    'id_penjualan_detail' => $penjualanDetail->id,
                    'id_item' => $penjualanDetail->id_item,
                    'item_name' => $penjualanDetail->item->name ?? 'Unknown Item',
                    'item_description' => $penjualanDetail->item->description,
                    'volume_ordered' => $penjualanDetail->volume_item,
                    'volume_delivered' => $volumeDelivered,
                    'unit' => $penjualanDetail->item->satuan->nama ?? 'Liter',
                    'unit_price' => $penjualanDetail->harga_jual,
                    'total_amount' => $volumeDelivered * $penjualanDetail->harga_jual,
                    'notes' => 'Auto-generated from existing DO',
                ]);

                $created++;
            }
        }

        $this->command->info("Created {$created} delivery order details");
    }
}
