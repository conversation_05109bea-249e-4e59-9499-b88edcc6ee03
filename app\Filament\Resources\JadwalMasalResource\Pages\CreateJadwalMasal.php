<?php

namespace App\Filament\Resources\JadwalMasalResource\Pages;

use App\Filament\Resources\JadwalMasalResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;

class CreateJadwalMasal extends CreateRecord
{
    protected static string $resource = JadwalMasalResource::class;

    protected array $karyawanData = [];

    protected function beforeCreate(): void
    {
        // Validate karyawan selection
        $karyawanData = $this->data['karyawan'] ?? '[]';
        if (is_string($karyawanData)) {
            $karyawanData = json_decode($karyawanData, true) ?? [];
        }

        if (empty($karyawanData)) {
            Notification::make()
                ->title('Validasi Error')
                ->body('Pilih minimal satu karyawan untuk jadwal masal.')
                ->danger()
                ->send();

            throw new \Exception('Pilih minimal satu karyawan untuk jadwal masal.');
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();

        $karyawanData = $data['karyawan'] ?? [];
        if (is_string($karyawanData)) {
            $karyawanData = json_decode($karyawanData, true) ?? [];
        }

        $this->karyawanData = is_array($karyawanData) ? $karyawanData : [];

        unset($data['karyawan']);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Attach selected karyawan to the jadwal masal
        if (!empty($this->karyawanData)) {
            $this->record->karyawan()->attach($this->karyawanData);

            Notification::make()
                ->title('Jadwal masal berhasil dibuat')
                ->body('Jadwal masal dengan ' . count($this->karyawanData) . ' karyawan berhasil dibuat. Klik "Generate Jadwal" untuk membuat jadwal individual.')
                ->success()
                ->duration(8000)
                ->send();
        } else {
            Notification::make()
                ->title('Jadwal masal dibuat tanpa karyawan')
                ->body('Jadwal masal berhasil dibuat, tapi tidak ada karyawan yang dipilih. Silakan edit jadwal untuk menambahkan karyawan.')
                ->warning()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
