<?php

namespace App\Filament\Pages;

use App\Models\Pelanggan;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class MonthlyDeliveryReportDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static ?string $navigationLabel = 'Laporan Pengiriman Bulanan';
    protected static ?string $title = '<PERSON><PERSON><PERSON>';
    protected static string $view = 'filament.pages.monthly-delivery-report-dashboard';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedMonth = null;

    #[Url(keep: true)]
    public ?string $selectedYear = null;

    #[Url(keep: true)]
    public ?string $selectedCustomer = null;

    #[Url(keep: true)]
    public ?string $selectedTbbm = null;

    #[Url(keep: true)]
    public ?string $selectedStatus = null;

    public array $data = [];

    public static function canAccess(): bool
    {
        return Auth::user()?->can('page_MonthlyDeliveryReportDashboard') ?? false;
    }

    public function mount(): void
    {
        // Set defaults to current month/year
        $this->selectedMonth = $this->selectedMonth ?? now()->format('m');
        $this->selectedYear = $this->selectedYear ?? now()->format('Y');

        // Initialize form data
        $this->data = [
            'selectedMonth' => $this->selectedMonth,
            'selectedYear' => $this->selectedYear,
            'selectedCustomer' => $this->selectedCustomer,
            'selectedTbbm' => $this->selectedTbbm,
            'selectedStatus' => $this->selectedStatus,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedMonth')
                    ->label('Bulan')
                    ->options([
                        '01' => 'Januari',
                        '02' => 'Februari',
                        '03' => 'Maret',
                        '04' => 'April',
                        '05' => 'Mei',
                        '06' => 'Juni',
                        '07' => 'Juli',
                        '08' => 'Agustus',
                        '09' => 'September',
                        '10' => 'Oktober',
                        '11' => 'November',
                        '12' => 'Desember',
                    ])
                    ->default(now()->format('m'))
                    ->live()
                    ->required()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedMonth = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedYear')
                    ->label('Tahun')
                    ->options(collect(range(now()->year - 2, now()->year + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->default(now()->format('Y'))
                    ->live()
                    ->required()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedYear = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedCustomer = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedTbbm = $state;
                        $this->dispatch('refresh-charts');
                    }),

                Select::make('selectedStatus')
                    ->label('Status (Opsional)')
                    ->options([
                        'pending' => 'Pending',
                        'muat' => 'Sedang Muat',
                        'selesai' => 'Selesai',
                    ])
                    ->placeholder('Semua Status')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedStatus = $state;
                        $this->dispatch('refresh-charts');
                    }),
            ])
            ->columns(5)
            ->statePath('data');
    }

    public function getDeliveryKpiData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $baseQuery = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedCustomer) {
            $baseQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTbbm) {
            $baseQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $baseQuery->where('delivery_order.status_muat', $this->selectedStatus);
        }

        $totalDeliveries = $baseQuery->count();
        $completedDeliveries = (clone $baseQuery)->where('delivery_order.status_muat', 'selesai')->count();
        $pendingDeliveries = (clone $baseQuery)->whereIn('delivery_order.status_muat', ['pending', 'muat'])->count();

        // On-time delivery calculation (assuming delivery should be completed within the scheduled date)
        $onTimeDeliveries = (clone $baseQuery)
            ->where('delivery_order.status_muat', 'selesai')
            ->whereRaw('DATE(delivery_order.waktu_selesai_muat) <= DATE(delivery_order.tanggal_delivery)')
            ->count();

        $completionRate = $totalDeliveries > 0 ? round(($completedDeliveries / $totalDeliveries) * 100, 1) : 0;
        $onTimeRate = $completedDeliveries > 0 ? round(($onTimeDeliveries / $completedDeliveries) * 100, 1) : 0;

        // Average delivery time (in hours)
        $avgDeliveryTime = (clone $baseQuery)
            ->where('delivery_order.status_muat', 'selesai')
            ->whereNotNull('delivery_order.waktu_muat')
            ->whereNotNull('delivery_order.waktu_selesai_muat')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, delivery_order.waktu_muat, delivery_order.waktu_selesai_muat)) as avg_time')
            ->value('avg_time') ?? 0;

        return [
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'pending_deliveries' => $pendingDeliveries,
            'completion_rate' => $completionRate,
            'on_time_deliveries' => $onTimeDeliveries,
            'on_time_rate' => $onTimeRate,
            'avg_delivery_time' => round($avgDeliveryTime, 1),
        ];
    }

    public function getDriverPerformanceData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $query = DB::table('delivery_order')
            ->join('users', 'delivery_order.id_user', '=', 'users.id')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('users.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        return $query->select([
            'users.name as driver_name',
            'users.no_induk as driver_id',
            DB::raw('COUNT(delivery_order.id) as total_deliveries'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_deliveries'),
            DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as completion_rate'),
            DB::raw('AVG(CASE WHEN delivery_order.status_muat = "selesai" AND delivery_order.waktu_muat IS NOT NULL AND delivery_order.waktu_selesai_muat IS NOT NULL THEN TIMESTAMPDIFF(HOUR, delivery_order.waktu_muat, delivery_order.waktu_selesai_muat) END) as avg_delivery_time')
        ])
            ->groupBy('users.id', 'users.name', 'users.no_induk')
            ->orderBy('total_deliveries', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function getVehicleUtilizationData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        return DB::table('delivery_order')
            ->join('kendaraans', 'delivery_order.id_kendaraan', '=', 'kendaraans.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->select([
                'kendaraans.no_pol_kendaraan as vehicle_plate',
                'kendaraans.tipe as vehicle_type',
                DB::raw('COUNT(delivery_order.id) as total_trips'),
                DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
                DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as utilization_rate')
            ])
            ->groupBy('kendaraans.id', 'kendaraans.no_pol_kendaraan', 'kendaraans.tipe')
            ->orderBy('total_trips', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function getDailyDeliveryTrendData(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        // PERBAIKAN: Menambahkan filter customer, TBBM, dan status
        $query = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        // Get all delivery data for the month in one query for better performance
        $monthlyDeliveries = $query->select([
            DB::raw('DATE(delivery_order.tanggal_delivery) as delivery_date'),
            DB::raw('COUNT(*) as total_deliveries'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_deliveries')
        ])
            ->groupBy(DB::raw('DATE(delivery_order.tanggal_delivery)'))
            ->get()
            ->keyBy('delivery_date');

        $dailyData = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $dateKey = $current->format('Y-m-d');
            $dayData = $monthlyDeliveries->get($dateKey);

            $dailyData[] = [
                'date' => $dateKey,
                'day' => $current->format('d'),
                'total_deliveries' => $dayData ? (int)$dayData->total_deliveries : 0,
                'completed_deliveries' => $dayData ? (int)$dayData->completed_deliveries : 0,
            ];

            $current->addDay();
        }

        return $dailyData;
    }

    public function getDeliveryStatusDistribution(): array
    {
        $startDate = Carbon::createFromFormat('Y-m-d', "{$this->selectedYear}-{$this->selectedMonth}-01")->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $query = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        $results = $query->select([
            'delivery_order.status_muat',
            DB::raw('COUNT(*) as count')
        ])
            ->groupBy('delivery_order.status_muat')
            ->get();

        $distribution = [];
        foreach ($results as $item) {
            $label = match ($item->status_muat) {
                'pending' => 'Load Order Issued',
                'muat' => 'Load Confirmed',
                'selesai' => 'Loading Complete',
                default => ucfirst($item->status_muat),
            };
            $distribution[$label] = $item->count;
        }

        return $distribution;
    }

    // Method to refresh data when filters change
    public function refreshData()
    {
        $this->dispatch('refresh-charts');
    }
}
