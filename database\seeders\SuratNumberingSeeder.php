<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NumberingSetting;

class SuratNumberingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Surat Numbering Settings...');

        // Surat Numbering Setting
        NumberingSetting::updateOrCreate(
            ['type' => 'surat'],
            [
                'prefix' => 'SURAT',
                'suffix' => null,
                'sequence_digits' => 4,
                'format' => '{SEQUENCE}/{PREFIX}/{MONTH_ROMAN}/{YEAR}',
                'reset_frequency' => 'yearly',
                'last_sequence' => 0,
                'last_reset_date' => now()->toDateString(),
            ]
        );

        $this->command->info('Surat numbering settings seeded successfully.');
    }
}
