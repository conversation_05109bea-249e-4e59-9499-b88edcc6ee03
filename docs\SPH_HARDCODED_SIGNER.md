# SPH Creator-Based Signer Implementation

## Overview

Sistem SPH telah diupdate untuk menggunakan **creator/pembuat SPH** sebagai signer. Tanda tangan akan berdasarkan user yang membuat surat penawaran, bukan hardcoded direktur.

## Changes Made

### 1. HardcodedSignerService (Updated)

-   **File**: `app/Services/HardcodedSignerService.php`
-   **Purpose**: Service untuk mengembalikan creator <PERSON><PERSON> sebagai signer
-   **Features**:
    -   Menggunakan `$sph->createdBy` sebagai signer
    -   <PERSON>a dan jabatan berdasarkan data user yang membuat SPH
    -   Tanda tangan berdasarkan `signature_path` user

### 2. Updated Templates

Semua template SPH telah diupdate untuk menggunakan `HardcodedSignerService`:

#### Preview Templates:

-   `resources/views/sph/sph-preview-id.blade.php`
-   `resources/views/sph/sph-preview-en.blade.php`
-   `resources/views/sph/_dataSetup.blade.php`

#### PDF Templates:

-   `resources/views/sph/sph-pdf-id.blade.php`
-   `resources/views/sph/sph-pdf-en.blade.php`

### 3. Updated Components

#### Signature Mounter Components:

-   `resources/views/components/signature-mounter.blade.php`
-   `resources/views/components/signature-mounter-pdf.blade.php`

Kedua komponen telah diupdate untuk menggunakan signature path dari user yang membuat SPH.

## File Requirements

### Tanda Tangan User

-   **Path**: `storage/app/public/signatures/` (atau sesuai konfigurasi user)
-   **Format**: PNG dengan background transparan
-   **Ukuran yang disarankan**: 250x100 pixels
-   **Status**: Berdasarkan `signature_path` field di tabel users

### Logo Perusahaan

-   **Path**: `public/images/lrp-logo-noBG.png`
-   **Format**: PNG dengan background transparan
-   **Purpose**: Digunakan sebagai cap/stempel perusahaan

## API Methods

### HardcodedSignerService::getDefaultSigner($sph)

```php
$signer = HardcodedSignerService::getDefaultSigner($sph);
// Returns User object (creator of SPH) or null if:
// - No SPH provided
// - SPH has no creator
//
// If returns User object, it has:
// - name: User's name
// - signature_path: User's signature file path
// - signature_url: User's signature URL (via accessor)
// - getPosition($locale): User's position based on jabatan & divisi
```

## Testing

Unit tests tersedia di `tests/Unit/HardcodedSignerServiceTest.php` untuk memverifikasi semua functionality.

Run tests:

```bash
php artisan test tests/Unit/HardcodedSignerServiceTest.php
```

## Implementation Notes

1. **Creator-Based Signing**: Sistem menggunakan user yang membuat SPH (`$sph->createdBy`) sebagai signer.

2. **Consistent Behavior**: Semua template SPH (preview dan PDF, Indonesia dan English) menggunakan service yang sama untuk konsistensi.

3. **Dynamic Values**: Sistem mengambil nama, jabatan, dan tanda tangan dari data user yang sebenarnya di database.

4. **Signature Display**: Tanda tangan akan ditampilkan di atas logo perusahaan dengan positioning yang sudah diatur, hanya jika user memiliki signature_path.

## Next Steps

1. Pastikan setiap user yang akan membuat SPH memiliki `signature_path` yang valid di database
2. Upload file tanda tangan user ke folder `storage/app/public/signatures/`
3. Pastikan file logo perusahaan tersedia di `public/images/lrp-logo-noBG.png`
4. Test preview dan PDF generation untuk memastikan tampilan sesuai ekspektasi
