<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TransaksiPenjualan;

class UpdateEmptySONumbersSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Updating empty SO numbers to "-"...');

        $updated = TransaksiPenjualan::whereNull('kode')
            ->orWhere('kode', '')
            ->update(['kode' => '-']);

        $this->command->info("Updated {$updated} records with empty SO numbers to \"-\"");
    }
}
