# Fitur Upload Foto Totalisator - Driver Delivery Detail

## Overview

Fitur ini memungkinkan driver untuk mengunggah 3 jenis foto totalisator (awal, sampai, akhir) di halaman DriverDeliveryDetail. Foto-foto ini akan ditampilkan dalam view detail dengan tampilan yang menarik dan responsif.

## Implementasi

### 1. Model Changes

#### PengirimanDriver Model
- **File:** `app/Models/PengirimanDriver.php`
- **Changes:**
  - Menambahkan media collection `foto_totalizer_sampai` untuk melengkapi yang sudah ada
  - Menambahkan helper method `getFotoTotalizerSampaiUrlAttribute()`
  - Memperbarui array collections untuk media conversions

```php
// Media Collections (ditambahkan)
$this->addMediaCollection('foto_totalizer_sampai')
    ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

// Media Conversions (diperbarui)
$collections = [
    'foto_pengiriman',
    'foto_totalizer_awal',
    'foto_totalizer_sampai',  // BARU
    'foto_totalizer_akhir',
    // ... collections lainnya
];

// Helper Method (ditambahkan)
public function getFotoTotalizerSampaiUrlAttribute()
{
    return $this->getFirstMediaUrl('foto_totalizer_sampai');
}
```

### 2. Form Changes

#### DriverDeliveryDetail Page
- **File:** `app/Filament/Pages/DriverDeliveryDetail.php`
- **Changes:**
  - Menambahkan section "Upload Foto Totalisator" dalam totalisator form
  - Menambahkan 3 field upload foto totalisator dengan grid layout
  - Memperbarui method `updateTotalisator()` untuk handle media uploads
  - Mengubah model form dari DeliveryOrder ke PengirimanDriver

```php
// Section Upload Foto Totalisator
Forms\Components\Section::make('Upload Foto Totalisator')
    ->description('Upload foto totalisator untuk dokumentasi perjalanan')
    ->schema([
        Forms\Components\Grid::make(3)
            ->schema([
                // Foto Totalisator Awal
                SpatieMediaLibraryFileUpload::make('foto_totalizer_awal')
                    ->label('Foto Totalisator Awal')
                    ->collection('foto_totalizer_awal')
                    ->helperText('Upload foto totalisator sebelum berangkat'),

                // Foto Totalisator Sampai  
                SpatieMediaLibraryFileUpload::make('foto_totalizer_sampai')
                    ->label('Foto Totalisator Sampai')
                    ->collection('foto_totalizer_sampai')
                    ->helperText('Upload foto totalisator saat tiba di tujuan'),

                // Foto Totalisator Akhir
                SpatieMediaLibraryFileUpload::make('foto_totalizer_akhir')
                    ->label('Foto Totalisator Akhir')
                    ->collection('foto_totalizer_akhir')
                    ->helperText('Upload foto totalisator setelah kembali ke pool'),
            ]),
    ])
    ->collapsible(),
```

### 3. View Changes

#### Driver Delivery Detail Template
- **File:** `resources/views/filament/pages/driver-delivery-detail.blade.php`
- **Changes:**
  - Menambahkan section "Foto Totalisator" terpisah
  - Grid layout responsif untuk menampilkan 3 jenis foto totalisator
  - Icon yang berbeda untuk setiap jenis foto totalisator
  - Modal preview untuk melihat foto ukuran penuh

```php
<!-- Foto Totalisator -->
@if ($this->record->pengirimanDriver)
    @php
        $totalisatorTypes = [
            'foto_totalizer_awal' => 'Foto Totalisator Awal',
            'foto_totalizer_sampai' => 'Foto Totalisator Sampai',
            'foto_totalizer_akhir' => 'Foto Totalisator Akhir',
        ];
    @endphp

    @if ($hasTotalisatorPhotos)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Foto Totalisator
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Grid untuk menampilkan foto-foto -->
            </div>
        </div>
    @endif
@endif
```

## Features

### Upload Foto Totalisator
- **3 Jenis Foto:**
  - **Foto Totalisator Awal:** Sebelum berangkat dari pool
  - **Foto Totalisator Sampai:** Saat tiba di tujuan pengiriman
  - **Foto Totalisator Akhir:** Setelah kembali ke pool
- **Format:** JPG, PNG, GIF, WebP
- **Ukuran maksimal:** 10MB per foto
- **Multiple upload:** Maksimal 3 foto per jenis
- **Image editor:** Built-in image editor untuk crop dan resize
- **Auto resize:** Target 800x800px untuk optimasi

### Tampilan Foto Totalisator
- **Grid responsif:** 1 kolom mobile, 3 kolom desktop
- **Icon indicators:** Icon berbeda untuk setiap jenis foto:
  - Awal: Play icon (biru)
  - Sampai: Map pin icon (hijau)
  - Akhir: Home icon (ungu)
- **Modal preview:** Klik foto untuk melihat ukuran penuh
- **Hover effects:** Efek hover dengan magnifying glass icon
- **Error handling:** Fallback ke original URL jika conversion gagal

### Conversions
- **Thumbnail:** 150x150px (untuk grid display)
- **Preview:** 400x400px (untuk modal preview)
- **Large:** 800x800px (untuk full-size viewing)
- **Quality optimization:** Kompresi otomatis untuk performa

## Usage

### Menambah Foto Totalisator
1. Buka halaman Driver Delivery Detail
2. Klik tombol "Totalisator" di quick actions
3. Scroll ke section "Upload Foto Totalisator"
4. Upload foto sesuai dengan jenisnya:
   - **Awal:** Foto totalisator sebelum berangkat
   - **Sampai:** Foto totalisator saat tiba di tujuan
   - **Akhir:** Foto totalisator setelah kembali ke pool
5. Gunakan image editor jika perlu untuk crop/resize
6. Klik "Simpan" untuk menyimpan data dan foto

### Melihat Foto Totalisator
1. Buka halaman Driver Delivery Detail
2. Scroll ke section "Foto Totalisator"
3. Foto-foto akan ditampilkan dalam grid berdasarkan jenisnya
4. Klik foto untuk melihat ukuran penuh dalam modal
5. Gunakan tombol X atau ESC untuk menutup modal

## Technical Notes

### Media Storage
- **Location:** `storage/app/public/`
- **Access:** Via `/storage/` URL
- **Naming:** UUID-based untuk mencegah konflik
- **Collections:** Setiap jenis foto memiliki collection terpisah

### Performance
- **Lazy loading:** Foto hanya dimuat saat section dibuka
- **Optimized conversions:** Multiple ukuran untuk berbagai kebutuhan
- **Non-queued:** Conversions diproses langsung untuk response cepat

### Security
- **File validation:** Hanya menerima format gambar yang aman
- **Size limits:** Maksimal 10MB per file
- **MIME type checking:** Validasi tipe file di level server

### Form Integration
- **Model binding:** Form terikat dengan PengirimanDriver model
- **Auto-creation:** PengirimanDriver record dibuat otomatis jika belum ada
- **Media handling:** Media files disimpan otomatis oleh SpatieMediaLibraryFileUpload
- **Data separation:** Data non-media dan media dihandle secara terpisah

## Workflow

### Driver Workflow
1. **Sebelum Berangkat:**
   - Input totalisator awal
   - Upload foto totalisator awal
   - Set waktu mulai

2. **Saat Tiba di Tujuan:**
   - Input totalisator tiba
   - Upload foto totalisator sampai
   - Set waktu tiba

3. **Setelah Kembali ke Pool:**
   - Input totalisator pool return
   - Upload foto totalisator akhir
   - Set waktu kembali pool

### Data Validation
- **Totalisator values:** Harus berupa angka
- **Photo requirements:** Format dan ukuran file
- **Timestamp consistency:** Waktu harus logis (awal < tiba < akhir)

## Future Enhancements

1. **GPS Integration:** Otomatis capture lokasi saat upload foto
2. **Timestamp Overlay:** Menambah timestamp pada foto
3. **Comparison View:** Membandingkan foto totalisator untuk validasi
4. **Auto Calculation:** Otomatis hitung jarak tempuh dari foto
5. **OCR Integration:** Otomatis baca angka totalisator dari foto
6. **Notification System:** Notifikasi reminder untuk upload foto
