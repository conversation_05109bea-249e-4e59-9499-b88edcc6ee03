<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Surat extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'surat';

    /**
     * The attributes that are mass assignable.
     * @var array<int, string>
     */
    protected $fillable = [
        'surat_number',
        'letter_setting_id',
        'title',
        'content',
        'surat_date',
        'status',
        'created_by',
        'signed_by_id',
        'notes_internal',
    ];



    /**
     * The attributes that should be cast.
     * @var array
     */
    protected $casts = [
        'surat_date' => 'date',
    ];

    /**
     * Get the letter setting associated with this surat.
     */
    public function letterSetting(): BelongsTo
    {
        return $this->belongsTo(LetterSetting::class);
    }

    /**
     * Get the user who created this surat.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who signed this surat.
     */
    public function signedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'signed_by_id');
    }

    /**
     * Clean UTF-8 string and remove problematic characters
     *
     * @param string $string
     * @return string
     */
    public static function cleanUtf8String(string $string): string
    {
        if (empty($string)) {
            return $string;
        }

        // Remove null bytes and other control characters
        $string = str_replace("\0", '', $string);

        // Convert to UTF-8 if not already
        if (!mb_check_encoding($string, 'UTF-8')) {
            $string = mb_convert_encoding($string, 'UTF-8', 'auto');
        }

        // Remove invalid UTF-8 sequences
        $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // Remove problematic control characters but keep newlines and tabs
        $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($string, 'UTF-8')) {
            $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');
        }

        return $string;
    }

    /**
     * Get the label for the surat status.
     *
     * @return \Illuminate\Database\Eloquent\Casts\Attribute
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: fn() => match ($this->status) {
                'draft' => 'Draft',
                'sent' => 'Terkirim',
                'archived' => 'Diarsipkan',
                default => 'Unknown',
            }
        );
    }

    /**
     * Get the color associated with the surat status for display in Filament.
     *
     * @return \Illuminate\Database\Eloquent\Casts\Attribute
     */
    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: fn() => match ($this->status) {
                'draft' => 'gray',
                'sent' => 'success',
                'archived' => 'warning',
                default => 'gray',
            }
        );
    }
}
