<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL to check and drop constraints safely
        $constraints = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'transaksi_penjualan'
            AND CONSTRAINT_TYPE = 'UNIQUE'
            AND CONSTRAINT_NAME LIKE '%kode%'
        ");

        foreach ($constraints as $constraint) {
            try {
                DB::statement("ALTER TABLE transaksi_penjualan DROP INDEX `{$constraint->CONSTRAINT_NAME}`");
            } catch (\Exception $e) {
                // Continue if constraint doesn't exist
            }
        }

        // Also check for indexes
        $indexes = DB::select("
            SHOW INDEX FROM transaksi_penjualan
            WHERE Key_name LIKE '%kode%'
            AND Key_name != 'PRIMARY'
        ");

        foreach ($indexes as $index) {
            try {
                DB::statement("ALTER TABLE transaksi_penjualan DROP INDEX `{$index->Key_name}`");
            } catch (\Exception $e) {
                // Continue if index doesn't exist
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transaksi_penjualan', function (Blueprint $table) {
            // Add back the unique constraint if needed (commented out to prevent issues)
            // $table->unique('kode', 'transaksi_penjualan_kode_unique_nullable');
        });
    }
};
