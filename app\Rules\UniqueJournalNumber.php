<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Journal;

class UniqueJournalNumber implements ValidationRule
{
    protected $ignoreId;

    public function __construct($ignoreId = null)
    {
        $this->ignoreId = $ignoreId;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $query = Journal::where('journal_number', $value);

        // Ignore current record if editing
        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }

        // Only check non-soft-deleted records
        $exists = $query->whereNull('deleted_at')->exists();

        if ($exists) {
            $fail('Nomor jurnal :value sudah digunakan.');
        }
    }
}
