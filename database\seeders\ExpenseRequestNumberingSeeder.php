<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NumberingSetting;

class ExpenseRequestNumberingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Expense Request Numbering Settings...');

        // Expense Request Numbering Setting
        NumberingSetting::updateOrCreate(
            ['type' => 'expense_request'],
            [
                'prefix' => 'EXP',
                'suffix' => null,
                'sequence_digits' => 4,
                'format' => '{PREFIX}/{YEAR}/{MONTH}/{SEQUENCE}',
                'reset_frequency' => 'monthly',
                'last_sequence' => 0,
                'last_reset_date' => now()->toDateString(),
            ]
        );

        $this->command->info('Expense Request numbering settings seeded successfully.');
        $this->command->info('Format: EXP/YYYY/MM/0001');
    }
}
