<?php

namespace App\Filament\Resources\SphResource\Pages;

use App\Filament\Resources\SphResource;
use App\Models\Sph;
use App\Models\SphDetail;
use App\Services\SphService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

// --- ADDED: Import all necessary components ---
use Filament\Forms\Get;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Actions\Action;
use Filament\Forms\Components\Toggle;
use Filament\Infolists\Components\Actions as InfolistActions;
use Filament\Forms\Components\Repeater as FormRepeater; // <-- ADDED
use Filament\Forms\Components\Hidden; // <-- ADDED
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;

// PDF generation imports
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

// custom support
use App\Support\Formatter;

class ViewSph extends ViewRecord
{
    protected static string $resource = SphResource::class;

    /**
     * Defines the layout for displaying the record's information.
     */
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Main column with primary details
                Group::make()
                    ->schema([
                        Section::make('Informasi SPH & Pelanggan')
                            ->schema([
                                TextEntry::make('sph_number')->label('Nomor SPH'),
                                TextEntry::make('letterSetting.name')
                                    ->label('Penerbitan'),
                                TextEntry::make('customer.nama')->label('Pelanggan'),
                                TextEntry::make('paymentMethod.method_display_name')->label('Metode Pembayaran'),
                                TextEntry::make('opsional_pic')->label('Contact Person (U.p.)'),
                                TextEntry::make('sph_date')->label('Tanggal SPH')->date('d F Y'),
                                TextEntry::make('valid_until_date')->label('Berlaku Hingga')->date('d F Y'),
                                // TextEntry::make('total_amount')
                                //     ->label('Total Penawaran')
                                //     ->formatStateUsing(
                                //         fn(Sph $record, $state): string =>
                                //         Formatter::currency($state, $record->letterSetting?->locale ?? 'id')
                                //     )
                                //     ->weight('bold')
                                //     ->color('primary'),
                            ])
                            ->columns(2)
                            ->collapsible(),

                        Section::make('Detail Item Penawaran')
                            ->headerActions([
                                Action::make('edit_all_display_options')
                                    ->label('Ubah Tampilan Cetak')
                                    ->icon('heroicon-o-cog-6-tooth')
                                    ->modalHeading('Ubah Opsi Tampilan Cetak untuk Semua Item')
                                    ->form([
                                        FormRepeater::make('details_form')
                                            ->label('Item')
                                            ->schema([
                                                Hidden::make('id'),
                                                Placeholder::make('item_name')
                                                    ->label('Nama Item')
                                                    ->content(fn($get) => \App\Models\Item::find($get('item_id'))?->name ?? 'N/A'),
                                                Toggle::make('show_ppn')->label('Tampilkan PPN'),
                                                Toggle::make('show_oat')->label('Tampilkan OAT'),
                                                Toggle::make('show_pbbkb')->label('Tampilkan PBBKB'),
                                            ])
                                            ->columns(4)
                                            ->addable(false)
                                            ->deletable(false),
                                    ])
                                    ->action(function (Sph $record, array $data): void {
                                        foreach ($data['details_form'] as $detailData) {
                                            $detail = SphDetail::find($detailData['id']);
                                            if ($detail) {
                                                $detail->update([
                                                    'show_ppn' => $detailData['show_ppn'],
                                                    'show_oat' => $detailData['show_oat'],
                                                    'show_pbbkb' => $detailData['show_pbbkb'],
                                                ]);
                                            }
                                        }
                                        Notification::make()->title('Pengaturan tampilan diperbarui')->success()->send();
                                    })
                                    ->fillForm(fn(Sph $record): array => [
                                        'details_form' => $record->details->map(fn(SphDetail $detail) => [
                                            'id' => $detail->id,
                                            'item_id' => $detail->item_id, // For the placeholder
                                            'show_ppn' => $detail->show_ppn,
                                            'show_oat' => $detail->show_oat,
                                            'show_pbbkb' => $detail->show_pbbkb,
                                        ])->all(),
                                    ]),
                            ])
                            ->schema([
                                RepeatableEntry::make('details')
                                    ->label('') // Hide the main repeater label
                                    ->schema([
                                        // Each item is now in its own card-like grid
                                        Grid::make(4)
                                            ->schema([
                                                TextEntry::make('item.name')
                                                    ->label('Item/Produk')
                                                    ->columnSpan(3),
                                                TextEntry::make('quantity')
                                                    ->label('Kuantitas')
                                                    ->numeric(2),
                                                TextEntry::make('description')
                                                    ->label('Deskripsi')
                                                    ->columnSpanFull(),

                                                // Grouping price components
                                                TextEntry::make('harga_dasar')
                                                    ->label('Harga Dasar')
                                                    ->formatStateUsing(fn(SphDetail $record, $state): string => Formatter::currency($state, $record->sph->letterSetting?->locale ?? 'id')),

                                                TextEntry::make('ppn')
                                                    ->label(function (SphDetail $record): HtmlString {
                                                        $status = $record->show_ppn ? '(show)' : '(hide)';
                                                        $color = $record->show_ppn ? '#3574faff' : '#f59e0b';
                                                        return new HtmlString("PPN <span style='font-weight: 600; color: {$color};'>{$status}</span>");
                                                    })
                                                    ->formatStateUsing(fn(SphDetail $record, $state): string => Formatter::currency($state, $record->sph->letterSetting?->locale ?? 'id')),

                                                TextEntry::make('oat')
                                                    ->label(function (SphDetail $record): HtmlString {
                                                        $status = $record->show_oat ? '(show)' : '(hide)';
                                                        $color = $record->show_oat ? '#3574faff' : '#f59e0b';
                                                        return new HtmlString("OAT <span style='font-weight: 600; color: {$color};'>{$status}</span>");
                                                    })
                                                    ->formatStateUsing(fn(SphDetail $record, $state): string => Formatter::currency($state, $record->sph->letterSetting?->locale ?? 'id')),

                                                TextEntry::make('pbbkb')
                                                    ->label(function (SphDetail $record): HtmlString {
                                                        $status = $record->show_pbbkb ? '(show)' : '(hide)';
                                                        $color = $record->show_pbbkb ? '#3574faff' : '#f59e0b';
                                                        return new HtmlString("PBBKB <span style='font-weight: 600; color: {$color};'>{$status}</span>");
                                                    })
                                                    ->formatStateUsing(fn(SphDetail $record, $state): string => Formatter::currency($state, $record->sph->letterSetting?->locale ?? 'id')),

                                                TextEntry::make('price')
                                                    ->label('Harga Jual')
                                                    ->weight('bold')
                                                    ->formatStateUsing(fn(SphDetail $record, $state): string => Formatter::currency($state, $record->sph->letterSetting?->locale ?? 'id')),
                                            ])
                                    ])
                            ])
                            ->collapsible(),

                    ])->columnSpan(2),

                // Side column with metadata
                Group::make()
                    ->schema([
                        Section::make('Status')
                            ->schema([
                                TextEntry::make('status')
                                    ->label('Status Saat Ini')
                                    ->badge()
                                    ->color(fn(Sph $record) => $record->status_color)
                                    ->formatStateUsing(fn(Sph $record) => $record->status_label),
                                TextEntry::make('createdBy.name')
                                    ->label('Dibuat Oleh'),
                                TextEntry::make('created_at')
                                    ->label('Tanggal Dibuat')
                                    ->dateTime('d M Y H:i'),
                            ])
                            ->collapsible(),

                        Section::make('Riwayat Approval')
                            ->schema([
                                RepeatableEntry::make('approvals')
                                    ->label('')
                                    ->schema([
                                        TextEntry::make('user.name')
                                            ->label('Oleh')
                                            ->weight('bold'),
                                        TextEntry::make('status')
                                            ->label('Tindakan')
                                            ->badge()
                                            ->formatStateUsing(fn($state) => ucfirst(str_replace('_', ' ', $state)))
                                            ->color(fn($state) => match ($state) {
                                                'approved' => 'success',
                                                'rejected' => 'danger',
                                                'needs_revision' => 'warning',
                                                default => 'gray'
                                            }),
                                        TextEntry::make('note')
                                            ->label('Catatan')
                                            ->placeholder('Tidak ada catatan.'),
                                        TextEntry::make('created_at')
                                            ->label('Waktu')
                                            ->since(),
                                    ])->columns(2)
                            ])
                            ->collapsible()
                            ->visible(fn(Sph $record) => $record->approvals->isNotEmpty()),

                        Section::make('Dokumen & Catatan')
                            ->schema([
                                SpatieMediaLibraryImageEntry::make('dokumen_sph')
                                    ->label('Dokumen SPH')
                                    ->collection('dokumen_sph')
                                    ->placeholder('Belum ada dokumen'),
                                TextEntry::make('terms_and_conditions')
                                    ->label('Syarat dan Ketentuan')
                                    ->placeholder('Tidak ada syarat dan ketentuan')
                                    ->columnSpanFull(),
                                TextEntry::make('notes_internal')
                                    ->label('Catatan Internal')
                                    ->placeholder('Tidak ada catatan internal')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                    ])->columnSpan(1),

                // side collum for document dan catatan internal


            ])->columns(3);
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Preview SPH')
                ->color('gray')
                ->icon('heroicon-o-eye')
                ->action(null)
                ->modalContent(function (Sph $record): \Illuminate\View\View {
                    // Eager load the relationship for efficiency
                    $record->load('letterSetting');

                    // Determine the locale ('id' or 'en') from the setting, defaulting to 'id'
                    $locale = $record->letterSetting?->locale ?? 'id';

                    // Construct the view name dynamically
                    $viewName = "sph.sph-preview-{$locale}";

                    // Check if the view exists before trying to render it
                    if (!View::exists($viewName)) {
                        // Fallback to the default Indonesian view if the English one is missing
                        $viewName = 'sph.sph-preview-id';
                    }

                    return View::make($viewName, ['record' => $record]);
                })
                ->modalHeading("Preview: {$this->record->sph_number}")
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Tutup')
                ->slideOver()
                ->modalWidth('4xl')
                ->extraModalFooterActions([
                    // Actions\Action::make('download_from_modal')
                    //     ->label('Download PDF')
                    //     ->color('success')
                    //     ->icon('heroicon-o-arrow-down-tray')
                    //     ->action(function (Sph $record) {
                    //         // Same logic as main download action
                    //         try {
                    //             $sph = Sph::with([
                    //                 'customer',
                    //                 'details.item',
                    //                 'createdBy'
                    //             ])->find($record->id);

                    //             $filename = 'SPH_' . str_replace(['/', '\\'], '_', $sph->sph_number) . '_' . now()->format('Ymd_His') . '.pdf';

                    //             $pdf = Pdf::loadView('sph.sph-pdf', ['record' => $sph])
                    //                 ->setPaper('a4', 'portrait')
                    //                 ->setOptions([
                    //                     'isHtml5ParserEnabled' => true,
                    //                     'isPhpEnabled' => true,
                    //                     'defaultFont' => 'Arial',
                    //                     'isRemoteEnabled' => true,
                    //                 ]);

                    //             return response()->streamDownload(function () use ($pdf) {
                    //                 echo $pdf->output();
                    //             }, $filename, [
                    //                 'Content-Type' => 'application/pdf',
                    //                 'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                    //             ]);
                    //         } catch (\Exception $e) {
                    //             Log::error('Failed to generate SPH PDF from modal: ' . $e->getMessage());

                    //             \Filament\Notifications\Notification::make()
                    //                 ->title('Error generating PDF')
                    //                 ->body('Failed to generate PDF: ' . $e->getMessage())
                    //                 ->danger()
                    //                 ->send();

                    //             return null;
                    //         }
                    //     })

                    Actions\Action::make('download_from_modal')
                        ->label('Download PDF')
                        ->color('success')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(fn(Sph $record, SphService $sphService) => $sphService->generatePdf($record))
                ]),

            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray')
                ->action(function (Sph $record) {
                    try {
                        // Load the record with all necessary relationships
                        $sph = Sph::with([
                            'customer',
                            'details.item',
                            'createdBy'
                        ])->find($record->id);

                        // Generate dynamic filename
                        $filename = 'SPH_' . str_replace(['/', '\\'], '_', $sph->sph_number) . '_' . now()->format('Ymd_His') . '.pdf';

                        $locale = $record->letterSetting?->locale ?? 'id';
                        $viewName = "sph.sph-pdf-{$locale}";
                        if (!View::exists($viewName)) {
                            $viewName = 'sph.sph-pdf-id'; // Fallback
                        }

                        $pdf_with_locale = Pdf::loadView($viewName, ['record' => $record])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial',
                                'isRemoteEnabled' => true,
                            ]);

                        // Load the PDF view with the record data
                        $pdf = Pdf::loadView($viewName, ['record' => $sph])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial',
                                'isRemoteEnabled' => true,
                            ]);

                        // Stream the PDF as a download
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $filename, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                        ]);
                    } catch (\Exception $e) {
                        // Log the error for debugging
                        Log::error('Failed to generate SPH PDF: ' . $e->getMessage());
                        Log::error('SPH PDF Error Stack Trace: ' . $e->getTraceAsString());
                        Log::error('SPH PDF Error Context: ', [
                            'sph_id' => $record->id,
                            'sph_number' => $record->sph_number,
                            'user_id' => \Illuminate\Support\Facades\Auth::id(),
                        ]);

                        // Show notification to user
                        \Filament\Notifications\Notification::make()
                            ->title('Error generating PDF')
                            ->body('Failed to generate PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                }),

            Actions\EditAction::make()
                ->visible(fn(Sph $record): bool => $record->isEditable()),

            // Submit SPH for Approval Action
            Actions\Action::make('submit_for_approval')
                ->label('Submit untuk Approval')
                ->color('warning')
                ->icon('heroicon-o-paper-airplane')
                ->requiresConfirmation()
                ->modalHeading('Submit SPH untuk Approval')
                ->modalDescription('Apakah Anda yakin ingin mengirim SPH ini untuk approval? SPH tidak dapat diedit setelah di-approve.')
                ->modalSubmitActionLabel('Ya, Submit')
                ->modalCancelActionLabel('Batal')
                ->action(function (Sph $record, SphService $sphService): void {
                    try {
                        \Illuminate\Support\Facades\Log::info("=== SUBMIT SPH ACTION TRIGGERED FROM VIEW PAGE ===");
                        \Illuminate\Support\Facades\Log::info("Record ID: {$record->id}, Status: {$record->status}");

                        $sphService->submitForApproval($record);

                        Notification::make()
                            ->title('SPH Berhasil Disubmit')
                            ->body("SPH {$record->sph_number} telah dikirim untuk approval.")
                            ->success()
                            ->send();

                        // Refresh the page to show updated status
                        $this->redirect(static::getResource()::getUrl('view', ['record' => $record]));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal Submit SPH')
                            ->body('Error: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn(Sph $record): bool => $record->status === 'draft'),

            // Approve SPH Action
            Actions\Action::make('approve_sph')
                ->label('Approve SPH')
                ->color('success')
                ->icon('heroicon-o-check-circle')
                ->form([
                    Textarea::make('note')
                        ->label('Catatan Approval (Opsional)')
                        ->rows(3)
                        ->placeholder('Tambahkan catatan untuk approval ini...'),
                ])
                ->action(function (Sph $record, SphService $sphService, array $data): void {
                    try {
                        $sphService->processApproval(
                            $record,
                            Auth::user(),
                            'approved',
                            $data['note'] ?? null
                        );

                        Notification::make()
                            ->title('SPH Berhasil Diapprove')
                            ->body("SPH {$record->sph_number} telah disetujui.")
                            ->success()
                            ->send();

                        // Refresh the page to show updated status
                        $this->redirect(static::getResource()::getUrl('view', ['record' => $record]));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal Approve SPH')
                            ->body('Error: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn(Sph $record): bool => $record->status === 'pending_approval'),

            // Reject SPH Action
            Actions\Action::make('reject_sph')
                ->label('Reject SPH')
                ->color('danger')
                ->icon('heroicon-o-x-circle')
                ->form([
                    Textarea::make('note')
                        ->label('Alasan Penolakan')
                        ->required()
                        ->rows(3)
                        ->placeholder('Jelaskan alasan penolakan SPH ini...'),
                ])
                ->action(function (Sph $record, SphService $sphService, array $data): void {
                    try {
                        $sphService->processApproval(
                            $record,
                            Auth::user(),
                            'rejected',
                            $data['note']
                        );

                        Notification::make()
                            ->title('SPH Ditolak')
                            ->body("SPH {$record->sph_number} telah ditolak.")
                            ->warning()
                            ->send();

                        // Refresh the page to show updated status
                        $this->redirect(static::getResource()::getUrl('view', ['record' => $record]));
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal Reject SPH')
                            ->body('Error: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn(Sph $record): bool => $record->status === 'pending_approval'),
        ];
    }

    protected function downloadPdf(Sph $record)
    {
        try {
            // Load the record with all necessary relationships
            $sph = Sph::with([
                'customer',
                'details.item',
                'createdBy'
            ])->find($record->id);

            // Generate dynamic filename
            $filename = 'SPH_' . $sph->sph_number . '_' . now()->format('Ymd_His') . '.pdf';

            // Load the PDF view with the record data
            $pdf = Pdf::loadView('sph.sph-pdf', ['record' => $sph])
                ->setPaper('a4', 'portrait')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'Arial'
                ]);

            // Stream the PDF as a download
            return response()->streamDownload(function () use ($pdf) {
                echo $pdf->output();
            }, $filename);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Failed to generate SPH PDF: ' . $e->getMessage());
            Log::error('SPH PDF Error Stack Trace: ' . $e->getTraceAsString());

            // Show notification to user
            \Filament\Notifications\Notification::make()
                ->title('Error generating PDF')
                ->body('Failed to generate PDF. Please try again or contact administrator.')
                ->danger()
                ->send();

            return;
        }
    }
}
