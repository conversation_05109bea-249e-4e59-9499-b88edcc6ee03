<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NumberingSetting;

class SalesOrderNumberingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create numbering setting for sales orders (transaksi_penjualan)
        NumberingSetting::updateOrCreate(
            ['type' => 'transaksi_penjualan'],
            [
                'prefix' => 'SO',
                'suffix' => null,
                'sequence_digits' => 4,
                'format' => '{PREFIX}/{YEAR}/{MONTH_ROMAN}/{SEQUENCE}',
                'reset_frequency' => 'monthly',
                'last_sequence' => 0,
                'last_reset_date' => now()->toDateString(),
            ]
        );

        echo "✅ Sales Order numbering setting created: SO/YYYY/MM/0001\n";
    }
}
