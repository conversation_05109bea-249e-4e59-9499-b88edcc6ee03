<?php

namespace App\Services;

use App\Models\TransaksiPenjualan;
use App\Models\TransaksiPenjualanApproval;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;
use App\Services\NotificationTemplateService;

/**
 * Handles all business logic related to Service Transactions (Transaksi Jasa).
 * This includes creation, approval processing, and other related operations.
 */
class TransaksiJasaService
{
    public function __construct(protected MessageService $messageService)
    {
        // MessageService is now injected
    }

    /**
     * Processes an approval decision for a Service Transaction.
     *
     * @param TransaksiPenjualan $transaksi The service transaction record to be processed.
     * @param User $approver The user performing the approval action.
     * @param string $status The new status ('approved', 'rejected', 'reject_with_perbaikan').
     * @param string|null $note Optional notes for the approval or rejection.
     * @return TransaksiPenjualanApproval The newly created approval record.
     */
    public function processApproval(TransaksiPenjualan $transaksi, User $approver, string $status, ?string $note): TransaksiPenjualanApproval
    {
        // Ensure this is a service transaction
        if ($transaksi->tipe !== 'jasa') {
            throw new \InvalidArgumentException('This service can only process service transactions (jasa)');
        }

        // DB Transaction for data integrity
        $approval = DB::transaction(function () use ($transaksi, $approver, $status, $note) {
            // Create approval record
            $approvalRecord = TransaksiPenjualanApproval::create([
                'id_transaksi_penjualan' => $transaksi->id,
                'created_by' => $approver->id,
                'status' => $status,
                'note' => $note,
            ]);

            // Update transaction status
            $transaksi->status = match ($status) {
                'approved' => 'approved',
                'rejected' => 'rejected',
                'reject_with_perbaikan' => 'needs_revision',
                default => $transaksi->status,
            };
            $transaksi->save();

            return $approvalRecord;
        });

        // Send notifications
        try {
            // Send notification to salesperson
            if ($transaksi->createdBy->hp) {
                $salesPerson = $transaksi->createdBy;
                $waResponse = match ($status) {
                    'approved' => $this->messageService->sendJasaApprovedNotification($transaksi, $salesPerson, $approver),
                    'rejected' => $this->messageService->sendJasaRejectedNotification($transaksi, $salesPerson, $approver, $note),
                    'reject_with_perbaikan' => $this->messageService->sendJasaNeedsRevisionNotification($transaksi, $salesPerson, $approver, $note),
                    default => null,
                };

                // Log if WA notification failed
                if ($waResponse && ($waResponse['status'] ?? false) === false) {
                    Log::warning('Notifikasi WA Gagal Terkirim untuk Transaksi Jasa', [
                        'transaksi_id' => $transaksi->id,
                        'status_approval' => $status,
                        'provider_response' => $waResponse,
                    ]);
                }
            }

            // Send notification to operations team based on status
            if ($status === 'approved') {
                $this->sendApprovedTransactionNotification($transaksi);
            } elseif ($status === 'rejected') {
                $this->sendRejectedTransactionNotification($transaksi);
            } elseif ($status === 'reject_with_perbaikan') {
                $this->sendRevisionTransactionNotification($transaksi);
            }
        } catch (Throwable $e) {
            Log::error('Gagal Menghubungi Service Notifikasi untuk Transaksi Jasa', [
                'transaksi_id' => $transaksi->id,
                'error_message' => $e->getMessage(),
            ]);
        }

        return $approval;
    }

    /**
     * Send notification to operations team when service transaction is approved
     */
    private function sendApprovedTransactionNotification(TransaksiPenjualan $transaksi): void
    {
        try {
            $eventName = 'transaksi_jasa_approved';

            $notificationSettings = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName);

            foreach ($notificationSettings as $setting) {
                if ($setting->user && $setting->user->hp) {
                    // Get transaction data
                    $data = NotificationTemplateService::getTransaksiData($transaksi, [
                        'approved_by' => auth()->user()->name ?? 'System',
                        'link_create_do' => url('/admin/delivery-orders/create?transaksi=' . $transaksi->id),
                    ]);

                    // Generate message from template
                    $message = NotificationTemplateService::getMessageForEvent($eventName, $data, $setting->user_id);

                    // Send via MessageService
                    $this->messageService->sendCustomMessage($setting->user->hp, $message);

                    Log::info("Sent approved service transaction notification to {$setting->user->name} ({$setting->user->hp}) for transaction {$transaksi->kode}");
                }
            }
        } catch (Throwable $e) {
            Log::error('Failed to send approved service transaction notification', [
                'transaksi_id' => $transaksi->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification when service transaction is rejected
     */
    private function sendRejectedTransactionNotification(TransaksiPenjualan $transaksi): void
    {
        try {
            $eventName = 'transaksi_jasa_rejected';

            $notificationSettings = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName);

            foreach ($notificationSettings as $setting) {
                if ($setting->user && $setting->user->hp) {
                    $data = NotificationTemplateService::getTransaksiData($transaksi, [
                        'rejected_by' => auth()->user()->name ?? 'System',
                    ]);

                    $message = NotificationTemplateService::getMessageForEvent($eventName, $data, $setting->user_id);
                    $this->messageService->sendCustomMessage($setting->user->hp, $message);

                    Log::info("Sent rejected service transaction notification to {$setting->user->name} ({$setting->user->hp}) for transaction {$transaksi->kode}");
                }
            }
        } catch (Throwable $e) {
            Log::error('Failed to send rejected service transaction notification', [
                'transaksi_id' => $transaksi->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification when service transaction needs revision
     */
    private function sendRevisionTransactionNotification(TransaksiPenjualan $transaksi): void
    {
        try {
            $eventName = 'transaksi_jasa_needs_revision';

            $notificationSettings = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName);

            foreach ($notificationSettings as $setting) {
                if ($setting->user && $setting->user->hp) {
                    $data = NotificationTemplateService::getTransaksiData($transaksi, [
                        'revision_requested_by' => auth()->user()->name ?? 'System',
                    ]);

                    $message = NotificationTemplateService::getMessageForEvent($eventName, $data, $setting->user_id);
                    $this->messageService->sendCustomMessage($setting->user->hp, $message);

                    Log::info("Sent revision service transaction notification to {$setting->user->name} ({$setting->user->hp}) for transaction {$transaksi->kode}");
                }
            }
        } catch (Throwable $e) {
            Log::error('Failed to send revision service transaction notification', [
                'transaksi_id' => $transaksi->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
