<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AsetResource\Pages;
use App\Filament\Resources\AsetResource\RelationManagers;
use App\Models\Aset;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AsetResource extends Resource
{
    protected static ?string $model = Aset::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationGroup = 'Manajemen Keuangan';
    protected static ?string $label = 'Aset';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Aset')
                    ->schema([
                        Forms\Components\TextInput::make('kode_aset')
                            ->label('Kode Aset')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('XX-XX-XX')
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('nama_aset')
                            ->label('Nama Aset')
                            ->required()
                            ->placeholder('Aset X')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('deskripsi_aset')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->placeholder('Masukan deskripsi...')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('kategori_aset')
                            ->label('Kategori Aset')
                            ->options(Aset::ASSET_CATEGORIES)
                            ->required()
                            ->native(false)
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Tanah tidak dapat didepresiasi
                                if ($state === 'Tanah') {
                                    $set('is_depreciable', false);
                                } else {
                                    $set('is_depreciable', true);
                                }
                            }),

                        Forms\Components\Select::make('id_akun_aset')
                            ->label('Akun Aset')
                            ->relationship('akunAset', 'nama_akun', fn($query) => $query->where('kategori_akun', 'Aset')->where('tipe_akun', 'Debit'))
                            ->searchable()
                            ->preload(),

                        Forms\Components\Toggle::make('is_depreciable')
                            ->label('Dapat Didepresiasi')
                            ->default(true)
                            ->live()
                            ->helperText('Tanah tidak dapat didepresiasi')
                            ->disabled(fn(Forms\Get $get) => $get('kategori_aset') == 'Tanah'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Nilai & Akuisisi')
                    ->schema([
                        Forms\Components\TextInput::make('nilai_aset')
                            ->label('Nilai Perolehan')
                            ->required()
                            ->numeric()
                            ->placeholder('0')
                            ->prefix('Rp')
                            ->step(0.01),
                        Forms\Components\DatePicker::make('tanggal_akuisisi')
                            ->label('Tanggal Akuisisi')
                            ->required()
                            ->native(false)
                            ->default(now()),

                        Forms\Components\Select::make('id_akun_akuisisi')
                            ->label('Akun Akuisisi')
                            ->relationship('akunAkuisisi', 'nama_akun', fn($query) => $query->where('kategori_akun', 'Aset')->where('tipe_akun', 'Debit'))
                            ->searchable()
                            ->preload(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Pengaturan Depresiasi')
                    ->schema([
                        Forms\Components\TextInput::make('useful_life_months')
                            ->label('Masa Manfaat (Bulan)')
                            ->numeric()
                            ->default(5)
                            ->minValue(1)
                            ->maxValue(600)
                            ->required(),
                        Forms\Components\TextInput::make('salvage_value')
                            ->label('Nilai Sisa')
                            ->numeric()
                            ->prefix('Rp')
                            ->step(0.01)
                            ->default(0),
                        Forms\Components\TextInput::make('annual_depreciation_rate')
                            ->label('Tingkat Depresiasi (%)')
                            ->numeric()
                            ->suffix('%')
                            ->step(0.01)
                            ->minValue(0)
                            ->maxValue(100)
                            ->visible(fn(Forms\Get $get) => $get('depreciation_method') === 'declining_balance'),
                        Forms\Components\DatePicker::make('depreciation_start_date')
                            ->label('Tanggal Mulai Depresiasi')
                            ->native(false)
                            ->placeholder('01-01-2001')
                            ->helperText('Kosongkan untuk menggunakan tanggal akuisisi'),


                        Forms\Components\Select::make('id_akun_beban')
                            ->label('Akun Beban')
                            ->relationship('akunBeban', 'nama_akun', fn($query) => $query->where('tipe_akun', 'Debit'))
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('id_akun_akumulasi')
                            ->label('Akun Akumulasi')
                            ->relationship('akunAkumulasi', 'nama_akun', fn($query) => $query->where('kategori_akun', 'Aset')->where('tipe_akun', 'Kredit'))
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Textarea::make('depreciation_notes')
                            ->label('Catatan Depresiasi')
                            ->rows(3)
                            ->placeholder('Masukan catatan...')
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->visible(fn(Forms\Get $get) => $get('is_depreciable')),

                Forms\Components\Hidden::make('created_by')
                    ->default(auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_aset')
                    ->label('Kode Aset')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('nama_aset')
                    ->label('Nama Aset')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('kategori_aset')
                    ->label('Kategori')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Tanah' => 'success',
                        'Bangunan' => 'info',
                        'Peralatan' => 'warning',
                        'Transportasi' => 'primary',
                        'Lainnya' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('nilai_aset')
                    ->label('Nilai Perolehan')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('accumulated_depreciation')
                    ->label('Akumulasi Depresiasi')
                    ->money('IDR')
                    ->sortable()
                    ->getStateUsing(fn($record) => $record->accumulatedDepreciations())
                    ->toggleable(),

                Tables\Columns\TextColumn::make('last_depreciation_date')
                    ->label('Tanggal Depresiasi Terakhir')
                    ->sortable()
                    ->getStateUsing(fn($record) => $record->last_depreciation_date_formatted),

                Tables\Columns\TextColumn::make('book_value')
                    ->label('Nilai Buku')
                    ->money('IDR')
                    ->sortable()
                    ->getStateUsing(fn($record) => $record->calculateCurrentBookValue())
                    ->color('success'),

                Tables\Columns\TextColumn::make('tanggal_akuisisi')
                    ->label('Tanggal Akuisisi')
                    ->date()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_depreciable')
                    ->label('Dapat Didepresiasi')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('kategori_aset')
                    ->label('Kategori')
                    ->native(false)
                    ->options(Aset::ASSET_CATEGORIES),

                Tables\Filters\TernaryFilter::make('is_depreciable')
                    ->label('Dapat Didepresiasi')
                    ->boolean()
                    ->trueLabel('Ya')
                    ->falseLabel('Tidak'),

                Tables\Filters\SelectFilter::make('bulan_depresiasi')
                    ->label('Bulan Depresiasi Terakhir')
                    ->native(false)
                    ->options([
                        '01' => 'Januari',
                        '02' => 'Februari',
                        '03' => 'Maret',
                        '04' => 'April',
                        '05' => 'Mei',
                        '06' => 'Juni',
                        '07' => 'Juli',
                        '08' => 'Agustus',
                        '09' => 'September',
                        '10' => 'Oktober',
                        '11' => 'November',
                        '12' => 'Desember',
                    ])
                    ->placeholder('Semua Bulan')
                    ->query(function (Builder $query, array $data) {
                        if ($data['value']) {
                            $query->whereMonth('last_depreciation_date', $data['value']);
                        }
                    }),

            ])
            ->actions([
                Tables\Actions\Action::make('calculate_depreciation')
                    ->label('Hitung Depresiasi')
                    ->icon('heroicon-o-calculator')
                    ->color('warning')
                    ->action(function (Aset $record) {
                        $monthlyDepreciation = $record->processDepreciation();

                        \Filament\Notifications\Notification::make()
                            ->title('Depresiasi Berhasil Dihitung')
                            ->body($monthlyDepreciation['message'])
                            ->success()
                            ->send();
                    })
                    ->visible(fn(Aset $record): bool => $record->is_depreciable && !$record->isFullyDepreciated()),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAsets::route('/'),
            'create' => Pages\CreateAset::route('/create'),
            'view' => Pages\ViewAset::route('/{record}'),
            'edit' => Pages\EditAset::route('/{record}/edit'),
        ];
    }
}
