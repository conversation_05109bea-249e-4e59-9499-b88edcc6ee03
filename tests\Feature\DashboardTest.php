<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
    }

    /** @test */
    public function executive_summary_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/executive-summary-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function sales_by_type_location_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/sales-by-type-location-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function detailed_sales_report_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/detailed-sales-report-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function operational_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/operational-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function sales_marketing_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/sales-marketing-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function financial_performance_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/financial-performance-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function driver_fleet_management_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/driver-fleet-management-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function customer_analytics_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/customer-analytics-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function monthly_sales_realization_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/monthly-sales-realization-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function monthly_delivery_report_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/monthly-delivery-report-dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function accounts_receivable_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->user)
            ->get('/admin/accounts-receivable-dashboard');

        $response->assertStatus(200);
    }
}
