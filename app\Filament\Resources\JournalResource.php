<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JournalResource\Pages;
use App\Models\Journal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;


class JournalResource extends Resource
{
    protected static ?string $model = Journal::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationLabel = 'Jurnal';

    protected static ?string $modelLabel = 'Jurnal';

    protected static ?string $pluralModelLabel = 'Jurnal';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Journal')
                    ->description('Data dasar journal entry')
                    ->icon('heroicon-o-document-text')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('journal_number')
                                    ->label('Nomor Jurnal')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->placeholder('Auto-generated'),

                                Forms\Components\DatePicker::make('transaction_date')
                                    ->label('Tanggal Transaksi')
                                    ->required()
                                    ->default(now())
                                    ->native(false),

                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'Draft' => 'Draft',
                                        'Posted' => 'Posted',
                                        'Cancelled' => 'Cancelled',
                                        'Error' => 'Error',
                                    ])
                                    ->default('Draft')
                                    ->native(false),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('source_type')
                                    ->label('Tipe Sumber')
                                    ->options([
                                        'Sale' => 'Penjualan',
                                        'Purchase' => 'Pembelian',
                                        'Payment' => 'Pembayaran',
                                        'Receipt' => 'Penerimaan',
                                        'ManualAdjust' => 'Penyesuaian Manual',
                                        'ExpenseRequest' => 'Expense Request',
                                        'Invoice' => 'Invoice',
                                    ])
                                    ->native(false),

                                Forms\Components\TextInput::make('reference_number')
                                    ->label('Nomor Referensi')
                                    ->maxLength(255)
                                    ->placeholder('Opsional'),
                            ]),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->rows(3)
                            ->placeholder('Deskripsi journal entry...')
                            ->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->collapsible(),

                Forms\Components\Section::make('Journal Entries')
                    ->description('Detail entri debit dan kredit')
                    ->icon('heroicon-o-table-cells')
                    ->schema([
                        Forms\Components\Repeater::make('journalEntries')
                            ->relationship()
                            ->schema([
                                Forms\Components\Grid::make(4)
                                    ->schema([
                                        Forms\Components\Select::make('account_id')
                                            ->label('Akun')
                                            ->relationship('account', 'nama_akun', fn($query) => $query->orderBy('kode_akun'))
                                            ->required()
                                            ->searchable(['kode_akun', 'nama_akun'])
                                            ->preload()
                                            ->getOptionLabelFromRecordUsing(fn($record) => "{$record->kode_akun} - {$record->nama_akun}")
                                            ->columnSpan(2),

                                        Forms\Components\TextInput::make('debit')
                                            ->label('Debit')
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->default(0)
                                            ->placeholder('0')
                                            ->live(),

                                        Forms\Components\TextInput::make('credit')
                                            ->label('Kredit')
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->default(0)
                                            ->placeholder('0')
                                            ->live(),
                                    ]),

                                Forms\Components\Textarea::make('description')
                                    ->label('Deskripsi Entry')
                                    ->required()
                                    ->rows(2)
                                    ->placeholder('Deskripsi untuk entry ini...')
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('sort_order')
                                    ->label('Urutan')
                                    ->numeric()
                                    ->default(0)
                                    ->hidden(),
                            ])
                            ->columns(1)
                            ->defaultItems(2)
                            ->addActionLabel('+ Tambah Entry')
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->itemLabel(
                                fn(array $state): ?string =>
                                isset($state['account_id']) && $state['account_id']
                                    ? \App\Models\Akun::find($state['account_id'])?->nama_akun ?? 'Entry Baru'
                                    : 'Entry Baru'
                            )
                            ->columnSpanFull(),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Ringkasan')
                    ->description('Total debit dan kredit harus balance')
                    ->icon('heroicon-o-calculator')
                    ->schema([
                        Forms\Components\Placeholder::make('balance_info')
                            ->label('')
                            ->content(function (\Filament\Forms\Get $get): \Illuminate\Support\HtmlString {
                                $entries = $get('journalEntries') ?? [];
                                $totalDebit = 0;
                                $totalCredit = 0;

                                foreach ($entries as $entry) {
                                    $totalDebit += (float) ($entry['debit'] ?? 0);
                                    $totalCredit += (float) ($entry['credit'] ?? 0);
                                }

                                $difference = $totalDebit - $totalCredit;
                                $isBalanced = abs($difference) < 0.01;

                                $debitFormatted = 'Rp ' . number_format($totalDebit, 0, ',', '.');
                                $creditFormatted = 'Rp ' . number_format($totalCredit, 0, ',', '.');
                                $differenceFormatted = 'Rp ' . number_format(abs($difference), 0, ',', '.');

                                $status = $isBalanced ? '✅ Journal Balance' : '❌ Journal Tidak Balance';
                                $statusColor = $isBalanced ? 'text-green-600' : 'text-red-600';

                                $html = "
                                    <div class='grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border'>
                                        <div class='text-center'>
                                            <div class='text-sm text-gray-500 dark:text-gray-400 font-medium'>Total Debit</div>
                                            <div class='text-lg font-semibold text-green-600 mt-1'>{$debitFormatted}</div>
                                        </div>
                                        <div class='text-center'>
                                            <div class='text-sm text-gray-500 dark:text-gray-400 font-medium'>Total Kredit</div>
                                            <div class='text-lg font-semibold text-red-600 mt-1'>{$creditFormatted}</div>
                                        </div>
                                        <div class='text-center'>
                                            <div class='text-sm text-gray-500 dark:text-gray-400 font-medium'>Status</div>
                                            <div class='text-lg font-semibold {$statusColor} mt-1'>{$status}</div>
                                            " . (!$isBalanced ? "<div class='text-sm text-orange-600 mt-1'>Selisih: {$differenceFormatted}</div>" : "") . "
                                        </div>
                                    </div>
                                ";

                                if (!$isBalanced) {
                                    $html .= "
                                        <div class='mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg'>
                                            <div class='flex items-center'>
                                                <svg class='w-5 h-5 text-orange-500 mr-2' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z'></path>
                                                </svg>
                                                <span class='text-sm text-orange-700 dark:text-orange-300'>
                                                    Journal tidak balance. Pastikan total debit sama dengan total kredit.
                                                </span>
                                            </div>
                                        </div>
                                    ";
                                }

                                return new \Illuminate\Support\HtmlString($html);
                            })
                            ->live(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('transaction_date', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('journal_number')
                    ->label('Nomor Jurnal')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Referensi')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source_type')
                    ->label('Tipe')
                    ->badge(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->colors([
                        'secondary' => 'Draft',
                        'success' => 'Posted',
                        'danger' => 'Cancelled',
                        'warning' => 'Error',
                    ]),
                Tables\Columns\TextColumn::make('total_debit')
                    ->label('Total Debit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->journalEntries->sum('debit')),
                Tables\Columns\TextColumn::make('total_credit')
                    ->label('Total Credit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->journalEntries->sum('credit')),
                Tables\Columns\IconColumn::make('is_balanced')
                    ->label('Seimbang')
                    ->boolean()
                    ->getStateUsing(fn($record) => $record->isBalanced()),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'Draft' => 'Draft',
                        'Posted' => 'Posted',
                        'Cancelled' => 'Cancelled',
                        'Error' => 'Error',
                    ]),
                Tables\Filters\SelectFilter::make('source_type')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                    ]),
                Tables\Filters\TernaryFilter::make('is_balanced')
                    ->label('Status Seimbang')
                    ->placeholder('Semua')
                    ->trueLabel('Seimbang')
                    ->falseLabel('Tidak Seimbang')
                    ->queries(
                        true: fn(Builder $query) => $query->balanced(),
                        false: fn(Builder $query) => $query->unbalanced(),
                        blank: fn(Builder $query) => $query,
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->status === 'Draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            // Only delete records that are in Draft status
                            $records->each(function ($record) {
                                if ($record->status === 'Draft') {
                                    $record->delete();
                                }
                            });
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJournals::route('/'),
            'create' => Pages\CreateJournal::route('/create'),
            'view' => Pages\ViewJournal::route('/{record}'),
            'edit' => Pages\EditJournal::route('/{record}/edit'),
        ];
    }
}
