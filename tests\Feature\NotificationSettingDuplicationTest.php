<?php

namespace Tests\Feature;

use App\Models\NotificationSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationSettingDuplicationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->user1 = User::factory()->create(['name' => 'Test User 1']);
        $this->user2 = User::factory()->create(['name' => 'Test User 2']);
    }

    /** @test */
    public function notification_setting_can_be_duplicated_with_different_user()
    {
        // Create original notification setting
        $original = NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Duplicate with different user
        $duplicate = NotificationSetting::create([
            'event_name' => $original->event_name,
            'user_id' => $this->user2->id,
            'channel' => $original->channel,
            'is_active' => $original->is_active,
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user2->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        $this->assertNotEquals($original->id, $duplicate->id);
        $this->assertEquals($original->event_name, $duplicate->event_name);
        $this->assertEquals($original->channel, $duplicate->channel);
        $this->assertNotEquals($original->user_id, $duplicate->user_id);
    }

    /** @test */
    public function notification_setting_can_be_duplicated_with_different_channel()
    {
        // Create original notification setting
        $original = NotificationSetting::create([
            'event_name' => 'penjualan_disetujui',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Duplicate with different channel
        $duplicate = NotificationSetting::create([
            'event_name' => $original->event_name,
            'user_id' => $original->user_id,
            'channel' => 'email',
            'is_active' => $original->is_active,
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_disetujui',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_disetujui',
            'user_id' => $this->user1->id,
            'channel' => 'email',
        ]);
    }

    /** @test */
    public function duplicate_notification_setting_combination_should_not_be_allowed()
    {
        // Create original notification setting
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Check if duplicate combination exists
        $existing = NotificationSetting::where([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
        ])->first();

        $this->assertNotNull($existing);

        // Count total records with this combination
        $count = NotificationSetting::where([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
        ])->count();

        $this->assertEquals(1, $count);
    }

    /** @test */
    public function notification_setting_can_be_duplicated_with_different_event()
    {
        // Create original notification setting
        $original = NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Duplicate with different event
        $duplicate = NotificationSetting::create([
            'event_name' => 'penjualan_disetujui',
            'user_id' => $original->user_id,
            'channel' => $original->channel,
            'is_active' => $original->is_active,
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_baru',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
        ]);

        $this->assertDatabaseHas('notification_settings', [
            'event_name' => 'penjualan_disetujui',
            'user_id' => $this->user1->id,
            'channel' => 'whatsapp',
        ]);
    }

    /** @test */
    public function notification_setting_duplication_preserves_all_attributes()
    {
        // Create original notification setting
        $original = NotificationSetting::create([
            'event_name' => 'expense_request_created',
            'user_id' => $this->user1->id,
            'channel' => 'database',
            'is_active' => false,
        ]);

        // Duplicate with different user but same other attributes
        $duplicate = NotificationSetting::create([
            'event_name' => $original->event_name,
            'user_id' => $this->user2->id,
            'channel' => $original->channel,
            'is_active' => $original->is_active,
        ]);

        $this->assertEquals($original->event_name, $duplicate->event_name);
        $this->assertEquals($original->channel, $duplicate->channel);
        $this->assertEquals($original->is_active, $duplicate->is_active);
        $this->assertNotEquals($original->user_id, $duplicate->user_id);
        $this->assertNotEquals($original->id, $duplicate->id);
    }
}
