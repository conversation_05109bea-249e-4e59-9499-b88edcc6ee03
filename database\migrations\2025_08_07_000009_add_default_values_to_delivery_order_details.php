<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_order_details', function (Blueprint $table) {
            // Add default values to prevent SQL errors
            $table->decimal('volume_ordered', 15, 2)->default(0)->change();
            $table->decimal('volume_delivered', 15, 2)->default(0)->change();
            $table->decimal('unit_price', 15, 2)->default(0)->change();
            $table->decimal('total_amount', 15, 2)->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_order_details', function (Blueprint $table) {
            // Remove default values
            $table->decimal('volume_ordered', 15, 2)->default(null)->change();
            $table->decimal('volume_delivered', 15, 2)->default(null)->change();
            $table->decimal('unit_price', 15, 2)->default(null)->change();
            $table->decimal('total_amount', 15, 2)->default(null)->change();
        });
    }
};
