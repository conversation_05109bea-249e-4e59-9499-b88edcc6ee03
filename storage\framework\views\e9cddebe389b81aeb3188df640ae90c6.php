

<!-- include -->
<?php
    use App\Support\Formatter;
?>

<!-- logic -->
<?php
    // var
    $locale = 'id';
    $letterSetting = $record->letterSetting;

    // --- UPDATED: The query now specifically fetches only the two required ISOs ---
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();

    // SIGNATURE SET PART - Use hardcoded director
    use App\Services\HardcodedSignerService;
    $signer = HardcodedSignerService::getDefaultSigner($record ?? null);
?>

<div class="p-4 sm:p-6 bg-white font-sans text-gray-800">

    
    <header class="mb-8">
        <table class="w-full">
            <tbody>
                <tr>
                    
                    <td class="w-1/3">
                        <img src="<?php echo e(asset('images/lrp.png')); ?>" alt="PT. Lintas Riau Prima" style="height: 150px;"
                            class="mb-2">
                    </td>
                    
                    <td class="w-1/3"></td>
                    
                    <td class="w-1/3 text-right">
                        <h2 class="font-bold text-lg">TRUSTED & RELIABLE PARTNER</h2>
                        <p class="text-xs">Fuel Agent – Fuel Transportation – Bunker Service</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </header>

    
    <section class="mb-4">
        <div class="text-right text-sm font-semibold">
            <?php echo e($letterSetting?->city); ?>, <?php echo e(Formatter::date($record->sph_date, 'id')); ?>

        </div>

        <table class="text-sm mt-4">
            <tbody>
                <tr>
                    <td class="pr-2">No</td>
                    <td class="pr-2">:</td>
                    <td class="font-semibold"><?php echo e($record->sph_number); ?></td>
                </tr>
                <tr>
                    <td class="pr-2">Lampiran</td>
                    <td class="pr-2">:</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td class="pr-2 align-top">Perihal</td>
                    <td class="pr-2 align-top">:</td>
                    <td class="font-semibold">Penawaran Harga Pertalite Industri<br>Pertamina Patra Niaga</td>
                </tr>
            </tbody>
        </table>
    </section>

    
    <section class="mb-4 text-sm font-semibold">
        <p>Kepada Yth.</p>
        <p class="font-bold"><?php echo e($record->customer?->nama); ?></p>
        <!--[if BLOCK]><![endif]--><?php if($record->opsional_pic): ?>
            <p>Up: <?php echo e($record->opsional_pic); ?></p>
        <?php elseif($record->customer?->pic_nama): ?>
            <p>Up: <?php echo e($record->customer->pic_nama); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <p>Di –</p>
        <p class="ml-4">Tempat</p>
    </section>

    
    <section class="mb-6 text-sm leading-relaxed">
        <p class="mb-0">Salam hormat,</p>
        <p>
            Sehubungan dengan adanya informasi kebutuhan BBM Pertalite industri untuk PT. Amico Putera Perkasa, maka
            bersama ini kami kirimkan surat penawaran harga untuk periode
            <span class="font-bold"><?php echo e($record->sph_date->format('d M Y')); ?></span> s/d <span
                class="font-bold"><?php echo e($record->valid_until_date->format('d M Y')); ?></span>.
        </p>
    </section>

    <section class="mb-8 text-sm">
        <p class="mb-2 font-bold">Produk BBM yang kami tawarkan yaitu :</p>
        <div class="pl-4">
            <table class="w-full">
                <tbody>
                    <tr>
                        <td class="w-4 pr-2 align-top">1.</td>
                        <td class="font-semibold pr-2 w-28">Nama Produk</td>
                        
                        
                        <td>:
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $record->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($detail->item->name); ?><!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>
                                    ,
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">2.</td>
                        <td class="font-semibold pr-2">Spesifikasi</td>
                        <td>: Standar Dirjen Migas & International ASTM</td>
                    </tr>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">3.</td>
                        <td class="font-semibold pr-2">Legalitas</td>
                        <td>: Full Document/ Resmi dari PT. Pertamina Patra Niaga</td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">4.</td>
                        <td class="font-semibold pr-2">Sumber</td>
                        <td>: Pertamina Patra Niaga</td>
                    </tr>
                    <tr>
                        <td class="pr-2 align-top">5.</td>
                        <td class="font-semibold pr-2">TKDN</td>
                        <td>: 99,93% berdasarkan laporan Kementrian Perindustrian RI</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    
    <section class="mb-2">
        <h2 class="text-sm font-semibold mb-2">Harga Penawaran yang kami berikan yaitu:</h2>
        <table class="w-full text-left text-sm border-collapse border border-gray-400">
            <thead>
                <tr class="bg-gray-100">
                    <th class="p-2 border border-gray-300 text-center">No</th>
                    <th class="p-2 border border-gray-300">Rincian</th>
                    <th class="p-2 border border-gray-300 text-right">Harga/Liter</th>
                </tr>
            </thead>
            <tbody>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $record->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $idx => $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <!-- harga dasar -->
                    <tr class="border-t border-gray-300">
                        <td class="p-2 border border-gray-300 text-center"><?php echo e($idx + 1); ?></td>
                        <td class="p-2 border border-gray-300">Dasar BBM</td>
                        <td class="p-2 border border-gray-300 text-right">
                            <?php echo e(Formatter::currency($detail->harga_dasar, $locale)); ?></td>
                    </tr>

                    <!-- ppn/vat -->
                    <!--[if BLOCK]><![endif]--><?php if($detail->show_ppn): ?>
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">PPN BBM 11%</td>
                            <td class="p-2 border border-gray-300 text-right">
                                <?php echo e(Formatter::currency($detail->ppn, $locale)); ?></td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- oat -->
                    <!--[if BLOCK]><![endif]--><?php if($detail->show_oat): ?>
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">OAT</td>
                            <td class="p-2 border border-gray-300 text-right">
                                <?php echo e(Formatter::currency($detail->oat, $locale)); ?></td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- pbbkb -->
                    <!--[if BLOCK]><![endif]--><?php if($detail->show_pbbkb): ?>
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">PBBKB</td>
                            <td class="p-2 border border-gray-300 text-right">
                                <?php echo e(Formatter::currency($detail->pbbkb, $locale)); ?></td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- total price -->
                    <tr class="font-bold bg-gray-100">
                        <td colspan="2" class="p-2 border border-gray-300">Total Penawaran</td>
                        <td class="p-2 border border-gray-300 text-right">
                            <?php echo e(Formatter::currency($detail->price, $locale)); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </tbody>
        </table>
    </section>



    
    <section class="mb-8 text-sm">
        <h2 class="font-semibold">Syarat dan Ketentuan</h2>
        <ol class="list-none space-y-0 mb-2">
            <li class="flex"><span class="mr-2">1.</span><span>Penawaran harga ini berlaku pada periode
                    <?php echo e($record->sph_date->format('d M Y')); ?> -
                    <?php echo e($record->valid_until_date->format('d M Y')); ?>.</span></li>
            <li class="flex"><span class="mr-2">2.</span>
                <div><span>Pembayaran tagihan CASH hari setelah Dokumen diterima melalui transfer ke Bank
                        <?php echo e($record->paymentMethod->bank_name); ?></span>
                    <div class="font-semibold ml-4">No Rekening: <?php echo e($record->paymentMethod->account_number); ?>

                        <!--[if BLOCK]><![endif]--><?php if($record->paymentMethod->account_name): ?>
                            A.n. <?php echo e($record->paymentMethod->account_name); ?>

                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </li>
            <li class="flex"><span class="mr-2">3.</span><span>PO kami terima minimal 3 (Tiga) hari (via email atau
                    WA) sebelum pengiriman.</span></li>
            <li class="flex"><span class="mr-2">4.</span><span>Untuk kondisi mendesak/urgent dapat berkoordinasi
                    langsung sebelum pukul 12.00 Wib. pada hari yang sama.</span></li>
        </ol>
        <span>Demikian surat penawaran ini kami sampaikan dan atas perhatian kami ucapkan terima kasih yang
            sebesar-besarnya</span>
    </section>

    
    <section class="pt-8 flex justify-end mt-6 mb-6">
        <div class="text-center">
            <p class="text-sm">Hormat kami</p>
            <p class="text-sm">PT Lintas Riau Prima</p>

            <!--[if BLOCK]><![endif]--><?php if($signer): ?>
                <!--[if BLOCK]><![endif]--><?php if($record->status === 'approved'): ?>
                    
                    <?php if (isset($component)) { $__componentOriginalaa69033367186bcff2ca14e3c75e4402 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa69033367186bcff2ca14e3c75e4402 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.signature-mounter','data' => ['user' => $signer]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('signature-mounter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($signer)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaa69033367186bcff2ca14e3c75e4402)): ?>
<?php $attributes = $__attributesOriginalaa69033367186bcff2ca14e3c75e4402; ?>
<?php unset($__attributesOriginalaa69033367186bcff2ca14e3c75e4402); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaa69033367186bcff2ca14e3c75e4402)): ?>
<?php $component = $__componentOriginalaa69033367186bcff2ca14e3c75e4402; ?>
<?php unset($__componentOriginalaa69033367186bcff2ca14e3c75e4402); ?>
<?php endif; ?>
                <?php else: ?>
                    
                    <div style="height: 100px;"></div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                
                <p class="text-sm font-bold underline"><?php echo e($signer->name ?? 'Tanpa Nama'); ?></p>
                <p class="text-xs text-gray-600"><?php echo e($signer->getPosition('id') ?? 'Tanpa Posisi'); ?></p>
            <?php else: ?>
                
                <div style="height: 100px;"></div>
                <p class="text-sm font-bold underline">No Data</p>
                <p class="text-xs text-gray-600">No Data</p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </section>

    
    <footer class="mt-16 pt-4 border-t-4 border-blue-800 flex justify-between items-center text-xs">
        <div class="flex items-center space-x-2">
            <!--[if BLOCK]><![endif]--><?php if(isset($isoCertifications)): ?>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $isoCertifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <img src="<?php echo e($cert->logo_url); ?>" alt="<?php echo e($cert->name); ?>" class="h-10">
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div class="text-center">
            <p class="font-bold">PT. LINTAS RIAU PRIMA</p>
            <!--[if BLOCK]><![endif]--><?php if($record->letterSetting): ?>
                <p><?php echo e($record->letterSetting->address); ?></p>
                <p><?php echo e($record->letterSetting->city); ?>, <?php echo e($record->letterSetting->province); ?>.
                    <?php echo e($record->letterSetting->postal_code); ?></p>
            <?php else: ?>
                <p>Jl. Mesjid Al Furqon No. 26</p>
                <p>Pekanbaru, Riau. 28144</p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div class="text-left">
            <!--[if BLOCK]><![endif]--><?php if($record->letterSetting): ?>
                <p>
                    <?php echo e($record->letterSetting->phone_number ?? '0761-22369'); ?> -
                </p>
                <p>
                    <?php echo e($record->letterSetting->email ?? '<EMAIL>'); ?><br>
                </p>
                <p>
                    <?php echo e($record->letterSetting->website ?? 'www.lintasriauprima.com'); ?>

                </p>
            <?php else: ?>
                <p>☎️ 0761-22369</p>
                <p>✉️ <EMAIL></p>
                <p>🌐 www.lintasriauprima.com</p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        </div>
    </footer>
</div>
<?php /**PATH D:\laragon\www\lrp\resources\views/sph/sph-preview-id.blade.php ENDPATH**/ ?>