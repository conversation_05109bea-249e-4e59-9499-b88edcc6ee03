<?php

namespace App\Filament\Resources\TransaksiPenjualanResource\Pages;

use App\Filament\Resources\TransaksiPenjualanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditTransaksiPenjualan extends EditRecord
{
    protected static string $resource = TransaksiPenjualanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load the penjualan details for editing
        $record = $this->getRecord();
        $data['penjualanDetails'] = $record->penjualanDetails->map(function ($detail) {
            return [
                'id_item' => $detail->id_item,
                'volume_item' => $detail->volume_item,
                'harga_jual' => $detail->harga_jual,
                'item_info' => $detail->item ? $detail->item->kode . ' - ' . $detail->item->name : '',
                'satuan_info' => $detail->item?->satuan?->nama ?? '',
                'subtotal' => $detail->volume_item * $detail->harga_jual,
            ];
        })->toArray();

        // Set SPH input type berdasarkan data yang ada
        if ($record->sph_id) {
            $data['sph_input_type'] = 'select';
        } else {
            $data['sph_input_type'] = 'manual';
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure tipe remains 'dagang'
        $data['tipe'] = 'dagang';

        // Handle SPH data berdasarkan input type
        if (isset($data['sph_input_type'])) {
            if ($data['sph_input_type'] === 'select' && isset($data['sph_id'])) {
                // Jika pilih dari SPH yang ada, pastikan nomor_sph terisi dari SPH
                $sph = \App\Models\Sph::find($data['sph_id']);
                if ($sph) {
                    $data['nomor_sph'] = $sph->sph_number;
                }
            } elseif ($data['sph_input_type'] === 'manual') {
                // Jika input manual, clear sph_id
                $data['sph_id'] = null;
            }

            // Remove sph_input_type karena tidak disimpan di database
            unset($data['sph_input_type']);
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Show success notification with item count
        $record = $this->getRecord();
        $itemCount = $record->penjualanDetails()->count();

        if ($itemCount === 0) {
            Notification::make()
                ->title('Peringatan')
                ->body('Transaksi penjualan tidak memiliki item. Silakan tambahkan minimal satu item.')
                ->warning()
                ->send();
        } else {
            Notification::make()
                ->title('Berhasil')
                ->body("Transaksi penjualan berhasil diperbarui dengan {$itemCount} item.")
                ->success()
                ->send();
        }
    }
}
