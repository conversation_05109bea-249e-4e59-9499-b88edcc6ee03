<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LetterSettingResource\Pages;
use App\Models\LetterSetting;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

// Import components for cleaner code
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;

class LetterSettingResource extends Resource
{
    protected static ?string $model = LetterSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Pengaturan Surat';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Detail Pengaturan')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Pengaturan')
                            ->helperText("Contoh: 'Pekanbaru - Indonesian', 'Batam - English'")
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->columnSpanFull(),
                        TextInput::make('city')
                            ->label('Kota Penerbitan')
                            ->required()
                            ->maxLength(255),
                        Select::make('locale')
                            ->label('Bahasa')
                            ->options([
                                'id' => 'Indonesian (ID)',
                                'en' => 'English (EN)',
                            ])
                            ->required(),
                        Textarea::make('address')
                            ->label('Alamat Kantor')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                        TextInput::make('phone_number')
                            ->label('Nomor Telepon')
                            ->tel(),
                        TextInput::make('email')
                            ->label('Alamat Email')
                            ->email(),
                        TextInput::make('website')
                            ->label('Website')
                            ->url(),
                        Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                        Toggle::make('is_default')
                            ->label('Jadikan Default')
                            ->helperText('Jika ini aktif, pengaturan lain akan otomatis non-aktif.'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Pengaturan')
                    ->searchable()
                    ->weight('bold'),
                TextColumn::make('city')
                    ->label('Kota')
                    ->searchable(),
                TextColumn::make('locale')
                    ->label('Bahasa')
                    ->badge(),
                IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean(),
                ToggleColumn::make('is_active')
                    ->label('Aktif'),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLetterSettings::route('/'),
            'create' => Pages\CreateLetterSetting::route('/create'),
            'edit' => Pages\EditLetterSetting::route('/{record}/edit'),
            'view' => Pages\ViewLetterSetting::route('/{record}'),
        ];
    }    
}
