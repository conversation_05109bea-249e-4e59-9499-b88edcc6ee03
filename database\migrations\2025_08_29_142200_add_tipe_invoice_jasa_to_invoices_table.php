<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->enum('tipe_invoice_jasa', ['jasa_angkut_satuan', 'tagihan_pola'])
                ->nullable()
                ->after('id_transaksi')
                ->comment('Tipe invoice untuk transaksi jasa');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->dropColumn('tipe_invoice_jasa');
        });
    }
};
