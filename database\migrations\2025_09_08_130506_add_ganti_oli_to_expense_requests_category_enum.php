<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'ganti_oli' to the category enum
        DB::statement("ALTER TABLE expense_requests MODIFY COLUMN category ENUM(
            'tank_truck_maintenance',
            'license_fee',
            'business_travel',
            'utilities',
            'other',
            'ganti_oli'
        )");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'ganti_oli' from the category enum
        DB::statement("ALTER TABLE expense_requests MODIFY COLUMN category ENUM(
            'tank_truck_maintenance',
            'license_fee',
            'business_travel',
            'utilities',
            'other'
        )");
    }
};
