<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\HardcodedSignerService;
use App\Models\User;
use App\Models\Sph;

class HardcodedSignerServiceTest extends TestCase
{
    public function test_get_default_signer_returns_null_when_no_sph()
    {
        $signer = HardcodedSignerService::getDefaultSigner();
        $this->assertNull($signer);
    }

    public function test_get_default_signer_returns_null_when_sph_has_no_creator()
    {
        $sph = new Sph();
        $signer = HardcodedSignerService::getDefaultSigner($sph);
        $this->assertNull($signer);
    }

    public function test_get_default_signer_returns_creator_when_sph_has_creator()
    {
        // Create a mock SPH with creator
        $user = new User();
        $user->name = 'Test User';
        $user->id = 1;

        $sph = new Sph();
        $sph->setRelation('createdBy', $user);

        $signer = HardcodedSignerService::getDefaultSigner($sph);

        $this->assertNotNull($signer);
        $this->assertEquals('Test User', $signer->name);
        $this->assertEquals(1, $signer->id);
    }
}
