<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Entitas extends Model
{
    use SoftDeletes;

    protected $table = 'entitas';

    protected $fillable = [
        'id_entitas_tipe',
        'id_kota',

        'nama',
        'alamat',
        'phone',

        'lattitude',
        'longitude',
        'radius',
        'enable_geofencing',

        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function entitasTipe()
    {
        return $this->belongsTo(EntitasTipe::class, 'id_entitas_tipe');
    }

    public function users()
    {
        return $this->hasMany(User::class, 'id_entitas');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get formatted coordinates
     */
    public function getCoordinatesAttribute(): ?string
    {
        if ($this->lattitude && $this->longitude) {
            return "{$this->lattitude}, {$this->longitude}";
        }
        return null;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($entitas) {
            if (empty($entitas->created_by)) {
                $entitas->created_by = Auth::id();
            }
        });
    }
}
