@php
    // The $record variable is passed in from the View::make() call.

    // --- UPDATED: The query now specifically fetches only the two required ISOs ---
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();

    // SIGNATURE SET PART - Use hardcoded director
    use App\Services\HardcodedSignerService;
    $signer = HardcodedSignerService::getDefaultSigner($record ?? null);
@endphp
