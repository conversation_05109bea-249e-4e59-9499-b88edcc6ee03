@props([
    'title',
    'headers' => [],
    'data' => [],
    'actions' => null
])

<div class="dashboard-table">
    <div class="dashboard-table-header">
        <div class="flex items-center justify-between">
            <h3 class="dashboard-table-title">{{ $title }}</h3>
            {{ $actions }}
        </div>
    </div>
    <div class="dashboard-table-content">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            @if(count($headers) > 0)
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        @foreach($headers as $header)
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                {{ $header }}
                            </th>
                        @endforeach
                    </tr>
                </thead>
            @endif
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {{ $slot }}
            </tbody>
        </table>
    </div>
</div>
