<?php

namespace Tests\Feature;

use App\Models\PaymentMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentMethodEnhancementTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function payment_method_can_have_multiple_entries_with_same_type_but_different_display_names()
    {
        // Create two bank transfer methods with different display names
        $method1 = PaymentMethod::create([
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Kantor Pusat',
            'bank_name' => 'Bank BNI',
            'account_number' => '**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'is_active' => true,
        ]);

        $method2 = PaymentMethod::create([
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Cabang Pekanbaru',
            'bank_name' => 'Bank BNI',
            'account_number' => '**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('payment_methods', [
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Kantor Pusat',
        ]);

        $this->assertDatabaseHas('payment_methods', [
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Cabang Pekanbaru',
        ]);

        // Verify both records exist
        $this->assertEquals(2, PaymentMethod::where('method_name', 'bank_transfer')->count());
    }

    /** @test */
    public function payment_method_display_name_must_be_unique()
    {
        // Create first payment method
        PaymentMethod::create([
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Kantor Pusat',
            'bank_name' => 'Bank BNI',
            'account_number' => '**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'is_active' => true,
        ]);

        // Attempt to create another with the same display name should fail
        $this->expectException(\Illuminate\Database\QueryException::class);

        PaymentMethod::create([
            'method_name' => 'cash', // Different type
            'method_display_name' => 'BNI Kantor Pusat', // Same display name
            'is_active' => true,
        ]);
    }

    /** @test */
    public function cash_payment_method_can_be_created_without_bank_details()
    {
        $cashMethod = PaymentMethod::create([
            'method_name' => 'cash',
            'method_display_name' => 'Kas Kecil Kantor',
            'bank_name' => null,
            'account_number' => null,
            'account_name' => 'Kasir Kantor',
            'notes' => 'Untuk pembayaran tunai operasional',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('payment_methods', [
            'method_name' => 'cash',
            'method_display_name' => 'Kas Kecil Kantor',
            'bank_name' => null,
            'account_number' => null,
        ]);

        $this->assertEquals('cash', $cashMethod->method_name);
        $this->assertNull($cashMethod->bank_name);
        $this->assertNull($cashMethod->account_number);
    }

    /** @test */
    public function payment_method_labels_include_all_expected_types()
    {
        $expectedTypes = [
            'bank_transfer',
            'cash',
            'check',
            'giro',
            'credit_card',
            'debit_card',
            'e_wallet',
            'virtual_account',
            'other'
        ];

        foreach ($expectedTypes as $type) {
            $this->assertArrayHasKey($type, PaymentMethod::METHOD_NAME_LABELS);
        }

        // Verify we have at least the expected number of types
        $this->assertGreaterThanOrEqual(count($expectedTypes), count(PaymentMethod::METHOD_NAME_LABELS));
    }

    /** @test */
    public function multiple_cash_methods_can_exist_with_different_purposes()
    {
        $cashOffice = PaymentMethod::create([
            'method_name' => 'cash',
            'method_display_name' => 'Kas Kecil Kantor',
            'account_name' => 'Kasir Kantor',
            'notes' => 'Untuk operasional kantor',
            'is_active' => true,
        ]);

        $cashField = PaymentMethod::create([
            'method_name' => 'cash',
            'method_display_name' => 'Kas Lapangan',
            'account_name' => 'Supervisor Lapangan',
            'notes' => 'Untuk operasional lapangan',
            'is_active' => true,
        ]);

        $this->assertEquals(2, PaymentMethod::where('method_name', 'cash')->count());
        $this->assertNotEquals($cashOffice->method_display_name, $cashField->method_display_name);
    }

    /** @test */
    public function payment_method_can_store_notes_for_additional_information()
    {
        $method = PaymentMethod::create([
            'method_name' => 'e_wallet',
            'method_display_name' => 'OVO Corporate',
            'account_number' => '08**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'notes' => 'Khusus untuk pembayaran vendor kecil dan reimburse karyawan',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('payment_methods', [
            'method_display_name' => 'OVO Corporate',
            'notes' => 'Khusus untuk pembayaran vendor kecil dan reimburse karyawan',
        ]);
    }

    /** @test */
    public function payment_method_can_be_linked_to_chart_of_accounts()
    {
        // Create a COA account
        $akun = \App\Models\Akun::create([
            'kode_akun' => '1150',
            'nama_akun' => 'Test Bank Account',
            'kategori_akun' => 'Aset',
            'tipe_akun' => 'Debit',
            'saldo_awal' => 0,
        ]);

        // Create payment method linked to COA
        $method = PaymentMethod::create([
            'method_name' => 'bank_transfer',
            'method_display_name' => 'Test Bank Method',
            'akun_id' => $akun->id,
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'is_active' => true,
        ]);

        // Test the relationship
        $this->assertEquals($akun->id, $method->akun_id);
        $this->assertEquals('Test Bank Account', $method->akun->nama_akun);
        $this->assertEquals('1150', $method->akun->kode_akun);

        // Test reverse relationship
        $this->assertEquals($method->id, $akun->paymentMethod->id);
    }

    /** @test */
    public function payment_method_coa_relationship_is_nullable()
    {
        // Create payment method without COA
        $method = PaymentMethod::create([
            'method_name' => 'cash',
            'method_display_name' => 'Test Cash Method',
            'is_active' => true,
        ]);

        $this->assertNull($method->akun_id);
        $this->assertNull($method->akun);
    }
}
