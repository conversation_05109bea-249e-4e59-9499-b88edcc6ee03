<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\DB;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This will populate the payment_methods table with default values.
     */
    public function run(): void
    {
        // Use DB::statement to disable foreign key checks temporarily, which can be safer for seeding.
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        PaymentMethod::truncate(); // Optional: Clears the table before seeding
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $paymentMethods = [
            // Bank Transfer Methods - Using Real Account Numbers
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Giro Bank BNI',
                'bank_name' => 'Bank BNI',
                'account_number' => '*********',
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening giro utama Bank BNI',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Giro Bank Mandiri',
                'bank_name' => 'Bank Mandiri',
                'account_number' => '*************',
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening giro Bank Mandiri',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Giro Bank CIMB Niaga',
                'bank_name' => 'Bank CIMB Niaga',
                'account_number' => '************',
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening giro Bank CIMB Niaga',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Giro Bank BSI',
                'bank_name' => 'Bank BSI',
                'account_number' => '**********',
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Rekening giro Bank BSI',
                'is_active' => true,
            ],
            [
                'method_name' => 'bank_transfer',
                'method_display_name' => 'Giro USD BNI',
                'bank_name' => 'Bank BNI',
                'account_number' => '**********',
                'account_name' => 'PT Lintas Riau Prima',
                'notes' => 'Rekening giro USD Bank BNI',
                'is_active' => true,
            ],

            // Cash Methods
            [
                'method_name' => 'cash',
                'method_display_name' => 'Kas Kecil Kantor',
                'bank_name' => null,
                'account_number' => null,
                'account_name' => 'Kasir Kantor',
                'notes' => 'Untuk pembayaran tunai operasional kantor',
                'is_active' => true,
            ],
            [
                'method_name' => 'cash',
                'method_display_name' => 'Kas Lapangan',
                'bank_name' => null,
                'account_number' => null,
                'account_name' => 'Supervisor Lapangan',
                'notes' => 'Untuk pembayaran tunai di lapangan',
                'is_active' => true,
            ],

            // Other Payment Methods
            [
                'method_name' => 'check',
                'method_display_name' => 'Cek Bank BNI',
                'bank_name' => 'Bank BNI',
                'account_number' => null,
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Untuk pembayaran dengan cek',
                'is_active' => true,
            ],
            [
                'method_name' => 'giro',
                'method_display_name' => 'Giro Bank Mandiri',
                'bank_name' => 'Bank Mandiri',
                'account_number' => null,
                'account_name' => 'PT. Lintas Riau Prima',
                'notes' => 'Untuk pembayaran dengan giro',
                'is_active' => true,
            ],
        ];

        // Loop through the data and create records
        foreach ($paymentMethods as $method) {
            // Find appropriate COA based on method type
            $akunId = $this->findCOAForPaymentMethod($method);
            if ($akunId) {
                $method['akun_id'] = $akunId;
            }

            // Use updateOrCreate with method_display_name as unique key to prevent duplicates
            PaymentMethod::updateOrCreate(
                ['method_display_name' => $method['method_display_name']], // The unique key to check
                $method  // The data to insert or update
            );
        }
    }

    /**
     * Find appropriate COA for payment method
     */
    private function findCOAForPaymentMethod(array $method): ?int
    {
        $akunId = null;

        switch ($method['method_name']) {
            case 'cash':
                $akunId = \App\Models\Akun::where('kode_akun', '100')->value('id'); // Cash in Hand
                break;

            case 'bank_transfer':
                $bankName = strtolower($method['bank_name'] ?? '');
                $accountNumber = $method['account_number'] ?? '';

                // Map based on specific account numbers
                if ($accountNumber === '*********') {
                    $akunId = \App\Models\Akun::where('kode_akun', '102')->value('id'); // Giro Bank BNI
                } elseif ($accountNumber === '*************') {
                    $akunId = \App\Models\Akun::where('kode_akun', '101')->value('id'); // Giro Bank Mandiri
                } elseif ($accountNumber === '************') {
                    $akunId = \App\Models\Akun::where('kode_akun', '104')->value('id'); // Giro Bank CIMB Niaga
                } elseif ($accountNumber === '**********') {
                    $akunId = \App\Models\Akun::where('kode_akun', '105')->value('id'); // Giro Bank BSI
                } elseif ($accountNumber === '**********') {
                    $akunId = \App\Models\Akun::where('kode_akun', '103')->value('id'); // Giro USD BNI
                } elseif (str_contains($bankName, 'bni')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '102')->value('id'); // Default BNI
                } elseif (str_contains($bankName, 'mandiri')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '101')->value('id'); // Default Mandiri
                } elseif (str_contains($bankName, 'cimb')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '104')->value('id'); // Default CIMB
                } elseif (str_contains($bankName, 'bsi')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '105')->value('id'); // Default BSI
                }
                break;

            case 'check':
                $akunId = \App\Models\Akun::where('kode_akun', '102')->value('id'); // Default to BNI
                break;

            case 'giro':
                $bankName = strtolower($method['bank_name'] ?? '');
                if (str_contains($bankName, 'mandiri')) {
                    $akunId = \App\Models\Akun::where('kode_akun', '101')->value('id'); // Giro Bank Mandiri
                } else {
                    $akunId = \App\Models\Akun::where('kode_akun', '102')->value('id'); // Default to BNI
                }
                break;
        }

        return $akunId;
    }
}
