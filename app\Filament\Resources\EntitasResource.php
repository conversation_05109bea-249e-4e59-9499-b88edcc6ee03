<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EntitasResource\Pages;
use App\Filament\Resources\EntitasResource\RelationManagers;
use App\Models\Entitas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EntitasResource extends Resource
{
    protected static ?string $model = Entitas::class;

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Entitas')
                    ->schema([
                        Forms\Components\TextInput::make('nama')
                            ->label('Nama Entitas')
                            ->placeholder('Contoh: Kantor X')
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label('No. Telepon')
                            ->placeholder('Contoh: 08123456789'),

                        Forms\Components\Textarea::make('alamat')
                            ->label('Alamat')
                            ->placeholder("Contoh:\nJl. Contoh, No. X")
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Geofencing untuk Absensi')
                    ->description('Atur lokasi dan radius untuk validasi absensi karyawan')
                    ->schema([
                        Forms\Components\Toggle::make('enable_geofencing')
                            ->label('Aktifkan Geofencing')
                            ->helperText('Jika diaktifkan, karyawan hanya bisa absen dalam radius yang ditentukan')
                            ->default(true)
                            ->live(),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('lattitude')
                                    ->label('Latitude')
                                    ->numeric()
                                    ->step(0.00000000000000001)
                                    ->placeholder('Contoh: -6.2088')
                                    ->helperText('Koordinat lattitude lokasi entitas')
                                    ->visible(fn($get) => $get('enable_geofencing')),

                                Forms\Components\TextInput::make('longitude')
                                    ->label('Longitude')
                                    ->numeric()
                                    ->step(0.00000000000000001)
                                    ->placeholder('Contoh: 106.8456')
                                    ->helperText('Koordinat longitude lokasi entitas')
                                    ->visible(fn($get) => $get('enable_geofencing')),

                                Forms\Components\TextInput::make('radius')
                                    ->label('Radius (meter)')
                                    ->numeric()
                                    ->default(100)
                                    ->minValue(10)
                                    ->maxValue(1000)
                                    ->suffix('meter')
                                    ->helperText('Jarak maksimal yang diperbolehkan untuk absensi')
                                    ->visible(fn($get) => $get('enable_geofencing')),
                            ]),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('geocode_address')
                                ->label('📍 Dapatkan Koordinat dari Alamat')
                                ->color('info')
                                ->action(function ($set, $get) {
                                    $alamat = $get('alamat');

                                    if (!$alamat) {
                                        \Filament\Notifications\Notification::make()
                                            ->title('Alamat Diperlukan')
                                            ->body('Mohon isi alamat terlebih dahulu untuk mendapatkan koordinat otomatis.')
                                            ->warning()
                                            ->send();
                                        return;
                                    }

                                    // Simple geocoding using Nominatim (OpenStreetMap)
                                    try {
                                        $encodedAddress = urlencode($alamat);
                                        $url = "https://nominatim.openstreetmap.org/search?format=json&q={$encodedAddress}&limit=1";

                                        $context = stream_context_create([
                                            'http' => [
                                                'header' => "User-Agent: Viera-Filament-App/1.0\r\n"
                                            ]
                                        ]);

                                        $response = file_get_contents($url, false, $context);
                                        $data = json_decode($response, true);

                                        if (!empty($data) && isset($data[0]['lat']) && isset($data[0]['lon'])) {
                                            $lat = round((float)$data[0]['lat'], 8);
                                            $lon = round((float)$data[0]['lon'], 8);

                                            $set('lattitude', $lat);
                                            $set('longitude', $lon);

                                            \Filament\Notifications\Notification::make()
                                                ->title('Koordinat Berhasil Ditemukan!')
                                                ->body("Latitude: {$lat}, Longitude: {$lon}")
                                                ->success()
                                                ->send();
                                        } else {
                                            \Filament\Notifications\Notification::make()
                                                ->title('Koordinat Tidak Ditemukan')
                                                ->body('Tidak dapat menemukan koordinat untuk alamat tersebut. Mohon input manual.')
                                                ->warning()
                                                ->send();
                                        }
                                    } catch (\Exception $e) {
                                        \Filament\Notifications\Notification::make()
                                            ->title('Error Geocoding')
                                            ->body('Terjadi kesalahan saat mencari koordinat: ' . $e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                })
                                ->visible(fn($get) => $get('enable_geofencing')),
                        ])->visible(fn($get) => $get('enable_geofencing')),

                        Forms\Components\Placeholder::make('geofencing_info')
                            ->label('')
                            ->content('💡 Tips: Gunakan tombol "Dapatkan Koordinat dari Alamat" atau input manual dari Google Maps (klik kanan pada lokasi → pilih koordinat).')
                            ->visible(fn($get) => $get('enable_geofencing')),

                        Forms\Components\Placeholder::make('map_preview')
                            ->label('Preview Lokasi')
                            ->content(function ($get) {
                                $lattitude = $get('lattitude');
                                $longitude = $get('longitude');
                                $radius = $get('radius') ?? 100;

                                if (!$lattitude || !$longitude) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div style="background: #f8fafc; border: 2px dashed #cbd5e1; border-radius: 8px; padding: 40px; text-align: center; color: #64748b;">
                                            <div style="font-size: 48px; margin-bottom: 16px;">🗺️</div>
                                            <div style="font-weight: 600; margin-bottom: 8px;">Preview Maps</div>
                                            <div style="font-size: 14px;">Masukkan lattitude dan longitude untuk melihat preview lokasi</div>
                                        </div>
                                    ');
                                }

                                // Validate coordinates
                                if (
                                    !is_numeric($lattitude) || !is_numeric($longitude) ||
                                    $lattitude < -90 || $lattitude > 90 ||
                                    $longitude < -180 || $longitude > 180
                                ) {
                                    return new \Illuminate\Support\HtmlString('
                                        <div style="background: #fef2f2; border: 2px solid #fecaca; border-radius: 8px; padding: 20px; text-align: center; color: #dc2626;">
                                            <div style="font-size: 24px; margin-bottom: 8px;">⚠️</div>
                                            <div style="font-weight: 600;">Koordinat Tidak Valid</div>
                                            <div style="font-size: 14px; margin-top: 4px;">
                                                Latitude: -90 to 90<br>
                                                Longitude: -180 to 180
                                            </div>
                                        </div>
                                    ');
                                }

                                $googleMapsUrl = "https://www.google.com/maps?q={$lattitude},{$longitude}";
                                $openStreetMapUrl = "https://www.openstreetmap.org/export/embed.html?bbox=" .
                                    ($longitude - 0.005) . "%2C" . ($lattitude - 0.005) . "%2C" .
                                    ($longitude + 0.005) . "%2C" . ($lattitude + 0.005) .
                                    "&amp;layer=mapnik&amp;marker={$lattitude}%2C{$longitude}";

                                return new \Illuminate\Support\HtmlString('
                                    <div style="border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; background: white;">
                                        <!-- Map Header -->
                                        <div style="background: #f8fafc; padding: 12px 16px; border-bottom: 1px solid #e5e7eb;">
                                            <div style="display: flex; justify-content: between; align-items: center; flex-wrap: wrap; gap: 12px;">
                                                <div>
                                                    <div style="font-weight: 600; color: #1e293b; margin-bottom: 4px;">📍 Preview Lokasi</div>
                                                    <div style="font-size: 13px; color: #64748b; font-family: monospace;">
                                                        ' . $lattitude . ', ' . $longitude . ' (Radius: ' . $radius . 'm)
                                                    </div>
                                                </div>
                                                <div style="display: flex; gap: 8px;">
                                                    <a href="' . $googleMapsUrl . '" target="_blank"
                                                       style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 12px;
                                                              background: #3b82f6; color: white; text-decoration: none; border-radius: 6px;
                                                              font-size: 12px; font-weight: 500;">
                                                        🗺️ Google Maps
                                                    </a>
                                                    <button type="button" onclick="copyCoordinates(\'' . $lattitude . ', ' . $longitude . '\')"
                                                            style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 12px;
                                                                   background: #10b981; color: white; border: none; border-radius: 6px;
                                                                   font-size: 12px; font-weight: 500; cursor: pointer;">
                                                        📋 Copy
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Map Container -->
                                        <div style="position: relative; height: 300px;">
                                            <iframe src="' . $openStreetMapUrl . '"
                                                    style="width: 100%; height: 100%; border: none;"
                                                    allowfullscreen>
                                            </iframe>

                                            <!-- Radius Indicator -->
                                            <div style="position: absolute; top: 12px; left: 12px;
                                                        background: rgba(59, 130, 246, 0.9); color: white;
                                                        padding: 6px 10px; border-radius: 6px; font-size: 12px; font-weight: 500;">
                                                📏 Radius: ' . $radius . 'm
                                            </div>

                                            <!-- Coordinates Display -->
                                            <div style="position: absolute; bottom: 12px; left: 12px;
                                                        background: rgba(0, 0, 0, 0.8); color: white;
                                                        padding: 6px 10px; border-radius: 6px; font-size: 11px; font-family: monospace;">
                                                📍 ' . $lattitude . ', ' . $longitude . '
                                            </div>
                                        </div>

                                        <!-- Map Footer -->
                                        <div style="background: #f8fafc; padding: 12px 16px; border-top: 1px solid #e5e7eb;">
                                            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 8px;">
                                                <div style="font-size: 12px; color: #64748b;">
                                                    🎯 Area absensi dalam radius ' . $radius . ' meter dari titik ini
                                                </div>
                                                <div style="font-size: 11px; color: #9ca3af;">
                                                    Powered by OpenStreetMap
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <script>
                                    function copyCoordinates(coords) {
                                        navigator.clipboard.writeText(coords).then(function() {
                                            // Create temporary notification
                                            const notification = document.createElement("div");
                                            notification.style.cssText = `
                                                position: fixed; top: 20px; right: 20px; z-index: 9999;
                                                background: #10b981; color: white; padding: 12px 16px;
                                                border-radius: 8px; font-size: 14px; font-weight: 500;
                                                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                            `;
                                            notification.textContent = "📋 Koordinat berhasil dicopy!";
                                            document.body.appendChild(notification);

                                            setTimeout(() => {
                                                notification.remove();
                                            }, 2000);
                                        });
                                    }
                                    </script>
                                ');
                            })
                            ->visible(fn($get) => $get('enable_geofencing'))
                            ->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama Entitas')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('alamat')
                    ->label('Alamat')
                    ->limit(30)
                    ->tooltip(fn($record) => $record->alamat),

                Tables\Columns\TextColumn::make('coordinates')
                    ->label('Koordinat')
                    ->getStateUsing(fn($record) => $record->coordinates ?? '—')
                    ->copyable()
                    ->tooltip('Klik untuk copy koordinat'),

                Tables\Columns\TextColumn::make('radius')
                    ->label('Radius')
                    ->getStateUsing(fn($record) => $record->radius ? $record->radius . 'm' : '—')
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('enable_geofencing')
                    ->label('Geofencing')
                    ->boolean()
                    ->trueIcon('heroicon-o-shield-check')
                    ->falseIcon('heroicon-o-shield-exclamation')
                    ->trueColor('success')
                    ->falseColor('warning')
                    ->tooltip(fn($record) => $record->enable_geofencing ? 'Geofencing aktif' : 'Geofencing nonaktif'),

                Tables\Columns\TextColumn::make('karyawan_count')
                    ->label('Karyawan')
                    ->counts('users')
                    ->badge()
                    ->color('info')
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('nama');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEntitas::route('/'),
            'create' => Pages\CreateEntitas::route('/create'),
            'edit' => Pages\EditEntitas::route('/{record}/edit'),
        ];
    }
}
