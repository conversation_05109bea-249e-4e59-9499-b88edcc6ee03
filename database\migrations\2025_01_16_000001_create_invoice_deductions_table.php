<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Creates the invoice_deductions table for managing deduction history for invoices.
     */
    public function up(): void
    {
        Schema::create('invoice_deductions', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to invoice
            $table->foreignId('invoice_id')
                ->constrained('invoice')
                ->onDelete('cascade');
            
            // Deduction details
            $table->enum('deduction_type', ['ppn', 'pph', 'losis', 'admin_bank'])
                ->comment('Type of deduction: ppn, pph, losis, admin_bank');
            
            $table->decimal('amount', 15, 2)
                ->comment('Deduction amount in IDR');
            
            $table->decimal('percentage', 5, 2)
                ->nullable()
                ->comment('Deduction percentage if applicable');
            
            $table->string('description')
                ->nullable()
                ->comment('Description of the deduction');
            
            $table->datetime('deduction_date')
                ->comment('Date when deduction was applied');
            
            // Reference and notes
            $table->string('reference_number')
                ->nullable()
                ->comment('Reference number for the deduction');
            
            $table->text('notes')
                ->nullable()
                ->comment('Additional notes about the deduction');
            
            // Audit fields
            $table->foreignId('created_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->foreignId('updated_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['invoice_id', 'deduction_type']);
            $table->index(['deduction_date']);
            $table->index(['deduction_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_deductions');
    }
};
