<?php

namespace App\Services;

use App\Models\Entitas;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\User;

class GeofencingService
{
    /**
     * Validate if user can perform attendance based on location
     *
     * @param Karyawan $karyawan
     * @param float $userLat
     * @param float $userLon
     * @return array
     */
    public static function validateAttendanceLocation(User $karyawan, float $userLat, float $userLon): array
    {
        // Get karyawan's entitas
        $today = \Carbon\Carbon::today()->format('Y-m-d');
        $jadwal = Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', $today)
            ->with('entitas')
            ->first();

        $entitas = $jadwal->entitas ?? $karyawan->entitas;

        if (!$entitas) {
            return [
                'allowed' => false,
                'message' => 'Entitas karyawan tidak ditemukan',
                'distance' => 0,
                'radius' => 0,
                'error' => true
            ];
        }

        // Check if geofencing is enabled for this entitas
        if (!$entitas->enable_geofencing) {
            return [
                'allowed' => true,
                'message' => 'Geofencing tidak aktif untuk entitas ini',
                'distance' => 0,
                'radius' => $entitas->radius ?? 0,
                'entitas_name' => $entitas->nama ?? 'Unknown'
            ];
        }

        // Check if entitas has coordinates
        if (!$entitas->lattitude || !$entitas->longitude) {
            return [
                'allowed' => false,
                'message' => 'Koordinat entitas belum diatur',
                'distance' => 0,
                'radius' => $entitas->radius ?? 0,
                'error' => true
            ];
        }

        // Calculate distance
        $distance = self::calculateDistance(
            $userLat,
            $userLon,
            $entitas->lattitude,
            $entitas->longitude
        );

        $radius = $entitas->radius ?? 100; // Default 100 meters

        return [
            'allowed' => $distance <= $radius,
            'message' => $distance <= $radius
                ? 'Lokasi valid untuk absensi'
                : 'Anda berada di luar radius yang diperbolehkan',
            'distance' => $distance,
            'radius' => $radius,
            'entitas_name' => $entitas->nama ?? 'Unknown',
            'entitas_coordinates' => [
                'lattitude' => $entitas->lattitude,
                'longitude' => $entitas->longitude
            ]
        ];
    }

    /**
     * Get attendance location info for karyawan
     */
    public static function getAttendanceLocationInfo(User $karyawan): array
    {
        // Prioritas 1: Cek jadwal kerja hari ini untuk mendapatkan entitas
        $today = \Carbon\Carbon::today()->format('Y-m-d');
        $jadwal = Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', $today)
            ->with('entitas')
            ->first();

        $entitas = null;
        $source = '';

        // Prioritas 1: Gunakan entitas dari jadwal kerja jika ada
        if ($jadwal && $jadwal->entitas_id) {
            $entitas = $jadwal->entitas ?? Entitas::find($jadwal->entitas_id);
            $source = 'jadwal kerja hari ini';
        }

        // Prioritas 2: Jika tidak ada entitas di jadwal, gunakan entitas karyawan
        if (!$entitas && $karyawan->entitas) {
            $entitas = $karyawan->entitas;
            $source = 'entitas default karyawan';
        }

        if (!$entitas) {
            return [
                'has_location' => false,
                'message' => 'Entitas tidak ditemukan. Pastikan Anda memiliki jadwal kerja atau entitas default.'
            ];
        }

        if (!$entitas->enable_geofencing) {
            return [
                'has_location' => false,
                'message' => "Geofencing tidak diaktifkan untuk entitas {$entitas->nama}"
            ];
        }

        if (!$entitas->lattitude || !$entitas->longitude) {
            return [
                'has_location' => false,
                'message' => "Koordinat entitas {$entitas->nama} belum diatur"
            ];
        }

        return [
            'has_location' => true,
            'entitas_name' => $entitas->nama,
            'coordinates' => $entitas->coordinates,
            'radius' => $entitas->radius,
            'address' => $entitas->alamat,
            'source' => $source,
            'message' => "Anda harus berada dalam radius {$entitas->radius}m dari {$entitas->nama} (berdasarkan {$source})"
        ];
    }

    /**
     * Calculate distance between two coordinates using Haversine formula
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float Distance in meters
     */
    private static function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371000; // Earth radius in meters

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
