<?php

namespace App\Filament\Resources\SuratResource\Pages;

use App\Filament\Resources\SuratResource;
use App\Services\SuratService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateSurat extends CreateRecord
{
    protected static string $resource = SuratResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $suratService = app(SuratService::class);
        return $suratService->createSurat(\Filament\Facades\Filament::auth()->user(), $data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
