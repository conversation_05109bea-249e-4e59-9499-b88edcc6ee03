<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    protected $table = 'invoice_items';

    protected $fillable = [
        'invoice_id',
        'item_id',
        'item_name',
        'item_description',
        'quantity',
        'unit',
        'unit_price',
        'subtotal',
        'ppn_amount',
        'total_amount',
        'include_ppn',
        'ppn_rate',
        'include_operasional',
        'operasional_rate',
        'operasional_amount',
        'include_pbbkb',
        'pbbkb_rate',
        'pbbkb_amount',
        'ongkos_angkut',
        'total_ongkos_angkut',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'ppn_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'ppn_rate' => 'decimal:2',
        'include_ppn' => 'boolean',
        'include_operasional' => 'boolean',
        'operasional_rate' => 'decimal:2',
        'operasional_amount' => 'decimal:2',
        'include_pbbkb' => 'boolean',
        'pbbkb_rate' => 'decimal:2',
        'pbbkb_amount' => 'decimal:2',
        'ongkos_angkut' => 'decimal:2',
        'total_ongkos_angkut' => 'decimal:2',
    ];

    /**
     * Default values for attributes
     */
    protected $attributes = [
        'item_id' => 8, // Default item for tagihan pola
        'item_name' => '',
        'item_description' => '',
        'quantity' => 1,
        'unit' => 'Paket',
        'unit_price' => 0,
        'subtotal' => 0,
        'ppn_amount' => 0,
        'total_amount' => 0,
        'include_ppn' => false,
        'ppn_rate' => 11,
        'include_operasional' => false,
        'operasional_rate' => 0,
        'operasional_amount' => 0,
        'include_pbbkb' => false,
        'pbbkb_rate' => 0,
        'pbbkb_amount' => 0,
        'ongkos_angkut' => 0,
        'total_ongkos_angkut' => 0,
        'notes' => '',
    ];

    /**
     * Boot method to auto-calculate amounts
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoiceItem) {
            // Ensure all required fields have default values for tagihan pola
            if (empty($invoiceItem->item_id)) {
                $invoiceItem->item_id = 8; // Use default tagihan pola item
            }
            if (empty($invoiceItem->item_name)) {
                $invoiceItem->item_name = '';
            }
            if (empty($invoiceItem->item_description)) {
                $invoiceItem->item_description = '';
            }
            if (empty($invoiceItem->quantity)) {
                $invoiceItem->quantity = 1;
            }
            if (empty($invoiceItem->unit)) {
                $invoiceItem->unit = 'Paket';
            }
            if (empty($invoiceItem->unit_price)) {
                $invoiceItem->unit_price = 0;
            }
            if (empty($invoiceItem->notes)) {
                $invoiceItem->notes = '';
            }
            if ($invoiceItem->include_ppn === null) {
                $invoiceItem->include_ppn = false;
            }
            if (empty($invoiceItem->ppn_rate)) {
                $invoiceItem->ppn_rate = 11;
            }
        });

        static::saving(function ($invoiceItem) {
            // Check if this is tagihan pola (item_id = 8)
            $isTagihanPola = $invoiceItem->item_id == 8;

            if ($isTagihanPola) {
                // For tagihan pola, use total_amount directly as subtotal
                $invoiceItem->subtotal = $invoiceItem->total_amount;
                $invoiceItem->unit_price = $invoiceItem->total_amount; // Set unit price = total amount for consistency
                $invoiceItem->total_ongkos_angkut = 0; // Not used for tagihan pola
            } else {
                // Calculate total ongkos angkut (for service type)
                if ($invoiceItem->ongkos_angkut > 0) {
                    $invoiceItem->total_ongkos_angkut = $invoiceItem->quantity * $invoiceItem->ongkos_angkut;
                    // For service type, use ongkos angkut as unit price
                    $invoiceItem->unit_price = $invoiceItem->ongkos_angkut;
                } else {
                    $invoiceItem->total_ongkos_angkut = 0;
                }

                // Calculate subtotal
                $invoiceItem->subtotal = $invoiceItem->quantity * $invoiceItem->unit_price;
            }

            // Calculate PPN amount
            if ($invoiceItem->include_ppn) {
                if ($isTagihanPola) {
                    // For tagihan pola, calculate PPN from total_amount
                    $invoiceItem->ppn_amount = $invoiceItem->total_amount * ($invoiceItem->ppn_rate / 100);
                } else {
                    // For normal items, calculate PPN from subtotal
                    $invoiceItem->ppn_amount = $invoiceItem->subtotal * ($invoiceItem->ppn_rate / 100);
                }
            } else {
                $invoiceItem->ppn_amount = 0;
            }

            // Calculate operational amount (per liter)
            if ($invoiceItem->include_operasional) {
                $invoiceItem->operasional_amount = $invoiceItem->quantity * $invoiceItem->operasional_rate;
            } else {
                $invoiceItem->operasional_amount = 0;
            }

            // Calculate PBBKB amount (per liter)
            if ($invoiceItem->include_pbbkb) {
                $invoiceItem->pbbkb_amount = $invoiceItem->quantity * $invoiceItem->pbbkb_rate;
            } else {
                $invoiceItem->pbbkb_amount = 0;
            }

            // Calculate final total amount
            if ($isTagihanPola) {
                // For tagihan pola, total_amount is manually set, only add PPN if enabled
                if ($invoiceItem->include_ppn) {
                    // Don't override total_amount, it's manually set
                    // PPN is calculated separately and shown in ppn_amount field
                } else {
                    // No PPN, total_amount remains as manually set
                }
                // Operasional and PBBKB are not used for tagihan pola
                $invoiceItem->operasional_amount = 0;
                $invoiceItem->pbbkb_amount = 0;
            } else {
                // For normal items, calculate total amount (subtotal + ppn + operasional + pbbkb)
                $invoiceItem->total_amount = $invoiceItem->subtotal +
                    $invoiceItem->ppn_amount +
                    $invoiceItem->operasional_amount +
                    $invoiceItem->pbbkb_amount;
            }
        });
    }

    /**
     * Get the invoice that owns this item.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    /**
     * Get the item associated with this invoice item.
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'item_id');
    }
}
