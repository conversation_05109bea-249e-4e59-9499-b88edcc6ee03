<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            {{ $this->form }}

            <div class="mt-6 flex justify-end">
                <x-filament::button
                    color="primary"
                    icon="heroicon-o-document-arrow-down"
                    onclick="alert('Fitur export PDF akan segera tersedia')">
                    Export PDF
                </x-filament::button>
            </div>
        </div>

        <!-- Income Statement Report -->
        @if($this->start_date && $this->end_date)
            @php
                $data = $this->getIncomeStatementData();
            @endphp

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        PT. LINTAS RIAU PRIMA
                    </h2>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
                        LAPORAN RUGI LABA
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        {{ $data['periode']['title'] }}
                    </p>
                </div>

                <div class="space-y-6">
                    @if(isset($data['sections']))
                        <!-- PEREDARAN USAHA -->
                        @if(isset($data['sections']['peredaran_usaha']))
                            @php $section = $data['sections']['peredaran_usaha']; @endphp
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    {{ $section['title'] }}
                                </h3>
                                <div class="space-y-2 ml-4">
                                    @foreach($section['items'] as $category)
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200">{{ $category['category'] }}</span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $this->formatCurrency($category['total']) }}
                                                </span>
                                            </div>
                                            @foreach($category['accounts'] as $account)
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        {{ $account['nama_akun'] }}
                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        {{ $this->formatCurrency($account['saldo']) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endforeach
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Total {{ $section['title'] }}</span>
                                        <span class="text-blue-600 dark:text-blue-400">
                                            {{ $this->formatCurrency($section['total']) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- HPP -->
                        @if(isset($data['sections']['hpp']))
                            @php $section = $data['sections']['hpp']; @endphp
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    {{ $section['title'] }}
                                </h3>
                                <div class="space-y-2 ml-4">
                                    @foreach($section['items'] as $category)
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200">{{ $category['category'] }}</span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $this->formatCurrency($category['total']) }}
                                                </span>
                                            </div>
                                            @foreach($category['accounts'] as $account)
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        {{ $account['nama_akun'] }}
                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        {{ $this->formatCurrency($account['saldo']) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endforeach
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Total {{ $section['title'] }}</span>
                                        <span class="text-red-600 dark:text-red-400">
                                            {{ $this->formatCurrency($section['total']) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- LABA BRUTO -->
                        @if(isset($data['sections']['laba_bruto']))
                            @php $section = $data['sections']['laba_bruto']; @endphp
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-blue-50 dark:bg-blue-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        {{ $section['title'] }}
                                    </span>
                                    <span class="text-xl font-bold {{ $section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $this->formatCurrency($section['total']) }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <!-- BEBAN UMUM / ADM / OPS -->
                        @if(isset($data['sections']['beban_umum']))
                            @php $section = $data['sections']['beban_umum']; @endphp
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    {{ $section['title'] }}
                                </h3>
                                <div class="space-y-2 ml-4">
                                    @foreach($section['items'] as $category)
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200">{{ $category['category'] }}</span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $this->formatCurrency($category['total']) }}
                                                </span>
                                            </div>
                                            @foreach($category['accounts'] as $account)
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        {{ $account['nama_akun'] }}
                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        {{ $this->formatCurrency($account['saldo']) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endforeach
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Jumlah</span>
                                        <span class="text-red-600 dark:text-red-400">
                                            {{ $this->formatCurrency($section['total']) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- LABA USAHA -->
                        @if(isset($data['sections']['laba_usaha']))
                            @php $section = $data['sections']['laba_usaha']; @endphp
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-green-50 dark:bg-green-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        {{ $section['title'] }}
                                    </span>
                                    <span class="text-xl font-bold {{ $section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $this->formatCurrency($section['total']) }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <!-- PENDAPATAN DAN BEBAN LUAR USAHA -->
                        @if(isset($data['sections']['pendapatan_luar_usaha']) || isset($data['sections']['beban_luar_usaha']))
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    PENDAPATAN DAN BEBAN LUAR USAHA
                                </h3>
                                
                                @if(isset($data['sections']['pendapatan_luar_usaha']))
                                    @php $section = $data['sections']['pendapatan_luar_usaha']; @endphp
                                    <div class="ml-4 mb-4">
                                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">- Pendapatan Jasa Giro</h4>
                                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">- Beban Adm Bank & Pajak Giro</h4>
                                    </div>
                                @endif
                            </div>
                        @endif

                        <!-- LABA LUAR USAHA -->
                        @if(isset($data['sections']['laba_luar_usaha']))
                            @php $section = $data['sections']['laba_luar_usaha']; @endphp
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-yellow-50 dark:bg-yellow-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        {{ $section['title'] }}
                                    </span>
                                    <span class="text-xl font-bold {{ $section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $this->formatCurrency($section['total']) }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <!-- LABA BERSIH SEBELUM PAJAK -->
                        @if(isset($data['sections']['laba_bersih_sebelum_pajak']))
                            @php $section = $data['sections']['laba_bersih_sebelum_pajak']; @endphp
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-purple-50 dark:bg-purple-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        {{ $section['title'] }}
                                    </span>
                                    <span class="text-xl font-bold {{ $section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $this->formatCurrency($section['total']) }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <!-- PAJAK PENGHASILAN -->
                        @if(isset($data['sections']['pajak']))
                            @php $section = $data['sections']['pajak']; @endphp
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    Pajak Penghasilan
                                </h3>
                                <div class="space-y-2 ml-4">
                                    @foreach($section['items'] as $category)
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200">{{ $category['category'] }}</span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $this->formatCurrency($category['total']) }}
                                                </span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- LABA BERSIH SETELAH PAJAK -->
                        @if(isset($data['sections']['laba_bersih_setelah_pajak']))
                            @php $section = $data['sections']['laba_bersih_setelah_pajak']; @endphp
                            <div class="pt-6 border-t-4 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-4 bg-gray-50 dark:bg-gray-700 px-6 rounded-lg">
                                    <span class="text-xl font-bold text-gray-900 dark:text-gray-100">
                                        {{ $section['title'] }}
                                    </span>
                                    <span class="text-2xl font-bold {{ $section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $this->formatCurrency($section['total']) }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <!-- Signature Section -->
                        <div class="pt-8 mt-8 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex justify-end">
                                <div class="text-center">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-16">
                                        Pekanbaru, {{ \Carbon\Carbon::parse($this->end_date)->format('d F Y') }}
                                    </p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        PT. Lintas Riau Prima,
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Periode Laporan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih tanggal mulai dan akhir untuk melihat laporan laba rugi
                    </p>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
