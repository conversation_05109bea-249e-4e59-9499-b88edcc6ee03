<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Make employee fields required for karyawan users.
     */
    public function up(): void
    {
        // First, update existing NULL values with unique default values
        $usersWithNullNoInduk = DB::table('users')->whereNull('no_induk')->get();
        foreach ($usersWithNullNoInduk as $user) {
            DB::table('users')->where('id', $user->id)->update(['no_induk' => 'TEMP_' . $user->id . '_' . time()]);
        }

        DB::table('users')->whereNull('hp')->update(['hp' => '+62000000000']);

        // Get default jabatan and divisi IDs (create if they don't exist)
        $defaultJabatan = DB::table('jabatan')->where('nama', 'Staff')->first();
        if (!$defaultJabatan) {
            $jabatanId = DB::table('jabatan')->insertGetId([
                'nama' => 'Staff',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $jabatanId = $defaultJabatan->id;
        }

        $defaultDivisi = DB::table('divisi')->where('nama', 'General')->first();
        if (!$defaultDivisi) {
            $divisiId = DB::table('divisi')->insertGetId([
                'nama' => 'General',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $divisiId = $defaultDivisi->id;
        }

        // Update NULL jabatan and divisi with default values
        DB::table('users')->whereNull('id_jabatan')->update(['id_jabatan' => $jabatanId]);
        DB::table('users')->whereNull('id_divisi')->update(['id_divisi' => $divisiId]);

        // Drop foreign key constraints temporarily
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['id_jabatan']);
            $table->dropForeign(['id_divisi']);
        });

        // Now make the fields non-nullable
        Schema::table('users', function (Blueprint $table) {
            // Make employee ID (no_induk) required and non-nullable
            $table->string('no_induk', 100)->nullable(false)->change();

            // Make phone number (hp) required and non-nullable
            $table->string('hp', 100)->nullable(false)->change();

            // Make jabatan (position) required and non-nullable
            $table->unsignedBigInteger('id_jabatan')->nullable(false)->change();

            // Make divisi (division) required and non-nullable
            $table->unsignedBigInteger('id_divisi')->nullable(false)->change();
        });

        // Re-add foreign key constraints with RESTRICT instead of SET NULL
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('id_jabatan')->references('id')->on('jabatan')->onDelete('restrict');
            $table->foreign('id_divisi')->references('id')->on('divisi')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     * Revert the fields back to nullable.
     */
    public function down(): void
    {
        // Drop foreign key constraints
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['id_jabatan']);
            $table->dropForeign(['id_divisi']);
        });

        Schema::table('users', function (Blueprint $table) {
            // Revert employee ID (no_induk) back to nullable
            $table->string('no_induk', 100)->nullable()->change();

            // Revert phone number (hp) back to nullable
            $table->string('hp', 100)->nullable()->change();

            // Revert jabatan (position) back to nullable
            $table->unsignedBigInteger('id_jabatan')->nullable()->change();

            // Revert divisi (division) back to nullable
            $table->unsignedBigInteger('id_divisi')->nullable()->change();
        });

        // Re-add foreign key constraints with SET NULL
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('id_jabatan')->references('id')->on('jabatan')->onDelete('set null');
            $table->foreign('id_divisi')->references('id')->on('divisi')->onDelete('set null');
        });
    }
};
