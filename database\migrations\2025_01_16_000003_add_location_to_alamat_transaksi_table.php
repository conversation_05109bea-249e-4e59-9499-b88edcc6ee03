<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Adds location field to alamat_transaksi table for map picker functionality.
     */
    public function up(): void
    {
        Schema::table('alamat_transaksi', function (Blueprint $table) {
            // Add location column for Leaflet Map Picker
            $table->json('location')->nullable()->after('alamat')
                ->comment('Koordinat lokasi dalam format JSON {lat: float, lng: float}');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alamat_transaksi', function (Blueprint $table) {
            $table->dropColumn('location');
        });
    }
};
