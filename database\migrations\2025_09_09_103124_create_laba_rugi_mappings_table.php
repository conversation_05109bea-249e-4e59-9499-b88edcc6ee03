<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('laba_rugi_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('kode_akun', 10)->index();
            $table->string('nama_akun');
            $table->string('kategori_laba_rugi')->nullable();
            $table->integer('urutan')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Foreign key to akun table
            $table->foreign('kode_akun')->references('kode_akun')->on('akun')->onDelete('cascade');

            // Unique constraint
            $table->unique(['kode_akun']);

            // Index for faster queries
            $table->index(['kategori_laba_rugi', 'urutan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('laba_rugi_mappings');
    }
};
