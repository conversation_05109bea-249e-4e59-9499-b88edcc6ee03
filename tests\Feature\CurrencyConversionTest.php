<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\CurrencyService;
use App\Models\Invoice;
use App\Models\DeliveryOrder;
use App\Models\LetterSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CurrencyConversionTest extends TestCase
{
    use RefreshDatabase;

    public function test_currency_service_idr_to_usd_conversion()
    {
        $idrAmount = 158000; // 158,000 IDR
        $usdAmount = CurrencyService::idrToUsd($idrAmount);
        
        // With default rate of 15,800, this should be 10 USD
        $this->assertEquals(10.00, $usdAmount);
    }

    public function test_currency_service_usd_to_idr_conversion()
    {
        $usdAmount = 10; // 10 USD
        $idrAmount = CurrencyService::usdToIdr($usdAmount);
        
        // With default rate of 15,800, this should be 158,000 IDR
        $this->assertEquals(158000, $idrAmount);
    }

    public function test_currency_service_format_currency_indonesian()
    {
        $amount = 1500000; // 1.5 million IDR
        $formatted = CurrencyService::formatCurrency($amount, 'id', false);
        
        $this->assertEquals('Rp 1.500.000', $formatted);
    }

    public function test_currency_service_format_currency_english()
    {
        $amount = 1580000; // 1.58 million IDR
        $formatted = CurrencyService::formatCurrency($amount, 'en', true);
        
        // Should convert to USD: 1,580,000 / 15,800 = 100 USD
        $this->assertEquals('$100.00', $formatted);
    }

    public function test_currency_service_get_currency_symbol()
    {
        $this->assertEquals('Rp', CurrencyService::getCurrencySymbol('id'));
        $this->assertEquals('$', CurrencyService::getCurrencySymbol('en'));
    }

    public function test_currency_service_get_currency_code()
    {
        $this->assertEquals('IDR', CurrencyService::getCurrencyCode('id'));
        $this->assertEquals('USD', CurrencyService::getCurrencyCode('en'));
    }

    public function test_currency_service_format_for_template()
    {
        $amount = 158000; // 158,000 IDR
        
        // Test Indonesian format
        $result = CurrencyService::formatForTemplate($amount, 'id', false);
        $this->assertEquals('Rp 158.000', $result['formatted']);
        $this->assertEquals(158000, $result['amount']);
        $this->assertEquals('IDR', $result['currency']);
        $this->assertEquals('Rp', $result['symbol']);
        
        // Test English format with conversion
        $result = CurrencyService::formatForTemplate($amount, 'en', true);
        $this->assertEquals('$10.00', $result['formatted']);
        $this->assertEquals(10.00, $result['amount']);
        $this->assertEquals('USD', $result['currency']);
        $this->assertEquals('$', $result['symbol']);
    }

    public function test_invoice_format_currency_with_indonesian_setting()
    {
        // Create letter setting for Indonesian
        $letterSetting = LetterSetting::factory()->create([
            'locale' => 'id',
            'name' => 'Test Indonesian Setting'
        ]);

        // Create invoice with letter setting
        $invoice = Invoice::factory()->create([
            'letter_setting_id' => $letterSetting->id,
            'total_invoice' => 1500000
        ]);

        $formatted = $invoice->formatCurrency(1500000);
        $this->assertEquals('Rp 1.500.000', $formatted);
    }

    public function test_invoice_format_currency_with_english_setting()
    {
        // Create letter setting for English
        $letterSetting = LetterSetting::factory()->create([
            'locale' => 'en',
            'name' => 'Test English Setting'
        ]);

        // Create invoice with letter setting
        $invoice = Invoice::factory()->create([
            'letter_setting_id' => $letterSetting->id,
            'total_invoice' => 1580000
        ]);

        $formatted = $invoice->formatCurrency(1580000);
        $this->assertEquals('$100.00', $formatted);
    }

    public function test_invoice_get_locale_priority()
    {
        // Test priority 1: Direct letter setting
        $letterSetting = LetterSetting::factory()->create(['locale' => 'en']);
        $invoice = Invoice::factory()->create(['letter_setting_id' => $letterSetting->id]);
        
        $this->assertEquals('en', $invoice->getLocale());
        
        // Test fallback to 'id' when no letter setting
        $invoice2 = Invoice::factory()->create(['letter_setting_id' => null]);
        $this->assertEquals('id', $invoice2->getLocale());
    }

    public function test_delivery_order_format_currency()
    {
        // Create letter setting for English
        $letterSetting = LetterSetting::factory()->create([
            'locale' => 'en',
            'name' => 'Test English Setting'
        ]);

        // Create delivery order with letter setting
        $deliveryOrder = DeliveryOrder::factory()->create([
            'letter_setting_id' => $letterSetting->id
        ]);

        $formatted = $deliveryOrder->formatCurrency(790000); // Should be $50.00
        $this->assertEquals('$50.00', $formatted);
    }

    public function test_currency_conversion_edge_cases()
    {
        // Test zero amount
        $this->assertEquals('$0.00', CurrencyService::formatCurrency(0, 'en'));
        $this->assertEquals('Rp 0', CurrencyService::formatCurrency(0, 'id'));
        
        // Test null amount
        $this->assertEquals('$0.00', CurrencyService::formatCurrency(null, 'en'));
        $this->assertEquals('Rp 0', CurrencyService::formatCurrency(null, 'id'));
        
        // Test very large amount
        $largeAmount = 15800000000; // 15.8 billion IDR
        $formatted = CurrencyService::formatCurrency($largeAmount, 'en', true);
        $this->assertEquals('$1,000,000.00', $formatted); // Should be 1 million USD
    }
}
