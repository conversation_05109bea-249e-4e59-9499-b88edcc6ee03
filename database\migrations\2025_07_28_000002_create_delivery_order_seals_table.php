<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_order_seals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_delivery_order');
            $table->string('nomor_segel', 100);
            $table->enum('jenis_segel', ['atas', 'bawah', 'samping', 'lainnya'])->default('atas');
            $table->text('keterangan')->nullable();
            $table->integer('urutan')->default(1);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('id_delivery_order')->references('id')->on('delivery_order')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['id_delivery_order']);
            $table->index(['jenis_segel']);
            $table->index(['nomor_segel']);
            
            // Unique constraint untuk mencegah duplikasi nomor segel pada DO yang sama
            $table->unique(['id_delivery_order', 'nomor_segel'], 'unique_seal_per_do');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_order_seals');
    }
};
