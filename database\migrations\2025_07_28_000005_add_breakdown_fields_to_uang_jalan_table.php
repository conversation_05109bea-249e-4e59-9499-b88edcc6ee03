<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('uang_jalan', function (Blueprint $table) {
            // Breakdown uang jalan
            $table->decimal('uang_depot', 15, 2)->default(0)->after('nominal');
            $table->decimal('uang_jalan_amount', 15, 2)->default(0)->after('uang_depot');
            $table->decimal('uang_bongkar', 15, 2)->default(0)->after('uang_jalan_amount');
            $table->decimal('uang_pas', 15, 2)->default(0)->after('uang_bongkar');
            $table->decimal('uang_lembur', 15, 2)->default(0)->after('uang_pas');
            $table->decimal('uang_bbm', 15, 2)->default(0)->after('uang_lembur');
            $table->decimal('uang_tol', 15, 2)->default(0)->after('uang_bbm');
            
            // Keterangan untuk setiap komponen
            $table->text('keterangan_depot')->nullable()->after('uang_tol');
            $table->text('keterangan_jalan')->nullable()->after('keterangan_depot');
            $table->text('keterangan_bongkar')->nullable()->after('keterangan_jalan');
            $table->text('keterangan_pas')->nullable()->after('keterangan_bongkar');
            $table->text('keterangan_lembur')->nullable()->after('keterangan_pas');
            $table->text('keterangan_bbm')->nullable()->after('keterangan_lembur');
            $table->text('keterangan_tol')->nullable()->after('keterangan_bbm');
            
            // Indexes untuk performance
            $table->index(['uang_depot']);
            $table->index(['uang_jalan_amount']);
            $table->index(['uang_bongkar']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('uang_jalan', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['uang_depot']);
            $table->dropIndex(['uang_jalan_amount']);
            $table->dropIndex(['uang_bongkar']);
            
            // Drop columns
            $table->dropColumn([
                'uang_depot',
                'uang_jalan_amount',
                'uang_bongkar',
                'uang_pas',
                'uang_lembur',
                'uang_bbm',
                'uang_tol',
                'keterangan_depot',
                'keterangan_jalan',
                'keterangan_bongkar',
                'keterangan_pas',
                'keterangan_lembur',
                'keterangan_bbm',
                'keterangan_tol',
            ]);
        });
    }
};
