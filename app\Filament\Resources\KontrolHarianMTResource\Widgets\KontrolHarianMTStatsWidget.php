<?php

namespace App\Filament\Resources\KontrolHarianMTResource\Widgets;

use App\Models\KontrolHarianMT;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class KontrolHarianMTStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Get today's data
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        // Today's stats
        $todayCount = KontrolHarianMT::whereDate('tanggal_kontrol', $today)->count();
        $todayCompleted = KontrolHarianMT::whereDate('tanggal_kontrol', $today)
            ->whereNotNull('jam_pulang')
            ->count();

        // This week's stats
        $weekCount = KontrolHarianMT::where('tanggal_kontrol', '>=', $thisWeek)->count();

        // This month's stats
        $monthCount = KontrolHarianMT::where('tanggal_kontrol', '>=', $thisMonth)->count();

        // Safety score calculation
        $monthlyInspections = KontrolHarianMT::where('tanggal_kontrol', '>=', $thisMonth)->get();
        $totalInspections = $monthlyInspections->count();
        $passedInspections = 0;

        foreach ($monthlyInspections as $inspection) {
            $inspectionItems = [
                'perlengkapan_bongkar_muatan',
                'ban_serap_ada',
                'lampu_rotari_menyala',
                'apar_ada',
                'surat_kendaraan_dibawa',
                'awak_mt_sehat',
                'awak_mt_safety',
                'kelengkapan_surat_awak_ready',
            ];

            $passed = 0;
            foreach ($inspectionItems as $item) {
                if ($inspection->$item) {
                    $passed++;
                }
            }

            if ($passed >= 6) { // 75% pass rate
                $passedInspections++;
            }
        }

        $safetyScore = $totalInspections > 0 ? round(($passedInspections / $totalInspections) * 100, 1) : 0;

        return [
            Stat::make('Kontrol Hari Ini', $todayCount)
                ->description($todayCompleted . ' sudah selesai')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($todayCompleted == $todayCount && $todayCount > 0 ? 'success' : 'warning')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Kontrol Minggu Ini', $weekCount)
                ->description('Total kontrol 7 hari terakhir')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info')
                ->chart([3, 5, 8, 12, 15, 18, $weekCount]),

            Stat::make('Kontrol Bulan Ini', $monthCount)
                ->description('Total kontrol bulan ' . $thisMonth->format('M Y'))
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('primary')
                ->chart([10, 15, 20, 25, 30, 35, $monthCount]),

            Stat::make('Skor Keselamatan', $safetyScore . '%')
                ->description('Rata-rata inspeksi bulan ini')
                ->descriptionIcon('heroicon-m-shield-check')
                ->color($safetyScore >= 90 ? 'success' : ($safetyScore >= 75 ? 'warning' : 'danger'))
                ->chart([85, 87, 90, 88, 92, 89, $safetyScore]),
        ];
    }
}
