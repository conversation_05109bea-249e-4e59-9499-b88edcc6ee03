<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\TransaksiPenjualan;
use App\Models\User;
use App\Models\NotificationSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class TransaksiPenjualanApprovalTest extends TestCase
{
    use RefreshDatabase;

    public function test_edit_button_hidden_when_approved()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create an approved transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'approved',
            'created_by' => $user->id
        ]);

        // Test canEdit method
        $canEdit = \App\Filament\Resources\TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertFalse($canEdit, 'Approved transaction should not be editable');
    }

    public function test_edit_button_visible_when_pending()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create a pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $user->id
        ]);

        // Test canEdit method
        $canEdit = \App\Filament\Resources\TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Pending transaction should be editable');
    }

    public function test_edit_button_visible_when_rejected()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create a rejected transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'rejected',
            'created_by' => $user->id
        ]);

        // Test canEdit method
        $canEdit = \App\Filament\Resources\TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Rejected transaction should be editable');
    }

    public function test_edit_button_visible_when_needs_revision()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create a needs revision transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'needs_revision',
            'created_by' => $user->id
        ]);

        // Test canEdit method
        $canEdit = \App\Filament\Resources\TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Transaction needing revision should be editable');
    }

    public function test_notification_sent_on_create()
    {
        // Create a manager user with phone number
        $manager = User::factory()->create([
            'hp' => '6285274897212',
            'name' => 'Test Manager'
        ]);

        // Create notification setting
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $manager->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Mock Log to capture notification attempts
        Log::shouldReceive('info')
            ->with(\Mockery::pattern('/WhatsApp notification sent for Transaksi ID/'))
            ->once();

        Log::shouldReceive('error')->never();

        // Create transaction (should trigger notification)
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $manager->id
        ]);

        $this->assertDatabaseHas('transaksi_penjualan', [
            'id' => $transaction->id,
            'status' => 'pending_approval'
        ]);
    }

    public function test_notification_sent_on_update_when_not_approved()
    {
        // Create a manager user with phone number
        $manager = User::factory()->create([
            'hp' => '6285274897212',
            'name' => 'Test Manager'
        ]);

        // Create notification setting
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $manager->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Create transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $manager->id
        ]);

        // Mock Log to capture notification attempts
        Log::shouldReceive('info')
            ->with(\Mockery::pattern('/WhatsApp notification sent for Transaksi ID/'))
            ->once();

        // Update transaction (should trigger notification)
        $transaction->update(['kode' => 'UPDATED-SO-001']);

        $this->assertDatabaseHas('transaksi_penjualan', [
            'id' => $transaction->id,
            'kode' => 'UPDATED-SO-001'
        ]);
    }

    public function test_no_notification_sent_on_update_when_approved()
    {
        // Create a manager user with phone number
        $manager = User::factory()->create([
            'hp' => '6285274897212',
            'name' => 'Test Manager'
        ]);

        // Create notification setting
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $manager->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);

        // Create approved transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'approved',
            'created_by' => $manager->id
        ]);

        // Mock Log - should NOT receive notification log for update
        Log::shouldReceive('info')
            ->with(\Mockery::pattern('/WhatsApp notification sent for Transaksi ID/'))
            ->never();

        // Update transaction (should NOT trigger notification because it's approved)
        $transaction->update(['kode' => 'UPDATED-APPROVED-SO-001']);

        $this->assertDatabaseHas('transaksi_penjualan', [
            'id' => $transaction->id,
            'kode' => 'UPDATED-APPROVED-SO-001',
            'status' => 'approved'
        ]);
    }
}
