<?php

namespace Tests\Feature;

use App\Models\ExpenseRequest;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Akun;
use App\Models\Journal;
use App\Services\ExpenseRequestService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExpenseRequestPaymentMethodTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test COA accounts
        $this->cashAccount = Akun::create([
            'kode_akun' => '1110',
            'nama_akun' => 'Kas di <PERSON>',
            'kategori_akun' => 'Aset',
            'tipe_akun' => 'Debit',
            'saldo_awal' => 0,
        ]);
        
        $this->bankAccount = Akun::create([
            'kode_akun' => '1120',
            'nama_akun' => 'Bank BNI',
            'kategori_akun' => 'Aset',
            'tipe_akun' => 'Debit',
            'saldo_awal' => 0,
        ]);
        
        // Create test payment methods
        $this->cashPaymentMethod = PaymentMethod::create([
            'method_name' => 'cash',
            'method_display_name' => 'Kas Kecil Kantor',
            'akun_id' => $this->cashAccount->id,
            'is_active' => true,
        ]);
        
        $this->bankPaymentMethod = PaymentMethod::create([
            'method_name' => 'bank_transfer',
            'method_display_name' => 'BNI Kantor Pusat',
            'akun_id' => $this->bankAccount->id,
            'bank_name' => 'Bank BNI',
            'account_number' => '**********',
            'account_name' => 'PT. Lintas Riau Prima',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function expense_request_can_be_marked_as_paid_with_payment_method()
    {
        // Create approved expense request
        $expenseRequest = ExpenseRequest::create([
            'request_number' => 'EXP-********-0001',
            'category' => 'tank_truck_maintenance',
            'title' => 'Test Expense',
            'description' => 'Test Description',
            'requested_amount' => 1000000,
            'approved_amount' => 1000000,
            'status' => 'approved',
            'requested_by' => $this->user->id,
        ]);

        // Mark as paid with payment method
        $service = new ExpenseRequestService();
        $result = $service->markAsPaid($expenseRequest, $this->cashPaymentMethod->id);

        // Verify expense request was updated
        $this->assertEquals('paid', $result->status);
        $this->assertEquals($this->cashPaymentMethod->id, $result->payment_method_id);
        $this->assertNotNull($result->paid_at);
        $this->assertEquals($this->user->id, $result->paid_by);
    }

    /** @test */
    public function expense_request_payment_method_relationship_works()
    {
        $expenseRequest = ExpenseRequest::create([
            'request_number' => 'EXP-********-0002',
            'category' => 'business_travel',
            'title' => 'Travel Expense',
            'description' => 'Business travel to Jakarta',
            'requested_amount' => 500000,
            'status' => 'paid',
            'payment_method_id' => $this->bankPaymentMethod->id,
            'requested_by' => $this->user->id,
        ]);

        // Test relationship
        $this->assertEquals($this->bankPaymentMethod->id, $expenseRequest->paymentMethod->id);
        $this->assertEquals('BNI Kantor Pusat', $expenseRequest->paymentMethod->method_display_name);
        $this->assertEquals('Bank BNI', $expenseRequest->paymentMethod->bank_name);
    }

    /** @test */
    public function expense_request_can_exist_without_payment_method()
    {
        $expenseRequest = ExpenseRequest::create([
            'request_number' => 'EXP-********-0003',
            'category' => 'utilities',
            'title' => 'Utility Bill',
            'description' => 'Monthly electricity bill',
            'requested_amount' => 200000,
            'status' => 'approved',
            'requested_by' => $this->user->id,
        ]);

        $this->assertNull($expenseRequest->payment_method_id);
        $this->assertNull($expenseRequest->paymentMethod);
    }

    /** @test */
    public function payment_method_coa_integration_works()
    {
        $expenseRequest = ExpenseRequest::create([
            'request_number' => 'EXP-********-0004',
            'category' => 'license_fee',
            'title' => 'License Renewal',
            'description' => 'Annual software license',
            'requested_amount' => 300000,
            'status' => 'paid',
            'payment_method_id' => $this->bankPaymentMethod->id,
            'requested_by' => $this->user->id,
        ]);

        // Verify COA relationship through payment method
        $this->assertEquals($this->bankAccount->id, $expenseRequest->paymentMethod->akun->id);
        $this->assertEquals('1120', $expenseRequest->paymentMethod->akun->kode_akun);
        $this->assertEquals('Bank BNI', $expenseRequest->paymentMethod->akun->nama_akun);
    }

    /** @test */
    public function different_payment_methods_can_be_used_for_different_expenses()
    {
        // Cash payment
        $cashExpense = ExpenseRequest::create([
            'request_number' => 'EXP-********-0005',
            'category' => 'other',
            'title' => 'Office Supplies',
            'description' => 'Stationery and office materials',
            'requested_amount' => 50000,
            'status' => 'paid',
            'payment_method_id' => $this->cashPaymentMethod->id,
            'requested_by' => $this->user->id,
        ]);

        // Bank transfer payment
        $bankExpense = ExpenseRequest::create([
            'request_number' => 'EXP-********-0006',
            'category' => 'tank_truck_maintenance',
            'title' => 'Truck Repair',
            'description' => 'Engine maintenance',
            'requested_amount' => 2000000,
            'status' => 'paid',
            'payment_method_id' => $this->bankPaymentMethod->id,
            'requested_by' => $this->user->id,
        ]);

        // Verify different payment methods
        $this->assertEquals('cash', $cashExpense->paymentMethod->method_name);
        $this->assertEquals('bank_transfer', $bankExpense->paymentMethod->method_name);
        
        // Verify different COA accounts
        $this->assertEquals('1110', $cashExpense->paymentMethod->akun->kode_akun);
        $this->assertEquals('1120', $bankExpense->paymentMethod->akun->kode_akun);
    }
}
