<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class FixIosMethodSpoofing
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if this is an iOS device (including Chrome iOS)
        $userAgent = $request->header('User-Agent', '');
        $isIOS = $this->detectIOS($userAgent);

        // Only apply fix for iOS devices and POST requests
        if ($isIOS && $request->isMethod('POST')) {
            // Log for debugging Chrome iOS specifically
            $isChromeIOS = strpos($userAgent, 'CriOS') !== false;
            if ($isChromeIOS) {
                Log::info('Chrome iOS request detected', [
                    'user_agent' => $userAgent,
                    'has_method' => $request->has('_method'),
                    'path' => $request->getPathInfo(),
                    'is_livewire' => $request->header('X-Livewire') !== null,
                    'content_type' => $request->header('Content-Type'),
                    'all_input' => $request->all()
                ]);
            }

            // Special handling for Livewire requests
            if ($request->header('X-Livewire')) {
                $this->handleLivewireRequest($request, $isChromeIOS);
            }

            // Check if _method is missing but should be present
            if (!$request->has('_method')) {
                // For Livewire requests, ensure _method is set
                if (
                    $request->header('X-Livewire') ||
                    $request->has('_token') ||
                    strpos($request->getPathInfo(), '/livewire/') !== false ||
                    $request->header('X-CSRF-TOKEN')
                ) {

                    // Add _method to the request
                    $request->merge(['_method' => 'POST']);

                    if ($isChromeIOS) {
                        Log::info('Added _method to Chrome iOS request');
                    }
                }
            }

            // Handle specific cases where method might be in different formats
            $method = $request->input('_method');
            if ($method && in_array(strtoupper($method), ['PUT', 'PATCH', 'DELETE'])) {
                // Override the request method
                $request->setMethod(strtoupper($method));
            }

            // Chrome iOS specific fixes
            if ($isChromeIOS) {
                // Ensure all required parameters are present
                if (!$request->has('_token') && $request->header('X-CSRF-TOKEN')) {
                    $request->merge(['_token' => $request->header('X-CSRF-TOKEN')]);
                }
            }
        }

        return $next($request);
    }

    /**
     * Detect if the request is from an iOS device
     */
    private function detectIOS(string $userAgent): bool
    {
        // Standard iOS detection
        if (preg_match('/iPad|iPhone|iPod/', $userAgent)) {
            return true;
        }

        // Chrome iOS specific detection
        if (strpos($userAgent, 'CriOS') !== false) {
            return true;
        }

        // Firefox iOS specific detection
        if (strpos($userAgent, 'FxiOS') !== false) {
            return true;
        }

        // iPad Pro detection (reports as Macintosh)
        if (strpos($userAgent, 'Macintosh') !== false && strpos($userAgent, 'Mobile') !== false) {
            return true;
        }

        // Additional iOS detection patterns
        if (preg_match('/iPhone|iPad|iPod|iOS/i', $userAgent) && strpos($userAgent, 'AppleWebKit') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Handle Livewire-specific request processing for iOS
     */
    private function handleLivewireRequest(Request $request, bool $isChromeIOS): void
    {
        // Get the raw JSON content
        $content = $request->getContent();

        if ($content && $isChromeIOS) {
            Log::info('Livewire request content', [
                'content_length' => strlen($content),
                'content_preview' => substr($content, 0, 300),
                'headers' => $request->headers->all()
            ]);
        }

        // Try to decode and modify the JSON payload
        if ($content) {
            try {
                $payload = json_decode($content, true);

                if ($payload && is_array($payload)) {
                    $modified = false;

                    // Add _method to main payload if missing
                    if (!isset($payload['_method'])) {
                        $payload['_method'] = 'POST';
                        $modified = true;

                        if ($isChromeIOS) {
                            Log::info('Added _method to Livewire main payload');
                        }
                    }

                    // Comprehensive component fixing
                    if (isset($payload['components']) && is_array($payload['components'])) {
                        foreach ($payload['components'] as $index => &$component) {
                            // Add _method to component
                            if (!isset($component['_method'])) {
                                $component['_method'] = 'POST';
                                $modified = true;

                                if ($isChromeIOS) {
                                    Log::info("Added _method to Livewire component {$index}");
                                }
                            }

                            // Fix calls array (CRITICAL for actions)
                            if (isset($component['calls']) && is_array($component['calls'])) {
                                foreach ($component['calls'] as $callIndex => &$call) {
                                    // Add _method to call
                                    if (!isset($call['_method'])) {
                                        $call['_method'] = 'POST';
                                        $modified = true;

                                        if ($isChromeIOS) {
                                            Log::info("Added _method to Livewire component {$index} call {$callIndex}");
                                        }
                                    }

                                    // CRITICAL: Ensure 'method' property exists
                                    if (!isset($call['method'])) {
                                        if (isset($call['params']) && is_array($call['params']) && count($call['params']) > 0) {
                                            $call['method'] = $call['params'][0];
                                            $modified = true;

                                            if ($isChromeIOS) {
                                                Log::info("Added method property to call: {$call['method']}");
                                            }
                                        } else {
                                            $call['method'] = 'unknown';
                                            $modified = true;

                                            if ($isChromeIOS) {
                                                Log::warning("Added fallback method property to call");
                                            }
                                        }
                                    }
                                }
                            }

                            // Fix updates array
                            if (isset($component['updates']) && is_array($component['updates'])) {
                                foreach ($component['updates'] as $updateIndex => &$update) {
                                    if (!isset($update['_method'])) {
                                        $update['_method'] = 'POST';
                                        $modified = true;

                                        if ($isChromeIOS) {
                                            Log::info("Added _method to Livewire component {$index} update {$updateIndex}");
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // If we modified the payload, update the request
                    if ($modified) {
                        $newContent = json_encode($payload);

                        // Create a new request with the modified content
                        $newRequest = $request->duplicate();
                        $newRequest->initialize(
                            $request->query->all(),
                            $request->request->all(),
                            $request->attributes->all(),
                            $request->cookies->all(),
                            $request->files->all(),
                            $request->server->all(),
                            $newContent
                        );

                        // Replace the current request content
                        $request->initialize(
                            $newRequest->query->all(),
                            $newRequest->request->all(),
                            $newRequest->attributes->all(),
                            $newRequest->cookies->all(),
                            $newRequest->files->all(),
                            $newRequest->server->all(),
                            $newContent
                        );

                        if ($isChromeIOS) {
                            Log::info('Updated Livewire request content', [
                                'original_length' => strlen($content),
                                'new_length' => strlen($newContent),
                                'modifications' => 'Added missing method properties'
                            ]);
                        }
                    }
                }
            } catch (\Exception $e) {
                if ($isChromeIOS) {
                    Log::error('Error processing Livewire JSON payload', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'content_preview' => substr($content, 0, 200)
                    ]);
                }
            }
        }
    }
}
