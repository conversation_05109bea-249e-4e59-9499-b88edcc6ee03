<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KontrolHarianMT extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'tanggal_kontrol',
        'plat_kendaraan',
        'awak_mobil_tangki',
        'petugas_sekuriti',
        'tujuan_mt',
        'jam_keberangkatan',
        'jam_pulang',
        'jenis_muatan',
        'perlengkapan_bongkar_muatan',
        'ban_serap_ada',
        'lampu_rotari_menyala',
        'apar_ada',
        'kondisi_kebersihan',
        'surat_kendaraan_dibawa',
        'awak_mt_sehat',
        'awak_mt_safety',
        'kelengkapan_surat_awak_ready',
        'created_by',
    ];

    protected $casts = [
        'tanggal_kontrol' => 'date',
        'jam_keberangkatan' => 'datetime:H:i',
        'jam_pulang' => 'datetime:H:i',
        'perlengkapan_bongkar_muatan' => 'boolean',
        'ban_serap_ada' => 'boolean',
        'lampu_rotari_menyala' => 'boolean',
        'apar_ada' => 'boolean',
        'surat_kendaraan_dibawa' => 'boolean',
        'awak_mt_sehat' => 'boolean',
        'awak_mt_safety' => 'boolean',
        'kelengkapan_surat_awak_ready' => 'boolean',
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function kendaraan(): BelongsTo
    {
        return $this->belongsTo(Kendaraan::class, 'plat_kendaraan', 'no_pol_kendaraan');
    }

    public function awakMobilTangki(): BelongsTo
    {
        return $this->belongsTo(User::class, 'awak_mobil_tangki', 'name');
    }

    public function petugasSekuriti(): BelongsTo
    {
        return $this->belongsTo(User::class, 'petugas_sekuriti', 'name');
    }

    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'jenis_muatan', 'name');
    }

    public function getKondisiKebersihanLabelAttribute(): string
    {
        return match ($this->kondisi_kebersihan) {
            'bersih' => 'Bersih',
            'kurang_bersih' => 'Kurang Bersih',
            default => 'Tidak Diketahui'
        };
    }
}
