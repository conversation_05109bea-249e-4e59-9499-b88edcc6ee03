<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class InvoicePaymentPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     * Creates posting rules for invoice payment acceptance
     */
    public function run(): void
    {
        // Clear existing invoice payment posting rules
        $this->clearExistingPaymentRules();

        // Create payment posting rules
        $this->createPaymentAcceptanceRule();
    }

    private function clearExistingPaymentRules()
    {
        // Delete existing invoice payment posting rules
        $paymentRules = PostingRule::where('source_type', 'InvoicePayment')->get();

        foreach ($paymentRules as $rule) {
            PostingRuleEntry::where('posting_rule_id', $rule->id)->delete();
            $rule->delete();
        }
    }

    private function createPaymentAcceptanceRule()
    {
        // Check if required accounts exist
        $this->ensureRequiredAccountsExist();

        // Posting Rule untuk Penerimaan Pembayaran Invoice
        $paymentRule = PostingRule::create([
            'rule_name' => 'Penerimaan Pembayaran Invoice',
            'source_type' => 'InvoicePayment',
            'trigger_condition' => ['status' => 'accepted'],
            'description' => 'Aturan posting ketika pembayaran invoice diterima - mencatat penerimaan kas/bank dan mengurangi piutang',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Kas/Bank (berdasarkan payment method)
        // This will be dynamically determined by the payment method's account
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentRule->id,
            'account_id' => Akun::where('kode_akun', '1101')->first()->id, // Default to Kas - will be updated by payment method
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Penerimaan pembayaran invoice {source.invoice.nomor_invoice} via {source.paymentMethod.method_display_name} - Ref: {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha (mengurangi piutang)
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pelunasan piutang invoice {source.invoice.nomor_invoice} - {source.invoice.nama_pelanggan}',
            'sort_order' => 2,
        ]);
    }

    private function ensureRequiredAccountsExist()
    {
        $requiredAccounts = [
            ['kode_akun' => '1101', 'nama_akun' => 'Kas', 'kategori_akun' => 'Aset Lancar'],
            ['kode_akun' => '1201', 'nama_akun' => 'Piutang Usaha', 'kategori_akun' => 'Aset Lancar'],
        ];

        foreach ($requiredAccounts as $accountData) {
            $account = Akun::where('kode_akun', $accountData['kode_akun'])->first();
            
            if (!$account) {
                Akun::create([
                    'kode_akun' => $accountData['kode_akun'],
                    'nama_akun' => $accountData['nama_akun'],
                    'kategori_akun' => $accountData['kategori_akun'],
                    'tipe_akun' => 'Aset',
                    'saldo_awal' => 0,
                    'created_by' => 1,
                ]);
                
                $this->command->info("Created account: {$accountData['kode_akun']} - {$accountData['nama_akun']}");
            }
        }
    }
}
