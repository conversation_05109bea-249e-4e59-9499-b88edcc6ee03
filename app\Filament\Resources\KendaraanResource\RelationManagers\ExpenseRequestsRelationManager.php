<?php

namespace App\Filament\Resources\KendaraanResource\RelationManagers;

use App\Models\ExpenseRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseRequestsRelationManager extends RelationManager
{
    protected static string $relationship = 'expenseRequests';

    protected static ?string $title = 'Riwayat Expense Request';

    protected static ?string $modelLabel = 'Expense Request';

    protected static ?string $pluralModelLabel = 'Expense Requests';

    protected static ?string $icon = 'heroicon-o-document-text';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('request_number')
                    ->label('No. Permintaan')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('title')
                    ->label('Judul')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->required()
                    ->rows(3),

                Forms\Components\TextInput::make('requested_amount')
                    ->label('Jumlah Diminta')
                    ->required()
                    ->numeric()
                    ->prefix('Rp'),

                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'submitted' => 'Diajukan',
                        'under_review' => 'Sedang Ditinjau',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'paid' => 'Dibayar',
                    ])
                    ->required(),

                Forms\Components\Select::make('priority')
                    ->label('Prioritas')
                    ->options([
                        'low' => 'Rendah',
                        'medium' => 'Sedang',
                        'high' => 'Tinggi',
                        'urgent' => 'Mendesak',
                    ])
                    ->default('medium')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('request_number')
                    ->label('No. Permintaan')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('title')
                    ->label('Judul')
                    ->searchable()
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 40 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('requested_amount')
                    ->label('Jumlah')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'submitted' => 'warning',
                        'under_review' => 'info',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'paid' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'submitted' => 'Diajukan',
                        'under_review' => 'Sedang Ditinjau',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'paid' => 'Dibayar',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'low' => 'gray',
                        'medium' => 'warning',
                        'high' => 'danger',
                        'urgent' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'low' => 'Rendah',
                        'medium' => 'Sedang',
                        'high' => 'Tinggi',
                        'urgent' => 'Mendesak',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('requestedBy.name')
                    ->label('Diminta Oleh')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('requested_date')
                    ->label('Tanggal Permintaan')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('category')
                    ->label('Kategori')
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'ganti_oli' => 'Ganti Oli Kendaraan',
                            'vehicle_maintenance' => 'Perawatan Kendaraan',
                            'vehicle_fuel' => 'Bahan Bakar Kendaraan',
                            'vehicle_tax' => 'Pajak dan STNK Kendaraan',
                            'delivery_reimbursement' => 'Reimburs Delivery Order',
                            'uang_jalan_sc' => 'Uang Jalan SC',
                            default => ucfirst(str_replace('_', ' ', $state)),
                        };
                    })
                    ->badge()
                    ->color(function ($state) {
                        return match ($state) {
                            'ganti_oli' => 'warning',
                            'vehicle_maintenance' => 'info',
                            'vehicle_fuel' => 'success',
                            'vehicle_tax' => 'danger',
                            'delivery_reimbursement' => 'primary',
                            'uang_jalan_sc' => 'secondary',
                            default => 'gray',
                        };
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\ViewColumn::make('attributes')
                    ->label('Data Tambahan')
                    ->view('tables.columns.expense-attributes-summary')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'submitted' => 'Diajukan',
                        'under_review' => 'Sedang Ditinjau',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'paid' => 'Dibayar',
                    ])
                    ->multiple(),

                Tables\Filters\SelectFilter::make('priority')
                    ->label('Prioritas')
                    ->options([
                        'low' => 'Rendah',
                        'medium' => 'Sedang',
                        'high' => 'Tinggi',
                        'urgent' => 'Mendesak',
                    ])
                    ->multiple(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Set default category if not provided
                        if (!isset($data['category'])) {
                            $data['category'] = 'vehicle_maintenance';
                        }
                        $data['id_kendaraan'] = $this->getOwnerRecord()->id;
                        $data['user_id'] = auth()->id();
                        $data['requested_by'] = auth()->id();
                        $data['requested_date'] = now();
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('viewAttributes')
                    ->label('Detail Data')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn($record) => 'Detail Data Tambahan - ' . $record->title)
                    ->modalContent(fn($record) => view('modals.expense-attributes-detail', ['record' => $record]))
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Tutup')
                    ->visible(fn($record) => !empty($record->attributes)),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    protected function getTableQuery(): Builder
    {
        // Get the base query from the relationship
        $query = $this->getOwnerRecord()->expenseRequests()->getQuery();

        // Apply additional filters - show all vehicle-related expenses, not just tank_truck_maintenance
        return $query->whereIn('category', [
            'ganti_oli',
            'vehicle_maintenance',
            'vehicle_fuel',
            'vehicle_tax',
            'delivery_reimbursement',
            'uang_jalan_sc',
            'tank_truck_maintenance'
        ])
            ->with(['requestedBy', 'account', 'paymentMethod']);
    }
}
