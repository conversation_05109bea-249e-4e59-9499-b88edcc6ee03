<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->decimal('ongkos_angkut', 15, 2)->default(0)->after('pbbkb_amount')->comment('Ongkos angkut per unit untuk transaksi jasa');
            $table->decimal('total_ongkos_angkut', 15, 2)->default(0)->after('ongkos_angkut')->comment('Total ongkos angkut (ongkos_angkut * quantity)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn(['ongkos_angkut', 'total_ongkos_angkut']);
        });
    }
};
