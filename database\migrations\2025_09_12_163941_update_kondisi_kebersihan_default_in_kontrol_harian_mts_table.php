<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kontrol_harian_m_t_s', function (Blueprint $table) {
            // Update kondisi_kebersihan column to have default value 'bersih'
            $table->enum('kondisi_kebersihan', ['bersih', 'kurang_bersih'])
                ->default('bersih')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kontrol_harian_mts', function (Blueprint $table) {
            // Remove default value from kondisi_kebersihan column
            $table->enum('kondisi_kebersihan', ['bersih', 'kurang_bersih'])
                ->change();
        });
    }
};
