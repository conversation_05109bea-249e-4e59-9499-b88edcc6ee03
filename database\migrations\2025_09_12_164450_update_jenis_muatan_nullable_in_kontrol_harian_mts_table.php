<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kontrol_harian_m_t_s', function (Blueprint $table) {
            // Make jenis_muatan column nullable
            $table->string('jenis_muatan')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kontrol_harian_m_t_s', function (Blueprint $table) {
            // Make jenis_muatan column not nullable again
            $table->string('jenis_muatan')->nullable(false)->change();
        });
    }
};
