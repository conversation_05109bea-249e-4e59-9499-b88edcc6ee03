<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NeracaMapping;

class NeracaMappingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        NeracaMapping::truncate();

        $mappings = [
            // AKTIVA LANCAR - Kas & Bank
            ['100', 'Cash in Hand', 'Kas & Bank', 1],
            ['101', 'Cash in bank mandiri', 'Kas & Bank', 2],
            ['102', 'Cash in bank BNI', 'Kas & Bank', 3],
            ['103', 'Cash in Bank BNI USD', 'Kas & Bank', 4],
            ['104', 'Deposito', 'Kas & Bank', 5],

            // AKTIVA LANCAR - Piutang Usaha
            ['200.1', 'Piutang Usaha PT. ...', 'Piutang Usaha', 1],
            ['200.2', 'Piutang Usaha PT. ...', 'Piutang Usaha', 2],
            ['200.3', 'Piutang Usaha PT. ...', '<PERSON>utang Usaha', 3],
            ['200.4', 'Piutang Usaha PT. ...', 'Piutang Usaha', 4],
            ['200.5', 'Piutang Usaha PT. ...', 'Piutang Usaha', 5],

            // AKTIVA LANCAR - Piutang Lain-Lain
            ['201.1', 'Piutang Pemegang saham An. ...', 'Piutang Lain-Lain', 1],
            ['201.2', 'Piutang Pemegang saham An. ...', 'Piutang Lain-Lain', 2],
            ['201.3', 'Piutang Pemegang saham An. ...', 'Piutang Lain-Lain', 3],
            ['201.4', 'Piutang Pemegang saham An. ...', 'Piutang Lain-Lain', 4],
            ['201.5', 'Piutang Pemegang saham An. ...', 'Piutang Lain-Lain', 5],
            ['202.1', 'Piutang Karyawan An. ...', 'Piutang Lain-Lain', 6],
            ['202.2', 'Piutang Karyawan An. ...', 'Piutang Lain-Lain', 7],
            ['202.3', 'Piutang Karyawan An. ...', 'Piutang Lain-Lain', 8],
            ['202.4', 'Piutang Karyawan An. ...', 'Piutang Lain-Lain', 9],
            ['202.5', 'Piutang Karyawan An. ...', 'Piutang Lain-Lain', 10],
            ['203', 'PPN Keluaran', 'Piutang Lain-Lain', 11],
            ['204', 'PPN Masukan', 'Piutang Lain-Lain', 12],
            ['299', 'Piutang Lain - lain', 'Piutang Lain-Lain', 13],

            // AKTIVA LANCAR - Persediaan
            ['500', 'Persediaan Awal', 'Persediaan', 1],
            ['504', 'Persediaan Akhir', 'Persediaan', 2],

            // AKTIVA TETAP - Tanah dan Bangunan
            ['300', 'Tanah', 'Tanah dan Bangunan', 1],
            ['301', 'Bangunan', 'Tanah dan Bangunan', 2],

            // AKTIVA TETAP - Peralatan & Kendaraan
            ['302', 'Kendaraan', 'Peralatan & Kendaraan', 1],

            // AKTIVA TETAP - Akm. Peny. Peralatan & Kendaraan
            ['303', 'Akumulasi Penyusutan Kendaraan', 'Akm. Peny. Peralatan & Kendaraan', 1],

            // AKTIVA TETAP - Inventaris
            ['304', 'Inventaris', 'Inventaris', 1],

            // AKTIVA TETAP - Akm. Peny. Inventaris
            ['305', 'Akumulasi Penyusutan Inventaris', 'Akm. Peny. Inventaris', 1],

            // PASIVA - Hutang Usaha
            ['306', 'Hutang Usaha', 'Hutang Usaha', 1],

            // PASIVA - Hutang Bank
            ['307', 'Hutang Bank', 'Hutang Bank', 1],

            // PASIVA - Hutang Kendaraan
            ['308', 'Hutang Leasing/Kendaraan', 'Hutang Kendaraan', 1],

            // PASIVA - Hutang Pajak
            ['309', 'Hutang Pajak', 'Hutang Pajak', 1],

            // PASIVA - Modal Disetor
            ['310', 'Modal Disetor', 'Modal Disetor', 1],
            ['311', 'Laba ditahan sd tahun xxxx', 'Modal Disetor', 2],
            ['312', 'Dividen', 'Modal Disetor', 3],
            ['313', 'Laba tahun berjalan', 'Modal Disetor', 4],
        ];

        foreach ($mappings as $mapping) {
            NeracaMapping::create([
                'kode_akun' => $mapping[0],
                'nama_akun' => $mapping[1],
                'kategori_neraca' => $mapping[2],
                'urutan' => $mapping[3],
            ]);
        }
    }
}
