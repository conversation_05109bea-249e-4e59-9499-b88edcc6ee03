<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shift extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'shift';

    protected $fillable = [
        'nama_shift',
        'waktu_mulai',
        'waktu_selesai',
        'toleransi_keterlambatan',
        'is_split_shift',
        'waktu_mulai_periode2',
        'waktu_selesai_periode2',
        'toleransi_keterlambatan_periode2',
        'keterangan',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_split_shift' => 'boolean',
        'waktu_mulai' => 'datetime:H:i:s',
        'waktu_selesai' => 'datetime:H:i:s',
        'waktu_mulai_periode2' => 'datetime:H:i:s',
        'waktu_selesai_periode2' => 'datetime:H:i:s',
    ];

    /**
     * Get the schedules associated with this shift
     */
    public function schedules()
    {
        return $this->hasMany(Schedule::class, 'shift_id');
    }

    /**
     * Get the user who created this shift
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the bulk schedules associated with this shift
     */
    public function jadwalMasal()
    {
        return $this->hasMany(JadwalMasal::class, 'shift_id');
    }

    /**
     * Check if this is a split shift
     */
    public function isSplitShift(): bool
    {
        return $this->is_split_shift &&
            !is_null($this->waktu_mulai_periode2) &&
            !is_null($this->waktu_selesai_periode2);
    }

    /**
     * Get all work periods for this shift
     * Returns array of periods with start and end times
     */
    public function getWorkPeriods(): array
    {
        $periods = [
            [
                'periode' => 1,
                'waktu_mulai' => $this->waktu_mulai,
                'waktu_selesai' => $this->waktu_selesai,
                'toleransi_keterlambatan' => $this->toleransi_keterlambatan,
            ]
        ];

        if ($this->isSplitShift()) {
            $periods[] = [
                'periode' => 2,
                'waktu_mulai' => $this->waktu_mulai_periode2,
                'waktu_selesai' => $this->waktu_selesai_periode2,
                'toleransi_keterlambatan' => $this->toleransi_keterlambatan_periode2 ?? $this->toleransi_keterlambatan,
            ];
        }

        return $periods;
    }

    /**
     * Determine which period the given time falls into
     */
    public function getCurrentPeriod($time = null): ?int
    {
        if (!$time) {
            $time = now()->format('H:i:s');
        }

        $timeCarbon = \Carbon\Carbon::parse($time);
        $periods = $this->getWorkPeriods();

        foreach ($periods as $period) {
            $start = \Carbon\Carbon::parse($period['waktu_mulai']);
            $end = \Carbon\Carbon::parse($period['waktu_selesai']);

            // Handle overnight shifts
            if ($end->lessThan($start)) {
                $end->addDay();
                if ($timeCarbon->lessThan($start)) {
                    $timeCarbon->addDay();
                }
            }

            // Check if time is within this period (with some buffer)
            $bufferStart = $start->copy()->subHours(2); // 2 hours before start
            $bufferEnd = $end->copy()->addHours(2); // 2 hours after end

            if ($timeCarbon->between($bufferStart, $bufferEnd)) {
                return $period['periode'];
            }
        }

        return 1; // Default to first period
    }

    /**
     * Get formatted display text for the shift
     */
    public function getDisplayText(): string
    {
        if ($this->isSplitShift()) {
            return sprintf(
                '%s (Split: %s-%s & %s-%s)',
                $this->nama_shift,
                substr($this->waktu_mulai, 0, 5),
                substr($this->waktu_selesai, 0, 5),
                substr($this->waktu_mulai_periode2, 0, 5),
                substr($this->waktu_selesai_periode2, 0, 5)
            );
        }

        return sprintf(
            '%s (%s-%s)',
            $this->nama_shift,
            substr($this->waktu_mulai, 0, 5),
            substr($this->waktu_selesai, 0, 5)
        );
    }
}
