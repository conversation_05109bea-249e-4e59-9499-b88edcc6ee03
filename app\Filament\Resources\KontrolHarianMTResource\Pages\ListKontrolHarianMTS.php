<?php

namespace App\Filament\Resources\KontrolHarianMTResource\Pages;

use App\Filament\Resources\KontrolHarianMTResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListKontrolHarianMTS extends ListRecords
{
    protected static string $resource = KontrolHarianMTResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            //
        ];
    }
}
