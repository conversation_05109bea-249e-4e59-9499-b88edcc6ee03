<?php

namespace App\Services;

class HardcodedSignerService
{
    /**
     * Get the signer based on SPH approval target
     * Returns the user who is the target of approval (manager) as the signer
     *
     * @param mixed $sph SPH model instance
     * @return mixed User object or null
     */
    public static function getDefaultSigner($sph = null)
    {
        // If SPH is provided, find the approval target user
        if ($sph) {
            // Get notification settings for SPH approval events
            $notificationSettings = \App\Models\NotificationSetting::where('event_name', 'sph_baru')
                ->where('is_active', true)
                ->with('user')
                ->get();

            // Return the first active approval target user
            foreach ($notificationSettings as $setting) {
                if ($setting->user && $setting->user->hp) {
                    return $setting->user;
                }
            }

            // Fallback: if no approval target found, use creator
            if ($sph->createdBy) {
                return $sph->createdBy;
            }
        }

        // If no SPH or no valid users found, return null
        return null;
    }
}
