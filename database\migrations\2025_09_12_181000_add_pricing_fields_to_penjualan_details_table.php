<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penjualan_detail', function (Blueprint $table) {
            $table->decimal('harga_dasar', 15, 2)->nullable()->after('harga_jual');
            $table->decimal('discount_agent_skp', 15, 2)->nullable()->after('harga_dasar');
            $table->decimal('ppn', 15, 2)->nullable()->after('discount_agent_skp');
            $table->decimal('pbbkb', 15, 2)->nullable()->after('ppn');
            $table->decimal('pph_22', 15, 2)->nullable()->after('pbbkb');
            $table->decimal('rounding', 15, 2)->nullable()->after('pph_22');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penjualan_detail', function (Blueprint $table) {
            $table->dropColumn([
                'harga_dasar',
                'discount_agent_skp',
                'ppn',
                'pbbkb',
                'pph_22',
                'rounding'
            ]);
        });
    }
};
