<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aset', function (Blueprint $table) {
            $table->dropColumn(['id_akun_depresiasi']);

            $table->unsignedBigInteger('id_akun_akumulasi')->nullable()->after('id_akun_aset');
            $table->unsignedBigInteger('id_akun_beban')->nullable()->after('id_akun_aset');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asets', function (Blueprint $table) {
            $table->unsignedBigInteger('id_akun_depresiasi')->nullable();
            $table->dropColumn(['id_akun_akumulasi', 'id_akun_beban']);
        });
    }
};
