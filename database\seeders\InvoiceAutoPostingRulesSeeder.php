<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class InvoiceAutoPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 1. Posting Rule untuk Invoice (Piutang Usaha)
        $invoiceRule = PostingRule::create([
            'rule_name' => 'Invoice - Piutang Usaha',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['action' => 'publish']),
            'description' => 'Auto posting piutang usaha ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha (200.1)
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceRule->id,
            'account_id' => Akun::where('kode_akun', '200.1')->first()->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang Usaha - {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Pendapatan Jasa (700)
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceRule->id,
            'account_id' => Akun::where('kode_akun', '700')->first()->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Pendapatan Jasa - {source.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // 2. Posting Rule untuk Invoice Payment (Pembayaran Piutang)
        $paymentRule = PostingRule::create([
            'rule_name' => 'Invoice Payment - Pembayaran Piutang',
            'source_type' => 'InvoicePayment',
            'trigger_condition' => json_encode(['status' => 'accepted']),
            'description' => 'Auto posting pembayaran piutang ketika payment diterima',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Kas/Bank (tergantung payment method)
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentRule->id,
            'account_id' => Akun::where('kode_akun', '100')->first()->id, // Default ke Kas
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran Invoice - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha (200.1)
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentRule->id,
            'account_id' => Akun::where('kode_akun', '200.1')->first()->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pelunasan Piutang - {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // 3. Posting Rule untuk Invoice Deduction - PPN
        $ppnRule = PostingRule::create([
            'rule_name' => 'Invoice Deduction - PPN',
            'source_type' => 'InvoiceDeduction',
            'trigger_condition' => json_encode(['deduction_type' => 'ppn']),
            'description' => 'Auto posting potongan PPN',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: PPN (901)
        PostingRuleEntry::create([
            'posting_rule_id' => $ppnRule->id,
            'account_id' => Akun::where('kode_akun', '901')->first()->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Potongan PPN - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha (200.1)
        PostingRuleEntry::create([
            'posting_rule_id' => $ppnRule->id,
            'account_id' => Akun::where('kode_akun', '200.1')->first()->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pengurangan Piutang - PPN {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // 4. Posting Rule untuk Invoice Deduction - PPh
        $pphRule = PostingRule::create([
            'rule_name' => 'Invoice Deduction - PPh',
            'source_type' => 'InvoiceDeduction',
            'trigger_condition' => json_encode(['deduction_type' => 'pph']),
            'description' => 'Auto posting potongan PPh',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Pajak Pasal 23 (904)
        PostingRuleEntry::create([
            'posting_rule_id' => $pphRule->id,
            'account_id' => Akun::where('kode_akun', '904')->first()->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Potongan PPh - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha (200.1)
        PostingRuleEntry::create([
            'posting_rule_id' => $pphRule->id,
            'account_id' => Akun::where('kode_akun', '200.1')->first()->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pengurangan Piutang - PPh {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // 5. Posting Rule untuk Invoice Deduction - Admin Bank
        $adminBankRule = PostingRule::create([
            'rule_name' => 'Invoice Deduction - Admin Bank',
            'source_type' => 'InvoiceDeduction',
            'trigger_condition' => json_encode(['deduction_type' => 'admin_bank']),
            'description' => 'Auto posting potongan admin bank',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Debit: Adm Bank (800)
        PostingRuleEntry::create([
            'posting_rule_id' => $adminBankRule->id,
            'account_id' => Akun::where('kode_akun', '800')->first()->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Biaya Admin Bank - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha (200.1)
        PostingRuleEntry::create([
            'posting_rule_id' => $adminBankRule->id,
            'account_id' => Akun::where('kode_akun', '200.1')->first()->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pengurangan Piutang - Admin Bank {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        echo "Invoice auto posting rules created successfully!\n";
    }
}
