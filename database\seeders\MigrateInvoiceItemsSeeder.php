<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\InvoiceItem;

class MigrateInvoiceItemsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Migrating existing invoice data to invoice_items...');

        $invoices = Invoice::with([
            'transaksiPenjualan.penjualanDetails.item.satuan'
        ])->get();

        $created = 0;
        foreach ($invoices as $invoice) {
            // Skip if invoice already has items
            if ($invoice->invoiceItems()->count() > 0) {
                continue;
            }

            if ($invoice->transaksiPenjualan && $invoice->transaksiPenjualan->penjualanDetails) {
                foreach ($invoice->transaksiPenjualan->penjualanDetails as $detail) {
                    if ($detail->item) {
                        $quantity = $detail->volume_do ?? $detail->volume_item;
                        $unitPrice = $detail->harga_jual;
                        $subtotal = $quantity * $unitPrice;
                        
                        // Calculate PPN (assuming 11% if invoice includes PPN)
                        $includePpn = $invoice->include_ppn ?? true;
                        $ppnRate = 11.00;
                        $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
                        $totalAmount = $subtotal + $ppnAmount;

                        InvoiceItem::create([
                            'invoice_id' => $invoice->id,
                            'item_id' => $detail->id_item,
                            'item_name' => $detail->item->name,
                            'item_description' => $detail->item->description,
                            'quantity' => $quantity,
                            'unit' => $detail->item->satuan->nama ?? 'Liter',
                            'unit_price' => $unitPrice,
                            'subtotal' => $subtotal,
                            'ppn_amount' => $ppnAmount,
                            'total_amount' => $totalAmount,
                            'include_ppn' => $includePpn,
                            'ppn_rate' => $ppnRate,
                            'notes' => 'Migrated from penjualan_detail',
                        ]);

                        $created++;
                    }
                }
            }
        }

        $this->command->info("Successfully created {$created} invoice items from existing data.");
    }
}
