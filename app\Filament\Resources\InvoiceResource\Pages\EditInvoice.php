<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditInvoice extends EditRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),

            // Add manual recalculation action


            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load invoice items properly for editing
        $record = $this->getRecord();

        if ($record->invoiceItems()->exists()) {
            $data['invoiceItems'] = $record->invoiceItems->map(function ($item) {
                return [
                    'item_id' => $item->item_id,
                    'item_name' => $item->item_name,
                    'item_description' => $item->item_description,
                    'quantity' => $item->quantity,
                    'unit' => $item->unit,
                    'unit_price' => $item->unit_price,
                    'subtotal' => $item->subtotal,
                    'include_ppn' => $item->include_ppn,
                    'ppn_rate' => $item->ppn_rate,
                    'ppn_amount' => $item->ppn_amount,
                    'include_operasional' => $item->include_operasional,
                    'operasional_rate' => $item->operasional_rate,
                    'operasional_amount' => $item->operasional_amount,
                    'include_pbbkb' => $item->include_pbbkb,
                    'pbbkb_rate' => $item->pbbkb_rate,
                    'pbbkb_amount' => $item->pbbkb_amount,
                    'total_amount' => $item->total_amount,
                    'notes' => $item->notes,
                ];
            })->toArray();
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Recalculate all totals after saving to ensure accuracy
        $this->recalculateInvoiceTotals();
    }



    /**
     * Recalculate invoice totals from database records
     */
    protected function recalculateInvoiceTotals(): void
    {
        $record = $this->getRecord();

        // First, recalculate individual item totals
        foreach ($record->invoiceItems as $item) {
            $quantity = (float) $item->quantity;
            $unitPrice = (float) $item->unit_price;

            // Calculate subtotal
            $subtotal = $quantity * $unitPrice;

            // Calculate PPN
            $ppnAmount = 0;
            if ($item->include_ppn) {
                $ppnRate = (float) ($item->ppn_rate ?? 11);
                $ppnAmount = $subtotal * ($ppnRate / 100);
            }

            // Calculate operational
            $operasionalAmount = 0;
            if ($item->include_operasional) {
                $operasionalRate = (float) ($item->operasional_rate ?? 0);
                $operasionalAmount = $quantity * $operasionalRate;
            }

            // Calculate PBBKB
            $pbbkbAmount = 0;
            if ($item->include_pbbkb) {
                $pbbkbRate = (float) ($item->pbbkb_rate ?? 0);
                $pbbkbAmount = $quantity * $pbbkbRate;
            }

            // Calculate total amount
            $totalAmount = $subtotal + $ppnAmount + $operasionalAmount + $pbbkbAmount;

            // Update item totals
            $item->update([
                'subtotal' => $subtotal,
                'ppn_amount' => $ppnAmount,
                'operasional_amount' => $operasionalAmount,
                'pbbkb_amount' => $pbbkbAmount,
                'total_amount' => $totalAmount,
            ]);
        }

        // Refresh the relationship to get updated data
        $record->refresh();
        $record->load('invoiceItems');

        // Calculate invoice totals from updated items
        $totalSubtotal = $record->invoiceItems->sum('subtotal');
        $totalPpn = $record->invoiceItems->sum('ppn_amount');
        $totalOperasional = $record->invoiceItems->sum('operasional_amount');
        $totalPbbkb = $record->invoiceItems->sum('pbbkb_amount');
        $totalVolume = $record->invoiceItems->sum('quantity');

        // Calculate final total
        $biayaOngkos = (float) $record->biaya_ongkos_angkut;
        $totalInvoice = $totalSubtotal + $biayaOngkos + $totalOperasional + $totalPpn + $totalPbbkb;

        // Update invoice totals
        $record->update([
            'subtotal' => $totalSubtotal,
            'total_pajak' => $totalPpn,
            'biaya_operasional_kerja' => $totalOperasional,
            'biaya_pbbkb' => $totalPbbkb,
            'operasional_volume' => $totalVolume,
            'pbbkb_volume' => $totalVolume,
            'total_invoice' => $totalInvoice,
        ]);
    }
}
