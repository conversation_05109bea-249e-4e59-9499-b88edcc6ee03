<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class FixDriverRolePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Fixing driver role permissions...');

        // Get the driver role
        $driverRole = Role::where('name', 'driver')->first();
        
        if (!$driverRole) {
            $this->command->error('Driver role not found!');
            return;
        }

        // Remove all accounting page permissions from driver role
        $accountingPagePermissions = [
            'page_GeneralLedger',
            'page_IncomeStatement', 
            'page_BalanceSheet',
            'page_AccountsReceivableDashboard',
            'page_FuelDeliveryDashboard',
            'page_MonthlyDeliveryReportDashboard',
            'page_MonthlySalesRealizationDashboard',
            'page_SalesOrderTimeline',
            'page_SalesOrderTimelineDetail',
        ];

        foreach ($accountingPagePermissions as $permission) {
            $permissionModel = Permission::where('name', $permission)->first();
            if ($permissionModel && $driverRole->hasPermissionTo($permission)) {
                $driverRole->revokePermissionTo($permission);
                $this->command->info("Revoked permission: {$permission}");
            }
        }

        // Remove all accounting resource permissions from driver role
        $accountingResourcePermissions = [
            'view_akun', 'view_any_akun', 'create_akun', 'update_akun', 'delete_akun', 'delete_any_akun',
            'view_journal', 'view_any_journal', 'create_journal', 'update_journal', 'delete_journal', 'delete_any_journal',
            'view_inventory', 'view_any_inventory', 'create_inventory', 'update_inventory', 'delete_inventory', 'delete_any_inventory',
            'view_posting::rule', 'view_any_posting::rule', 'create_posting::rule', 'update_posting::rule', 'delete_posting::rule', 'delete_any_posting::rule',
            'view_invoice', 'view_any_invoice', 'create_invoice', 'update_invoice', 'delete_invoice', 'delete_any_invoice',
            'view_receipt', 'view_any_receipt', 'create_receipt', 'update_receipt', 'delete_receipt', 'delete_any_receipt',
            'view_tax::invoice', 'view_any_tax::invoice', 'create_tax::invoice', 'update_tax::invoice', 'delete_tax::invoice', 'delete_any_tax::invoice',
            'view_expense::request', 'view_any_expense::request', 'create_expense::request', 'update_expense::request', 'delete_expense::request', 'delete_any_expense::request',
            'view_faktur::pajak', 'view_any_faktur::pajak', 'create_faktur::pajak', 'update_faktur::pajak', 'delete_faktur::pajak', 'delete_any_faktur::pajak',
        ];

        foreach ($accountingResourcePermissions as $permission) {
            $permissionModel = Permission::where('name', $permission)->first();
            if ($permissionModel && $driverRole->hasPermissionTo($permission)) {
                $driverRole->revokePermissionTo($permission);
                $this->command->info("Revoked permission: {$permission}");
            }
        }

        // Ensure driver only has the necessary permissions
        $driverPermissions = [
            'view_delivery::order',
            'view_any_delivery::order',
            'update_delivery::order',
            'view_pengiriman::driver',
            'view_any_pengiriman::driver',
            'update_pengiriman::driver',
            'view_uang::jalan',
            'view_any_uang::jalan',
            'update_uang::jalan',
        ];

        foreach ($driverPermissions as $permission) {
            $permissionModel = Permission::where('name', $permission)->first();
            if ($permissionModel && !$driverRole->hasPermissionTo($permission)) {
                $driverRole->givePermissionTo($permission);
                $this->command->info("Granted permission: {$permission}");
            }
        }

        // Assign accounting permissions to appropriate roles
        $this->assignAccountingPermissions();

        $this->command->info('Driver role permissions fixed successfully!');
    }

    private function assignAccountingPermissions(): void
    {
        // Finance role should have full access to accounting features
        $financeRole = Role::where('name', 'finance')->first();
        if ($financeRole) {
            $accountingPermissions = [
                'page_GeneralLedger',
                'page_IncomeStatement',
                'page_BalanceSheet',
                'page_AccountsReceivableDashboard',
                'view_akun', 'view_any_akun', 'create_akun', 'update_akun', 'delete_akun', 'delete_any_akun',
                'view_journal', 'view_any_journal', 'create_journal', 'update_journal', 'delete_journal', 'delete_any_journal',
                'view_inventory', 'view_any_inventory', 'create_inventory', 'update_inventory', 'delete_inventory', 'delete_any_inventory',
                'view_posting::rule', 'view_any_posting::rule', 'create_posting::rule', 'update_posting::rule', 'delete_posting::rule', 'delete_any_posting::rule',
                'view_invoice', 'view_any_invoice', 'create_invoice', 'update_invoice', 'delete_invoice', 'delete_any_invoice',
                'view_receipt', 'view_any_receipt', 'create_receipt', 'update_receipt', 'delete_receipt', 'delete_any_receipt',
                'view_tax::invoice', 'view_any_tax::invoice', 'create_tax::invoice', 'update_tax::invoice', 'delete_tax::invoice', 'delete_any_tax::invoice',
                'view_expense::request', 'view_any_expense::request', 'create_expense::request', 'update_expense::request', 'delete_expense::request', 'delete_any_expense::request',
                'view_faktur::pajak', 'view_any_faktur::pajak', 'create_faktur::pajak', 'update_faktur::pajak', 'delete_faktur::pajak', 'delete_any_faktur::pajak',
            ];

            foreach ($accountingPermissions as $permission) {
                $permissionModel = Permission::where('name', $permission)->first();
                if ($permissionModel && !$financeRole->hasPermissionTo($permission)) {
                    $financeRole->givePermissionTo($permission);
                    $this->command->info("Granted finance permission: {$permission}");
                }
            }
        }

        // Admin role should have access to accounting reports
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminAccountingPermissions = [
                'page_GeneralLedger',
                'page_IncomeStatement',
                'page_BalanceSheet',
                'page_AccountsReceivableDashboard',
                'view_akun', 'view_any_akun',
                'view_journal', 'view_any_journal',
                'view_inventory', 'view_any_inventory',
                'view_invoice', 'view_any_invoice',
                'view_receipt', 'view_any_receipt',
                'view_tax::invoice', 'view_any_tax::invoice',
            ];

            foreach ($adminAccountingPermissions as $permission) {
                $permissionModel = Permission::where('name', $permission)->first();
                if ($permissionModel && !$adminRole->hasPermissionTo($permission)) {
                    $adminRole->givePermissionTo($permission);
                    $this->command->info("Granted admin permission: {$permission}");
                }
            }
        }
    }
}
