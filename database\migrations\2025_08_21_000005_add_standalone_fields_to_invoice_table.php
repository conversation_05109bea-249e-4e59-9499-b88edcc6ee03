<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add standalone invoice fields for invoices not tied to sales transactions
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Flag to indicate if this is a standalone invoice
            $table->boolean('is_standalone')
                ->default(false)
                ->after('nomor_invoice')
                ->comment('Flag untuk invoice standalone yang tidak terkait transaksi penjualan');
            
            // Standalone customer information
            $table->string('standalone_customer_name', 255)
                ->nullable()
                ->after('is_standalone')
                ->comment('Nama pelanggan untuk invoice standalone');
                
            $table->text('standalone_customer_address')
                ->nullable()
                ->after('standalone_customer_name')
                ->comment('<PERSON>amat pelanggan untuk invoice standalone');
                
            $table->string('standalone_customer_phone', 50)
                ->nullable()
                ->after('standalone_customer_address')
                ->comment('Telepon pelanggan untuk invoice standalone');
                
            $table->string('standalone_customer_email', 255)
                ->nullable()
                ->after('standalone_customer_phone')
                ->comment('Email pelanggan untuk invoice standalone');
            
            // Make id_transaksi nullable for standalone invoices
            $table->unsignedBigInteger('id_transaksi')->nullable()->change();
            
            // Index for performance
            $table->index(['is_standalone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['is_standalone']);
            
            // Drop columns
            $table->dropColumn([
                'is_standalone',
                'standalone_customer_name',
                'standalone_customer_address',
                'standalone_customer_phone',
                'standalone_customer_email',
            ]);
            
            // Restore id_transaksi as required
            $table->unsignedBigInteger('id_transaksi')->nullable(false)->change();
        });
    }
};
