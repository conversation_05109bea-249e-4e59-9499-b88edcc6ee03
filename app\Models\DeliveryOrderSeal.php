<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class DeliveryOrderSeal extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $table = 'delivery_order_seals';

    protected $fillable = [
        'id_delivery_order',
        'nomor_segel',
        'jenis_segel',
        'keterangan',
        'urutan',
        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'urutan' => 'integer',
    ];

    // Jenis segel constants
    public const JENIS_SEGEL = [
        'atas' => 'Segel Atas',
        'bawah' => 'Segel Bawah',
        'samping' => 'Segel Samping',
        'lainnya' => 'Lainnya',
    ];

    // Relationships
    public function deliveryOrder(): BelongsTo
    {
        return $this->belongsTo(DeliveryOrder::class, 'id_delivery_order');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper methods
    public function getJenisSegalLabel(): string
    {
        return self::JENIS_SEGEL[$this->jenis_segel] ?? $this->jenis_segel;
    }

    // Scopes
    public function scopeByDeliveryOrder($query, int $deliveryOrderId)
    {
        return $query->where('id_delivery_order', $deliveryOrderId);
    }

    public function scopeByJenis($query, string $jenis)
    {
        return $query->where('jenis_segel', $jenis);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('urutan')->orderBy('created_at');
    }

    // Static methods
    public static function getNextUrutan(int $deliveryOrderId): int
    {
        return self::where('id_delivery_order', $deliveryOrderId)->max('urutan') + 1;
    }

    public static function createMultiple(int $deliveryOrderId, array $seals, ?int $createdBy = null): void
    {
        foreach ($seals as $index => $sealData) {
            if (is_string($sealData)) {
                $sealData = ['nomor_segel' => $sealData];
            }

            self::create([
                'id_delivery_order' => $deliveryOrderId,
                'nomor_segel' => $sealData['nomor_segel'],
                'jenis_segel' => $sealData['jenis_segel'] ?? 'atas',
                'keterangan' => $sealData['keterangan'] ?? null,
                'urutan' => $sealData['urutan'] ?? ($index + 1),
                'created_by' => $createdBy,
            ]);
        }
    }

    /**
     * Register media collections for the DeliveryOrderSeal model
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('foto_segel')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->singleFile();
    }

    /**
     * Register media conversions for the DeliveryOrderSeal model
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->quality(80)
            ->sharpen(10)
            ->performOnCollections('foto_segel')
            ->nonQueued();

        $this->addMediaConversion('preview')
            ->width(400)
            ->height(400)
            ->quality(85)
            ->performOnCollections('foto_segel')
            ->nonQueued();

        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->quality(90)
            ->performOnCollections('foto_segel')
            ->nonQueued();
    }

    /**
     * Get the URL of the seal photo
     */
    public function getFotoSegelUrlAttribute(): ?string
    {
        return $this->getFirstMediaUrl('foto_segel');
    }

    /**
     * Get the URL of the seal photo thumbnail
     */
    public function getFotoSegelThumbUrlAttribute(): ?string
    {
        return $this->getFirstMediaUrl('foto_segel', 'thumb');
    }
}
