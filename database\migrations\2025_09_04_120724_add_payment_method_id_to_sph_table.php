<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sph', function (Blueprint $table) {
            // Check if column doesn't exist before adding it
            if (!Schema::hasColumn('sph', 'payment_method_id')) {
                $table->foreignId('payment_method_id')
                    ->nullable()
                    ->after('letter_setting_id') // Place it after letter_setting_id
                    ->constrained('payment_methods')
                    ->nullOnDelete(); // If a payment method is deleted, don't delete the SPH
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sph', function (Blueprint $table) {
            // Check if column exists before dropping it
            if (Schema::hasColumn('sph', 'payment_method_id')) {
                $table->dropForeign(['payment_method_id']);
                $table->dropColumn('payment_method_id');
            }
        });
    }
};
