<?php

namespace App\Filament\Resources\NeracaMappingResource\Pages;

use App\Filament\Resources\NeracaMappingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditNeracaMapping extends EditRecord
{
    protected static string $resource = NeracaMappingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
