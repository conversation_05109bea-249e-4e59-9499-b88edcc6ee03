<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'starsender' => [
        'base_url' => env('STARSENDER_BASE_URL', 'https://api.starsender.online/api'),
        'default_sender' => env('STARSENDER_DEFAULT_SENDER', 'ilham'),

        'accounts' => [
            'ilham' => [
                'token' => env('STARSENDER_TOKEN_ILHAM'),
            ],
            'indra' => [
                'token' => env('STARSENDER_TOKEN_INDRA'),
            ],
        ],
    ],

];
