<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DeliveryOrder;
use App\Models\DeliveryOrderDetail;
use App\Models\PenjualanDetail;

class DeliveryOrderDetailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing delivery orders
        $deliveryOrders = DeliveryOrder::with('transaksi.penjualanDetails.item')->limit(10)->get();

        if ($deliveryOrders->isEmpty()) {
            $this->command->info('No delivery orders found. Please run DeliveryOrderSeeder first.');
            return;
        }

        foreach ($deliveryOrders as $do) {
            // Clear existing details for this DO
            $do->details()->delete();

            // Get penjualan details from the related transaction
            $penjualanDetails = $do->transaksi->penjualanDetails ?? collect();

            if ($penjualanDetails->isEmpty()) {
                $this->command->info("No penjualan details found for DO {$do->kode}. Skipping...");
                continue;
            }

            $totalVolumeDelivered = 0;

            foreach ($penjualanDetails as $index => $penjualanDetail) {
                // Calculate volume to deliver (80-100% of ordered volume)
                $volumeOrdered = $penjualanDetail->volume_item;
                $deliveryPercentage = rand(80, 100) / 100;
                $volumeDelivered = $volumeOrdered * $deliveryPercentage;

                // Round to nearest 10
                $volumeDelivered = round($volumeDelivered / 10) * 10;

                DeliveryOrderDetail::create([
                    'id_delivery_order' => $do->id,
                    'id_penjualan_detail' => $penjualanDetail->id,
                    'id_item' => $penjualanDetail->id_item,
                    'item_name' => $penjualanDetail->item->name,
                    'item_description' => $penjualanDetail->item->description,
                    'volume_ordered' => $volumeOrdered,
                    'volume_delivered' => $volumeDelivered,
                    'unit' => $penjualanDetail->item->satuan->nama ?? 'Liter',
                    'notes' => $this->generateNotes($index, $deliveryPercentage),
                ]);

                $totalVolumeDelivered += $volumeDelivered;
            }

            // Update delivery order total volume
            $do->update([
                'volume_do' => $totalVolumeDelivered,
            ]);

            // Calculate remaining volume
            $totalSoVolume = $penjualanDetails->sum('volume_item');
            $remainingVolume = $totalSoVolume - $totalVolumeDelivered;
            
            $do->update([
                'sisa_volume_do' => max(0, $remainingVolume),
            ]);
        }

        $this->command->info('Delivery order details seeded successfully!');
    }

    /**
     * Generate notes based on delivery scenario
     */
    private function generateNotes(int $index, float $deliveryPercentage): ?string
    {
        if ($deliveryPercentage >= 1.0) {
            return null; // Full delivery, no notes needed
        }

        $scenarios = [
            'Pengiriman parsial sesuai permintaan customer',
            'Volume disesuaikan dengan kapasitas tangki',
            'Pengiriman bertahap untuk menghindari overflow',
            'Volume dikurangi karena keterbatasan stok',
            'Pengiriman sesuai jadwal yang diminta',
            'Volume disesuaikan dengan kondisi jalan',
        ];

        // Return notes for partial deliveries
        return $scenarios[array_rand($scenarios)];
    }
}
