<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class LaporanHarian extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'tanggal_laporan',
        'catatan',
        'created_by',
    ];

    protected $casts = [
        'tanggal_laporan' => 'date',
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('foto_mobil_tangki')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])

            ;

        $this->addMediaCollection('foto_laporan_tamu')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10);
    }
}
