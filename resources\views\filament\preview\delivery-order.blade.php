<div class="p-6 bg-white">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">DELIVERY ORDER</h1>
            <p class="text-lg font-semibold text-blue-600">{{ $record->kode }}</p>
        </div>

        <!-- Company Info & DO Info -->
        <div class="grid grid-cols-2 gap-8 mb-8">
            <div>
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Informasi Perusahaan</h3>
                <div class="space-y-2 text-sm">
                    <p><strong>PT. Lintas Raya Persada</strong></p>
                    <p>Jl. Contoh Alamat No. 123</p>
                    <p>Jakarta, Indonesia</p>
                    <p>Telp: (021) 1234-5678</p>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Informasi DO</h3>
                <div class="space-y-2 text-sm">
                    <p><strong>Nomor DO:</strong> {{ $record->kode }}</p>
                    <p><strong>Tanggal:</strong>
                        {{ $record->tanggal_delivery ? $record->tanggal_delivery->format('d M Y') : '-' }}</p>
                    <p><strong>Status:</strong>
                        <span
                            class="px-2 py-1 rounded text-xs font-medium
                            @if ($record->status_muat === 'selesai') bg-green-100 text-green-800
                            @elseif($record->status_muat === 'muat') bg-blue-100 text-blue-800
                            @else bg-yellow-100 text-yellow-800 @endif">
                            {{ ucfirst($record->status_muat) }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Customer & Driver Info -->
        <div class="grid grid-cols-2 gap-8 mb-8">
            <div>
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Informasi Pelanggan</h3>
                <div class="space-y-2 text-sm">
                    @if ($record->transaksi && $record->transaksi->pelanggan)
                        <p><strong>Nama:</strong> {{ $record->transaksi->pelanggan->nama_pelanggan }}</p>
                        <p><strong>Nomor SO:</strong> {{ $record->transaksi->kode }}</p>
                        @if ($record->transaksi->pelanggan->alamatPelanggan->first())
                            <p><strong>Alamat:</strong>
                                {{ $record->transaksi->pelanggan->alamatPelanggan->first()->alamat_lengkap }}</p>
                        @endif
                    @else
                        <p class="text-gray-500">Data pelanggan tidak tersedia</p>
                    @endif
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Informasi Pengiriman</h3>
                <div class="space-y-2 text-sm">
                    <p><strong>Supir:</strong> {{ $record->user->name ?? '-' }}</p>
                    <p><strong>Kendaraan:</strong> {{ $record->kendaraan->no_pol_kendaraan ?? '-' }}</p>
                    <p><strong>Total Volume:</strong> {{ number_format($record->volume_do, 2) }} L</p>
                </div>
            </div>
        </div>

        <!-- Volume Details -->
        @if ($record->transaksi && $record->transaksi->penjualanDetails && $record->transaksi->penjualanDetails->count() > 0)
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Detail Volume (Berdasarkan Item SO)</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">No</th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Nama Item
                                </th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Volume SO (L)
                                </th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Volume DO (L)
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($record->transaksi->penjualanDetails as $index => $detail)
                                <tr class="border-b">
                                    <td class="px-4 py-2 text-sm">{{ $index + 1 }}</td>
                                    <td class="px-4 py-2 text-sm">
                                        {{ $detail->item ? $detail->item->nama_item : 'Unknown Item' }}
                                    </td>
                                    <td class="px-4 py-2 text-sm">{{ number_format($detail->volume_item ?? 0, 2) }}
                                    </td>
                                    <td class="px-4 py-2 text-sm">{{ number_format($detail->volume_do ?? 0, 2) }}</td>
                                </tr>
                            @endforeach
                            <tr class="bg-gray-50 font-semibold">
                                <td colspan="2" class="px-4 py-2 text-sm text-right">Total Volume DO:</td>
                                <td class="px-4 py-2 text-sm">{{ number_format($record->volume_do, 2) }} L</td>
                                <td class="px-4 py-2 text-sm"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <!-- Fallback: Show SO items if no volume details -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Item dari Sales Order</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">No</th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Nama Item
                                </th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Volume (L)
                                </th>
                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-700">Harga Jual
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($record->transaksi->penjualanDetails as $index => $detail)
                                <tr class="border-b">
                                    <td class="px-4 py-2 text-sm">{{ $index + 1 }}</td>
                                    <td class="px-4 py-2 text-sm">{{ $detail->item->nama_item ?? '-' }}</td>
                                    <td class="px-4 py-2 text-sm">{{ number_format($detail->volume_item, 2) }}</td>
                                    <td class="px-4 py-2 text-sm">Rp
                                        {{ number_format($detail->harga_jual, 0, ',', '.') }}</td>
                                </tr>
                            @endforeach
                            <tr class="bg-gray-50 font-semibold">
                                <td colspan="2" class="px-4 py-2 text-sm text-right">Total Volume:</td>
                                <td class="px-4 py-2 text-sm">{{ number_format($record->volume_do, 2) }} L</td>
                                <td class="px-4 py-2 text-sm"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        @endif

        <!-- Seals -->
        @if ($record->seals && $record->seals->count() > 0)
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Nomor Segel</h3>
                <div class="grid grid-cols-3 gap-4">
                    @foreach ($record->seals as $seal)
                        <div class="border border-gray-300 rounded p-3">
                            <p class="text-sm"><strong>{{ $seal->nomor_segel }}</strong></p>
                            <p class="text-xs text-gray-600">{{ ucfirst($seal->jenis_segel) }}</p>
                            @if ($seal->keterangan)
                                <p class="text-xs text-gray-500 mt-1">{{ $seal->keterangan }}</p>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Signatures -->
        <div class="grid grid-cols-3 gap-8 mt-12">
            <div class="text-center">
                <div class="border-t border-gray-400 pt-2 mt-16">
                    <p class="text-sm font-medium">Dibuat Oleh</p>
                    <p class="text-xs text-gray-600">Admin</p>
                </div>
            </div>
            <div class="text-center">
                <div class="border-t border-gray-400 pt-2 mt-16">
                    <p class="text-sm font-medium">Supir</p>
                    <p class="text-xs text-gray-600">{{ $record->user->name ?? '-' }}</p>
                </div>
            </div>
            <div class="text-center">
                <div class="border-t border-gray-400 pt-2 mt-16">
                    <p class="text-sm font-medium">Penerima</p>
                    <p class="text-xs text-gray-600">{{ $record->do_signatory_name ?? '........................' }}</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 pt-4 border-t border-gray-200">
            <p class="text-xs text-gray-500">
                Dokumen ini dibuat secara otomatis pada {{ now()->format('d M Y H:i') }}
            </p>
        </div>
    </div>
</div>
