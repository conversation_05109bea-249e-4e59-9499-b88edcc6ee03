<?php

namespace App\Services;

use App\Models\Surat;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class SuratPdfService
{
    /**
     * Generates a PDF for a given Surat and returns it as a download stream.
     *
     * @param Surat $surat The Surat record to generate a PDF for.
     * @param bool $download Whether to download or preview the PDF
     * @return StreamedResponse|Response|null
     */
    public function generatePdf(Surat $surat, bool $download = true): StreamedResponse|Response|null
    {
        try {
            // 1. Eager load all necessary relationships for the PDF view.
            $surat->load(['letterSetting', 'createdBy', 'signedBy']);

            // 2. Sanitize content to prevent encoding issues
            $surat = $this->sanitizeContent($surat);

            // 3. Determine the correct Blade view based on the locale.
            $locale = $surat->letterSetting?->locale ?? 'id';
            $viewName = "surat.surat-pdf-{$locale}";
            if (!view()->exists($viewName)) {
                $viewName = 'surat.surat-pdf-id'; // Fallback to Indonesian
            }

            // 4. Generate a dynamic filename.
            $filename = 'Surat_' . str_replace(['/', '\\'], '_', $surat->surat_number) . '.pdf';

            // 5. Load the PDF view with the record data and set options.
            $pdf = Pdf::loadView($viewName, ['record' => $surat])
                ->setPaper('a4', 'portrait')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true,
                    'defaultFont' => 'DejaVu Sans',
                    'isRemoteEnabled' => true, // Important for loading images
                    'chroot' => public_path(),
                    'debugKeepTemp' => false,
                    'debugCss' => false,
                    'debugLayout' => false,
                    'debugLayoutLines' => false,
                    'debugLayoutBlocks' => false,
                    'debugLayoutInline' => false,
                    'debugLayoutPaddingBox' => false,
                ]);

            // 6. Return based on download flag
            if ($download) {
                // Stream the PDF as a download response.
                return response()->streamDownload(fn() => print($pdf->output()), $filename);
            } else {
                // Return PDF for preview
                return response($pdf->output(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'inline; filename="' . $filename . '"'
                ]);
            }
        } catch (Throwable $e) {
            Log::error('Failed to generate Surat PDF.', [
                'surat_id' => $surat->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Preview PDF in browser without downloading
     *
     * @param Surat $surat
     * @return StreamedResponse|Response|null
     */
    public function previewPdf(Surat $surat): StreamedResponse|Response|null
    {
        return $this->generatePdf($surat, false);
    }

    /**
     * Download PDF file
     *
     * @param Surat $surat
     * @return StreamedResponse|Response|null
     */
    public function downloadPdf(Surat $surat): StreamedResponse|Response|null
    {
        return $this->generatePdf($surat, true);
    }

    /**
     * Get the PDF view name based on locale
     *
     * @param Surat $surat
     * @return string
     */
    public function getPdfViewName(Surat $surat): string
    {
        $locale = $surat->letterSetting?->locale ?? 'id';
        $viewName = "surat.surat-pdf-{$locale}";

        if (!view()->exists($viewName)) {
            $viewName = 'surat.surat-pdf-id'; // Fallback to Indonesian
        }

        return $viewName;
    }

    /**
     * Check if PDF can be generated for the surat
     *
     * @param Surat $surat
     * @return bool
     */
    public function canGeneratePdf(Surat $surat): bool
    {
        // Check if surat has required data
        if (empty($surat->title) || empty($surat->content)) {
            return false;
        }

        // Check if letter setting exists
        if (!$surat->letterSetting) {
            return false;
        }

        return true;
    }

    /**
     * Get PDF filename for the surat
     *
     * @param Surat $surat
     * @return string
     */
    public function getFilename(Surat $surat): string
    {
        return 'Surat_' . str_replace(['/', '\\'], '_', $surat->surat_number) . '_' . now()->format('Ymd_His') . '.pdf';
    }

    /**
     * Sanitize content to prevent encoding issues
     *
     * @param Surat $surat
     * @return Surat
     */
    private function sanitizeContent(Surat $surat): Surat
    {
        // Clean and ensure proper UTF-8 encoding for content
        if (!empty($surat->content)) {
            // First, try to detect and fix encoding issues
            $content = $this->cleanUtf8String($surat->content);
            $surat->content = $content;
        }

        // Clean title as well
        if (!empty($surat->title)) {
            $title = $this->cleanUtf8String($surat->title);
            $surat->title = $title;
        }

        // Clean notes_internal
        if (!empty($surat->notes_internal)) {
            $notes = $this->cleanUtf8String($surat->notes_internal);
            $surat->notes_internal = $notes;
        }

        return $surat;
    }

    /**
     * Clean UTF-8 string and remove problematic characters
     *
     * @param string $string
     * @return string
     */
    private function cleanUtf8String(string $string): string
    {
        // Remove null bytes and other control characters
        $string = str_replace("\0", '', $string);

        // Convert to UTF-8 if not already
        if (!mb_check_encoding($string, 'UTF-8')) {
            $string = mb_convert_encoding($string, 'UTF-8', 'auto');
        }

        // Remove invalid UTF-8 sequences
        $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // Remove or replace problematic characters
        $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);

        // Clean up HTML entities if present
        $string = html_entity_decode($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Final UTF-8 validation and cleaning
        $string = filter_var($string, FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW | FILTER_FLAG_STRIP_HIGH);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($string, 'UTF-8')) {
            $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');
        }

        return $string;
    }
}
