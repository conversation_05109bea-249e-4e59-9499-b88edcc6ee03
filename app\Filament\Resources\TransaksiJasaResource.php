<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransaksiJasaResource\Pages;
use App\Filament\Resources\TransaksiJasaResource\RelationManagers;
use App\Filament\Resources\TransaksiPenjualanResource\RelationManagers\AlamatTransaksiRelationManager;
use App\Filament\Resources\TransaksiPenjualanResource\RelationManagers\DeliveryOrdersRelationManager;
use App\Models\TransaksiPenjualan;
use App\Models\Pelanggan;
use App\Models\Tbbm;
use App\Models\Akun;
use App\Models\Item;
use App\Models\Sph;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class TransaksiJasaResource extends Resource
{
    protected static ?string $model = TransaksiPenjualan::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    protected static ?string $navigationLabel = 'Transaksi Jasa';

    protected static ?string $modelLabel = 'Transaksi Jasa';

    protected static ?string $pluralModelLabel = 'Transaksi Jasa';

    protected static ?string $navigationGroup = 'Operasional';

    protected static ?int $navigationSort = 15;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Pesanan Jasa')
                    ->schema([
                        Forms\Components\TextInput::make('kode')
                            ->label('Nomor SO')
                            ->maxLength(50)
                            ->placeholder('Kosongkan untuk default "-" atau isi manual')
                            ->helperText('Jika dikosongkan akan otomatis diisi "-". Untuk referensi gunakan nomor transaksi.'),

                        // media upload sales order
                        SpatieMediaLibraryFileUpload::make('dokumen_so')
                            ->label('Dokumen SO')
                            ->collection('dokumen_so')
                            ->acceptedFileTypes([
                                'application/pdf',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg',
                                'image/png'
                            ])
                            ->maxSize(10240),

                        Forms\Components\DateTimePicker::make('tanggal')
                            ->label('Tanggal Transaksi')
                            ->required()
                            ->default(now()),

                        Forms\Components\Select::make('id_pelanggan')
                            ->label('Pelanggan')
                            ->relationship('pelanggan', 'nama')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                if ($state) {
                                    $pelanggan = Pelanggan::with('alamatUtama')->find($state);
                                    if ($pelanggan && $pelanggan->alamatUtama) {
                                        $set('id_alamat_pelanggan', $pelanggan->alamatUtama->id);
                                    }
                                }
                            }),

                        Forms\Components\Select::make('id_alamat_pelanggan')
                            ->label('Alamat Pelanggan')
                            ->options(function (Forms\Get $get) {
                                $pelangganId = $get('id_pelanggan');
                                if (!$pelangganId) return [];

                                return \App\Models\AlamatPelanggan::where('id_pelanggan', $pelangganId)
                                    ->get()
                                    ->mapWithKeys(function ($alamat) {
                                        return [$alamat->id => $alamat->alamat . ($alamat->is_primary ? ' (Utama)' : '')];
                                    });
                            })
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\TextInput::make('nomor_po')
                            ->label('Nomor PO')
                            ->maxLength(50),

                        Forms\Components\TextInput::make('nomor_sph')
                            ->label('Nomor SPH')
                            ->maxLength(50),

                        Forms\Components\Select::make('letter_setting_id')
                            ->label('Format Surat & Mata Uang')
                            ->relationship('letterSetting', 'name', fn(Builder $query) => $query->where('is_active', true))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Pilih format surat untuk menentukan mata uang (Rupiah/Dollar) dan bahasa dokumen.')
                            ->live(),

                        // Hidden field for tipe - always 'jasa'
                        Forms\Components\Hidden::make('tipe')
                            ->default('jasa'),
                    ])
                    ->columns(2),



                Forms\Components\Section::make('Detail Jasa')
                    ->description('Detail jasa yang akan diberikan dalam transaksi ini')
                    ->schema([
                        Forms\Components\Repeater::make('penjualanDetails')
                            ->label('Detail Jasa')
                            ->relationship('penjualanDetails')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Select::make('id_item')
                                            ->label('Jenis Jasa')
                                            ->relationship('item', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                                if ($state) {
                                                    $item = Item::with('satuan')->find($state);
                                                    if ($item) {
                                                        $set('item_info', $item->kode . ' - ' . $item->name);
                                                        $set('satuan_info', $item->satuan->nama ?? '');
                                                    }
                                                }
                                            }),

                                        Forms\Components\TextInput::make('volume_item')
                                            ->label('Kuantitas')
                                            ->numeric()
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                                                $harga = $get('harga_jual');
                                                if ($harga && $state) {
                                                    $set('total_harga', $harga * $state);
                                                }
                                            }),

                                        Forms\Components\TextInput::make('harga_jual')
                                            ->label('Harga Satuan')
                                            ->numeric()
                                            ->required()
                                            ->prefix(function (callable $get) {
                                                $letterSettingId = $get('../../letter_setting_id');
                                                if ($letterSettingId) {
                                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                                    return $letterSetting && $letterSetting->locale === 'en' ? 'USD' : 'IDR';
                                                }
                                                return 'IDR';
                                            })
                                            ->live()
                                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                                                $volume = $get('volume_item');
                                                if ($volume && $state) {
                                                    $set('total_harga', $volume * $state);
                                                }
                                            }),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('total_harga')
                                            ->label('Total Harga')
                                            ->numeric()
                                            ->prefix(function (callable $get) {
                                                $letterSettingId = $get('../../letter_setting_id');
                                                if ($letterSettingId) {
                                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                                    return $letterSetting && $letterSetting->locale === 'en' ? 'USD' : 'IDR';
                                                }
                                                return 'IDR';
                                            })
                                            ->disabled()
                                            ->dehydrated(),

                                        Forms\Components\TextInput::make('satuan_info')
                                            ->label('Satuan')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ]),

                                Forms\Components\Textarea::make('alamat_pengiriman')
                                    ->label('Lokasi Pelaksanaan')
                                    ->rows(2)
                                    ->columnSpanFull(),

                                Forms\Components\Textarea::make('keterangan_lokasi')
                                    ->label('Keterangan Jasa')
                                    ->rows(2)
                                    ->columnSpanFull(),

                                Forms\Components\Hidden::make('item_info'),
                            ])
                            ->defaultItems(1)
                            ->addActionLabel('Tambah Jasa')
                            ->collapsible()
                            ->cloneable()
                            ->itemLabel(function (array $state, Forms\Get $get): ?string {
                                if (!isset($state['item_info']) || !$state['item_info']) {
                                    return 'Jasa Baru';
                                }

                                $volume = floatval($state['volume_item'] ?? 0);
                                $harga = floatval($state['harga_jual'] ?? 0);
                                $subtotal = number_format($volume * $harga, 0, ',', '.');

                                // Get currency based on letter setting
                                $letterSettingId = $get('letter_setting_id');
                                $currency = 'IDR';
                                if ($letterSettingId) {
                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                    $currency = $letterSetting && $letterSetting->locale === 'en' ? 'USD' : 'IDR';
                                }

                                return "{$state['item_info']} - {$volume} x {$currency} " . number_format($harga, 0, ',', '.') . " = {$currency} {$subtotal}";
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode')
                    ->label('Nomor SO')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('pelanggan.nama')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('nomor_po')
                    ->label('Nomor PO')
                    ->searchable()
                    ->placeholder('Tidak ada PO')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('total_transaksi')
                    ->label('Total Transaksi')
                    ->getStateUsing(function ($record) {
                        $total = $record->penjualanDetails->sum(function ($detail) {
                            return $detail->volume_item * $detail->harga_jual;
                        });

                        $currency = 'IDR';
                        if ($record->letterSetting && $record->letterSetting->locale === 'en') {
                            $currency = 'USD';
                        }

                        return $currency . ' ' . number_format($total, 0, ',', '.');
                    })
                    ->sortable(false),

                Tables\Columns\TextColumn::make('letterSetting.name')
                    ->label('Format Surat')
                    ->placeholder('Tidak Ada')
                    ->badge()
                    ->color('info')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('letterSetting.locale')
                    ->label('Mata Uang')
                    ->placeholder('Tidak Ada')
                    ->formatStateUsing(fn($state) => match ($state) {
                        'id' => 'Rupiah (IDR)',
                        'en' => 'Dollar (USD)',
                        default => $state
                    })
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'id' => 'success',
                        'en' => 'warning',
                        default => 'gray'
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('jumlah_jasa')
                    ->label('Jumlah Jasa')
                    ->getStateUsing(fn($record) => $record->penjualanDetails->count() . ' jasa')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'draft' => 'gray',
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('id_pelanggan')
                    ->label('Pelanggan')
                    ->relationship('pelanggan', 'nama')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('tanggal')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal', '<=', $date),
                            );
                    }),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('tanggal', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            AlamatTransaksiRelationManager::class,
            DeliveryOrdersRelationManager::class,
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('tipe', 'jasa')
            ->with([
                'pelanggan',
                'alamatPelanggan',
                'alamatTransaksi',
                'penjualanDetails.item',
                'createdBy',
                'letterSetting'
            ]);
    }

    public static function canEdit($record): bool
    {
        // Prevent editing if transaction is approved
        return $record->status !== 'approved';
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransaksiJasa::route('/'),
            'create' => Pages\CreateTransaksiJasa::route('/create'),
            'view' => Pages\ViewTransaksiJasa::route('/{record}'),
            'edit' => Pages\EditTransaksiJasa::route('/{record}/edit'),
        ];
    }
}
