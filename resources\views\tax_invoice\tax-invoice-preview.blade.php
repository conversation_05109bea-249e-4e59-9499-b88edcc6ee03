{{--
    This Blade file renders a preview of the Tax Invoice document.
    Location: resources/views/tax_invoice/tax-invoice-preview.blade.php
--}}

<div class="p-4 sm:p-6 bg-white font-sans text-gray-800">
    {{-- Header Section --}}
    <header class="mb-8 border-b-2 border-blue-600 pb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                {{-- Company Logo --}}
                <div class="w-16 h-16 border-2 border-blue-600 rounded-lg flex items-center justify-center bg-gray-50">
                    <div class="text-xs font-bold text-blue-600 text-center leading-tight">
                        LINTAS<br>RIAU<br>PRIMA
                    </div>
                </div>
                
                {{-- Company Info --}}
                <div>
                    <h1 class="text-xl font-bold text-blue-800">PT. LINTAS RIAU PRIMA</h1>
                    <p class="text-sm text-gray-600 italic">TRUSTED & RELIABLE PARTNER</p>
                    <p class="text-xs text-gray-500">Fuel Agent - Fuel Transportation - Bunker Service</p>
                </div>
            </div>
        </div>
    </header>

    {{-- Document Title --}}
    <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-blue-800 uppercase tracking-wide">FAKTUR PAJAK</h2>
    </div>

    {{-- Tax Invoice Information --}}
    <section class="mb-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        {{-- Tax Invoice Details --}}
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4 border-b border-gray-300 pb-2">
                Informasi Faktur Pajak
            </h3>
            <div class="space-y-2">
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Nomor:</span>
                    <span class="text-gray-900 font-semibold">{{ $record->nomor_tax_invoice }}</span>
                </div>
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Tanggal:</span>
                    <span class="text-gray-900">{{ $record->tanggal_tax_invoice->format('d F Y') }}</span>
                </div>
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Status:</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        {{ $record->status === 'approved' ? 'bg-green-100 text-green-800' : 
                           ($record->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($record->status) }}
                    </span>
                </div>
                @if($record->invoice)
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Invoice:</span>
                    <span class="text-gray-900">{{ $record->invoice->nomor_invoice }}</span>
                </div>
                @endif
            </div>
        </div>

        {{-- Company Information --}}
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4 border-b border-gray-300 pb-2">
                Informasi Perusahaan
            </h3>
            <div class="space-y-2">
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Nama:</span>
                    <span class="text-gray-900">{{ $record->nama_perusahaan }}</span>
                </div>
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">Alamat:</span>
                    <span class="text-gray-900">{{ $record->alamat_perusahaan }}</span>
                </div>
                <div class="flex">
                    <span class="w-24 font-medium text-gray-700">NPWP:</span>
                    <span class="text-gray-900">{{ $record->npwp_perusahaan }}</span>
                </div>
            </div>
        </div>
    </section>

    {{-- Customer Information --}}
    <section class="mb-8">
        <div class="bg-blue-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4 border-b border-blue-300 pb-2">
                Informasi Pelanggan
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <span class="block font-medium text-gray-700 mb-1">Nama Pelanggan:</span>
                    <span class="text-gray-900 font-semibold">{{ $record->nama_pelanggan }}</span>
                </div>
                <div>
                    <span class="block font-medium text-gray-700 mb-1">Alamat:</span>
                    <span class="text-gray-900">{{ $record->alamat_pelanggan }}</span>
                </div>
                <div>
                    <span class="block font-medium text-gray-700 mb-1">NPWP:</span>
                    <span class="text-gray-900">{{ $record->npwp_pelanggan ?: '-' }}</span>
                </div>
            </div>
        </div>
    </section>

    {{-- Tax Details Table --}}
    <section class="mb-8">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">Rincian Pajak</h3>
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300 bg-white">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border border-gray-300 px-4 py-3 text-center text-sm font-semibold text-gray-700">No</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Keterangan</th>
                        <th class="border border-gray-300 px-4 py-3 text-right text-sm font-semibold text-gray-700">Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3 text-center text-sm">1</td>
                        <td class="border border-gray-300 px-4 py-3 text-sm">Dasar Pengenaan Pajak (DPP)</td>
                        <td class="border border-gray-300 px-4 py-3 text-right text-sm font-medium">
                            Rp {{ number_format($record->dasar_pengenaan_pajak, 0, ',', '.') }}
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3 text-center text-sm">2</td>
                        <td class="border border-gray-300 px-4 py-3 text-sm">Tarif Pajak</td>
                        <td class="border border-gray-300 px-4 py-3 text-right text-sm font-medium">
                            {{ $record->tarif_pajak }}%
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3 text-center text-sm">3</td>
                        <td class="border border-gray-300 px-4 py-3 text-sm">Pajak Pertambahan Nilai (PPN)</td>
                        <td class="border border-gray-300 px-4 py-3 text-right text-sm font-medium">
                            Rp {{ number_format($record->pajak_pertambahan_nilai, 0, ',', '.') }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    {{-- Summary Section --}}
    <section class="mb-8 flex justify-end">
        <div class="w-full max-w-sm">
            <table class="w-full border-collapse border border-gray-300">
                <tr class="bg-blue-600 text-white">
                    <td class="border border-gray-300 px-4 py-3 font-semibold">Total Tax Invoice:</td>
                    <td class="border border-gray-300 px-4 py-3 text-right font-bold">
                        Rp {{ number_format($record->total_tax_invoice, 0, ',', '.') }}
                    </td>
                </tr>
            </table>
        </div>
    </section>

    {{-- Footer Section --}}
    <footer class="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
        {{-- Notes Section --}}
        @if($record->catatan)
        <div>
            <h4 class="text-sm font-semibold text-gray-700 mb-2">Catatan:</h4>
            <p class="text-sm text-gray-600 leading-relaxed bg-gray-50 p-3 rounded">
                {{ $record->catatan }}
            </p>
        </div>
        @endif

        {{-- Signature Section --}}
        <div class="text-center">
            <p class="text-sm text-gray-700 mb-2">
                Pekanbaru, {{ $record->tanggal_tax_invoice->format('d F Y') }}
            </p>
            <p class="text-sm text-gray-700 mb-4">Hormat kami,</p>
            
            {{-- Signature Placeholder --}}
            <div class="w-32 h-20 mx-auto border-2 border-dashed border-gray-300 flex items-center justify-center mb-4 bg-gray-50">
                <span class="text-xs text-gray-400">Tanda Tangan</span>
            </div>
            
            <p class="text-sm font-semibold text-gray-800 underline">
                {{ $record->createdBy?->name ?? 'Admin' }}
            </p>
            <p class="text-xs text-gray-600">
                {{ $record->createdBy?->jabatan?->nama ?? 'Manager Keuangan' }}
            </p>
        </div>
    </footer>
</div>
