<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Change category column from enum to varchar for flexibility
        DB::statement("ALTER TABLE expense_requests MODIFY COLUMN category VARCHAR(100)");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to enum (this might cause data loss if new categories were added)
        DB::statement("ALTER TABLE expense_requests MODIFY COLUMN category ENUM(
            'tank_truck_maintenance',
            'license_fee',
            'business_travel',
            'utilities',
            'other',
            'ganti_oli'
        )");
    }
};
