<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SuratPdfController;

Route::get('/', function () {
    return redirect('/admin');
});

// Surat PDF Routes
Route::get('/surat/{surat}/preview-pdf', [SuratPdfController::class, 'preview'])->name('surat.preview-pdf');
Route::get('/surat/{surat}/download-pdf', [SuratPdfController::class, 'download'])->name('surat.download-pdf');



// Geofencing validation route for karyawan panel
Route::post('/karyawan/validate-geofencing', [App\Http\Controllers\GeofencingController::class, 'validateAttendanceLocation'])
    ->middleware(['auth'])
    ->name('karyawan.validate-geofencing');

// UangJalan receipt routes
Route::get('/uang-jalan/{uangJalan}/print-receipt', [App\Http\Controllers\UangJalanReceiptController::class, 'printReceipt'])
    ->middleware(['auth'])
    ->name('uang-jalan.print-receipt');

Route::get('/uang-jalan/{uangJalan}/preview-receipt', [App\Http\Controllers\UangJalanReceiptController::class, 'previewReceipt'])
    ->middleware(['auth'])
    ->name('uang-jalan.preview-receipt');
