<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NeracaMappingResource\Pages;
use App\Filament\Resources\NeracaMappingResource\RelationManagers;
use App\Models\NeracaMapping;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;

class NeracaMappingResource extends Resource
{
    protected static ?string $model = NeracaMapping::class;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationLabel = 'Mapping Neraca';

    protected static ?string $modelLabel = 'Mapping Neraca';

    protected static ?string $pluralModelLabel = 'Mapping Neraca';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Akun')
                    ->schema([
                        Forms\Components\Select::make('kode_akun')
                            ->label('Kode Akun')
                            ->options(function ($record) {
                                // Get already mapped account codes, excluding current record if editing
                                $mappedCodes = NeracaMapping::when($record, function ($query) use ($record) {
                                    return $query->where('id', '!=', $record->id);
                                })->pluck('kode_akun')->toArray();

                                // Get available accounts (not yet mapped)
                                return Akun::whereNotIn('kode_akun', $mappedCodes)
                                    ->get()
                                    ->pluck('nama_akun', 'kode_akun')
                                    ->map(fn($nama, $kode) => "$kode - $nama");
                            })
                            ->searchable()
                            ->required()
                            ->live()
                            ->helperText('Hanya menampilkan akun yang belum di-mapping')
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $akun = Akun::where('kode_akun', $state)->first();
                                    if ($akun) {
                                        $set('nama_akun', $akun->nama_akun);
                                    }
                                }
                            }),

                        Forms\Components\TextInput::make('nama_akun')
                            ->label('Nama Akun')
                            ->required()
                            ->disabled()
                            ->dehydrated(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Kategori Neraca')
                    ->schema([
                        Forms\Components\Select::make('kategori_neraca')
                            ->label('Kategori Neraca')
                            ->options([
                                // AKTIVA LANCAR
                                'Kas dan Bank' => 'Kas dan Bank',
                                'Piutang Usaha' => 'Piutang Usaha',
                                'Piutang Lain-lain' => 'Piutang Lain-lain',
                                'Persediaan' => 'Persediaan',
                                'Biaya Dibayar Dimuka' => 'Biaya Dibayar Dimuka',
                                'Aktiva Lancar Lainnya' => 'Aktiva Lancar Lainnya',

                                // AKTIVA TETAP
                                'Tanah' => 'Tanah',
                                'Bangunan' => 'Bangunan',
                                'Kendaraan' => 'Kendaraan',
                                'Peralatan' => 'Peralatan',
                                'Inventaris' => 'Inventaris',
                                'Akumulasi Penyusutan' => 'Akumulasi Penyusutan',
                                'Aktiva Tetap Lainnya' => 'Aktiva Tetap Lainnya',

                                // HUTANG LANCAR
                                'Hutang Usaha' => 'Hutang Usaha',
                                'Hutang Pajak' => 'Hutang Pajak',
                                'Hutang Bank Jangka Pendek' => 'Hutang Bank Jangka Pendek',
                                'Hutang Lain-lain' => 'Hutang Lain-lain',
                                'Biaya Yang Masih Harus Dibayar' => 'Biaya Yang Masih Harus Dibayar',

                                // HUTANG JANGKA PANJANG
                                'Hutang Bank Jangka Panjang' => 'Hutang Bank Jangka Panjang',
                                'Hutang Jangka Panjang Lainnya' => 'Hutang Jangka Panjang Lainnya',

                                // MODAL
                                'Modal Disetor' => 'Modal Disetor',
                                'Laba Ditahan' => 'Laba Ditahan',
                                'Laba Tahun Berjalan' => 'Laba Tahun Berjalan',
                            ])
                            ->searchable()
                            ->required()
                            ->helperText('Pilih kategori untuk pengelompokan dalam neraca'),

                        Forms\Components\TextInput::make('urutan')
                            ->label('Urutan')
                            ->numeric()
                            ->default(0)
                            ->required()
                            ->helperText('Urutan tampil dalam neraca (angka kecil tampil lebih dulu)'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_akun')
                    ->label('Kode Akun')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('nama_akun')
                    ->label('Nama Akun')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('kategori_neraca')
                    ->label('Kategori Neraca')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        str_contains($state, 'Kas') || str_contains($state, 'Piutang') || str_contains($state, 'Persediaan') => 'success',
                        str_contains($state, 'Tanah') || str_contains($state, 'Bangunan') || str_contains($state, 'Kendaraan') => 'info',
                        str_contains($state, 'Hutang') => 'warning',
                        str_contains($state, 'Modal') || str_contains($state, 'Laba') => 'primary',
                        default => 'gray',
                    })
                    ->wrap(),

                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('kategori_neraca')
                    ->label('Kategori')
                    ->options([
                        'Kas dan Bank' => 'Kas dan Bank',
                        'Piutang Usaha' => 'Piutang Usaha',
                        'Persediaan' => 'Persediaan',
                        'Tanah' => 'Tanah',
                        'Bangunan' => 'Bangunan',
                        'Kendaraan' => 'Kendaraan',
                        'Hutang Usaha' => 'Hutang Usaha',
                        'Hutang Pajak' => 'Hutang Pajak',
                        'Modal Disetor' => 'Modal Disetor',
                        'Laba Ditahan' => 'Laba Ditahan',
                    ])
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->striped();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNeracaMappings::route('/'),
            'create' => Pages\CreateNeracaMapping::route('/create'),
            'edit' => Pages\EditNeracaMapping::route('/{record}/edit'),
        ];
    }
}
