<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\InvoiceDeduction;
use Carbon\Carbon;

class InvoiceDeductionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing invoices
        $invoices = Invoice::limit(5)->get();

        if ($invoices->isEmpty()) {
            $this->command->info('No invoices found. Please run InvoiceSeeder first.');
            return;
        }

        foreach ($invoices as $invoice) {
            // Add PPN deduction (11%)
            InvoiceDeduction::create([
                'invoice_id' => $invoice->id,
                'deduction_type' => InvoiceDeduction::TYPE_PPN,
                'amount' => $invoice->total_invoice * 0.11,
                'percentage' => 11.00,
                'description' => 'PPN 11% sesuai regulasi',
                'deduction_date' => Carbon::now()->subDays(rand(1, 30)),
                'reference_number' => 'PPN-' . $invoice->nomor_invoice,
                'notes' => 'Potongan PPN sesuai dengan ketentuan perpajakan',
                'created_by' => 1, // Admin user
            ]);

            // Add PPh deduction (2%) for some invoices
            if (rand(1, 2) === 1) {
                InvoiceDeduction::create([
                    'invoice_id' => $invoice->id,
                    'deduction_type' => InvoiceDeduction::TYPE_PPH,
                    'amount' => $invoice->total_invoice * 0.02,
                    'percentage' => 2.00,
                    'description' => 'PPh Pasal 23 sebesar 2%',
                    'deduction_date' => Carbon::now()->subDays(rand(1, 25)),
                    'reference_number' => 'PPH-' . $invoice->nomor_invoice,
                    'notes' => 'Potongan PPh Pasal 23 sesuai ketentuan',
                    'created_by' => 1,
                ]);
            }

            // Add admin bank deduction for some invoices
            if (rand(1, 3) === 1) {
                InvoiceDeduction::create([
                    'invoice_id' => $invoice->id,
                    'deduction_type' => InvoiceDeduction::TYPE_ADMIN_BANK,
                    'amount' => rand(5000, 25000),
                    'percentage' => null,
                    'description' => 'Biaya administrasi bank',
                    'deduction_date' => Carbon::now()->subDays(rand(1, 20)),
                    'reference_number' => 'ADM-' . $invoice->nomor_invoice,
                    'notes' => 'Biaya administrasi transfer bank',
                    'created_by' => 1,
                ]);
            }

            // Add losis deduction for some invoices
            if (rand(1, 4) === 1) {
                InvoiceDeduction::create([
                    'invoice_id' => $invoice->id,
                    'deduction_type' => InvoiceDeduction::TYPE_LOSIS,
                    'amount' => rand(50000, 200000),
                    'percentage' => null,
                    'description' => 'Loss in Storage - kehilangan selama penyimpanan',
                    'deduction_date' => Carbon::now()->subDays(rand(1, 15)),
                    'reference_number' => 'LOSIS-' . $invoice->nomor_invoice,
                    'notes' => 'Potongan karena kehilangan volume selama penyimpanan',
                    'created_by' => 1,
                ]);
            }
        }

        $this->command->info('Invoice deductions seeded successfully!');
    }
}
