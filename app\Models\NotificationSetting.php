<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_name',
        'user_id',
        'channel',
        'is_active',
        'message_template',
        'template_variables',
        'message_preview',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'template_variables' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * <PERSON><PERSON>i dan mengembalikan semua pengaturan notifikasi yang aktif untuk sebuah event.
     *
     * @param string $eventName Nama event yang akan dicari.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findActiveRecipientsForEvent(string $eventName)
    {
        return static::with('user')
            ->where('event_name', $eventName)
            ->where('is_active', true)
            ->get();
    }
}
