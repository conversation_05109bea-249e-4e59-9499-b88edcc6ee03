<?php

namespace App\Filament\Resources\JournalResource\Pages;

use App\Filament\Resources\JournalResource;
use App\Models\Journal;
use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

// Infolist Components
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;

class ViewJournal extends ViewRecord
{
    protected static string $resource = JournalResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Journal')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('journal_number')
                                    ->label('Nomor Journal')
                                    ->icon('heroicon-o-document-text')
                                    ->badge()
                                    ->color('primary')
                                    ->copyable()
                                    ->copyMessage('Nomor journal disalin!'),

                                TextEntry::make('transaction_date')
                                    ->label('Tanggal Transaksi')
                                    ->icon('heroicon-o-calendar-days')
                                    ->date('d M Y'),

                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        'Draft' => 'warning',
                                        'Posted' => 'success',
                                        'Cancelled' => 'danger',
                                        'Error' => 'danger',
                                        default => 'gray'
                                    }),
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextEntry::make('source_type')
                                    ->label('Tipe Sumber')
                                    ->icon('heroicon-o-tag')
                                    ->badge()
                                    ->color('info')
                                    ->formatStateUsing(fn($state) => match ($state) {
                                        'Sale' => 'Penjualan',
                                        'Purchase' => 'Pembelian',
                                        'Payment' => 'Pembayaran',
                                        'Receipt' => 'Penerimaan',
                                        'ManualAdjust' => 'Penyesuaian Manual',
                                        'ExpenseRequest' => 'Expense Request',
                                        'Invoice' => 'Invoice',
                                        default => $state
                                    }),

                                TextEntry::make('reference_number')
                                    ->label('Nomor Referensi')
                                    ->icon('heroicon-o-hashtag')
                                    ->placeholder('Tidak ada referensi'),
                            ]),

                        TextEntry::make('description')
                            ->label('Deskripsi')
                            ->icon('heroicon-o-document-text')
                            ->placeholder('Tidak ada deskripsi')
                            ->columnSpanFull(),
                    ])
                    ->columns(1),

                Section::make('Journal Entries')
                    ->description('Detail entri debit dan kredit untuk journal ini')
                    ->schema([
                        RepeatableEntry::make('journalEntries')
                            ->label('')
                            ->schema([
                                Grid::make(4)
                                    ->schema([
                                        TextEntry::make('account.kode_akun')
                                            ->label('Kode Akun')
                                            ->icon('heroicon-o-hashtag')
                                            ->badge()
                                            ->color('gray'),

                                        TextEntry::make('account.nama_akun')
                                            ->label('Nama Akun')
                                            ->icon('heroicon-o-building-office')
                                            ->weight('medium'),

                                        TextEntry::make('debit')
                                            ->label('Debit')
                                            ->icon('heroicon-o-arrow-up-circle')
                                            ->money('IDR')
                                            ->color('success')
                                            ->weight('bold')
                                            ->placeholder('-')
                                            ->formatStateUsing(fn($state) => $state > 0 ? 'Rp ' . number_format($state, 0, ',', '.') : '-'),

                                        TextEntry::make('credit')
                                            ->label('Kredit')
                                            ->icon('heroicon-o-arrow-down-circle')
                                            ->money('IDR')
                                            ->color('danger')
                                            ->weight('bold')
                                            ->placeholder('-')
                                            ->formatStateUsing(fn($state) => $state > 0 ? 'Rp ' . number_format($state, 0, ',', '.') : '-'),
                                    ]),

                                TextEntry::make('description')
                                    ->label('Deskripsi')
                                    ->icon('heroicon-o-chat-bubble-left-ellipsis')
                                    ->placeholder('Tidak ada deskripsi')
                                    ->columnSpanFull(),
                            ])
                            ->columns(1)
                            ->contained(false),
                    ])
                    ->collapsible(),

                Section::make('Informasi Tambahan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Dibuat Pada')
                                    ->icon('heroicon-o-clock')
                                    ->dateTime('d M Y, H:i'),

                                TextEntry::make('updated_at')
                                    ->label('Diperbarui Pada')
                                    ->icon('heroicon-o-clock')
                                    ->dateTime('d M Y, H:i'),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
