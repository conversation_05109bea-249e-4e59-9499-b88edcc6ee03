# AKUNTANSI MODULE MIGRATION - PART 1: MODELS AND MIGRATIONS
# This file contains all Models and Database Migrations for the Accounting Module

# ============================================================================
# FILE: app/Models/Akun.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Akun extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'akun';

    protected $fillable = [
        'kode_akun',
        'nama_akun',
        'kategori_akun',
        'tipe_akun',
        'saldo_awal',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'saldo_awal' => 'decimal:2',
    ];

    // Relationships
    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByCategory($query, $category)
    {
        return $query->where('kategori_akun', $category);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('tipe_akun', $type);
    }

    // Helper methods
    public function getCurrentBalance($endDate = null)
    {
        $query = $this->journalEntries()
            ->whereHas('journal', function ($q) use ($endDate) {
                $q->where('status', 'Posted');
                if ($endDate) {
                    $q->where('transaction_date', '<=', $endDate);
                }
            });

        $totalDebit = $query->sum('debit');
        $totalCredit = $query->sum('credit');

        $balance = $this->saldo_awal ?? 0;

        if ($this->tipe_akun === 'Debit') {
            $balance += $totalDebit - $totalCredit;
        } else {
            $balance += $totalCredit - $totalDebit;
        }

        return $balance;
    }
}

# ============================================================================
# FILE: app/Models/Journal.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Journal extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'journals';

    protected $fillable = [
        'journal_number',
        'transaction_date',
        'reference_number',
        'source_type',
        'source_id',
        'description',
        'status',
        'posting_rule_id',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'transaction_date'];

    protected $casts = [
        'transaction_date' => 'date',
    ];

    // Relationships
    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    public function source()
    {
        return $this->morphTo();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class, 'posting_rule_id');
    }

    // Scopes
    public function scopePosted($query)
    {
        return $query->where('status', 'Posted');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'Draft');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function getTotalDebitAttribute()
    {
        return $this->journalEntries->sum('debit');
    }

    public function getTotalCreditAttribute()
    {
        return $this->journalEntries->sum('credit');
    }

    public function isBalanced()
    {
        return $this->getTotalDebitAttribute() == $this->getTotalCreditAttribute();
    }

    // Auto-generate journal number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($journal) {
            if (empty($journal->journal_number)) {
                $journal->journal_number = static::generateJournalNumber();
            }
        });
    }

    public static function generateJournalNumber()
    {
        $year = date('Y');
        $month = date('m');
        $lastJournal = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastJournal ? (int)substr($lastJournal->journal_number, -4) + 1 : 1;

        return sprintf('JRN-%s%s-%04d', $year, $month, $sequence);
    }
}

# ============================================================================
# FILE: app/Models/JournalEntry.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    use HasFactory;

    protected $table = 'journal_entries';

    protected $fillable = [
        'journal_id',
        'account_id',
        'description',
        'debit',
        'credit',
        'sort_order',
    ];

    protected $casts = [
        'debit' => 'decimal:2',
        'credit' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Scopes
    public function scopeDebit($query)
    {
        return $query->where('debit', '>', 0);
    }

    public function scopeCredit($query)
    {
        return $query->where('credit', '>', 0);
    }

    // Helper methods
    public function getAmountAttribute()
    {
        return $this->debit > 0 ? $this->debit : $this->credit;
    }

    public function getTypeAttribute()
    {
        return $this->debit > 0 ? 'Debit' : 'Credit';
    }
}

# ============================================================================
# FILE: app/Models/PostingRule.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PostingRule extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'posting_rules';

    protected $fillable = [
        'rule_name',
        'source_type',
        'trigger_condition',
        'description',
        'is_active',
        'priority',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'trigger_condition' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
    ];

    // Relationships
    public function postingRuleEntries()
    {
        return $this->hasMany(PostingRuleEntry::class)->orderBy('sort_order');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function journals()
    {
        return $this->hasMany(Journal::class, 'posting_rule_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBySourceType($query, $sourceType)
    {
        return $query->where('source_type', $sourceType);
    }

    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }

    // Helper methods
    public function evaluateCondition($sourceModel)
    {
        if (empty($this->trigger_condition)) {
            return true; // No condition means always apply
        }

        foreach ($this->trigger_condition as $field => $expectedValue) {
            $actualValue = data_get($sourceModel, $field);
            if ($actualValue != $expectedValue) {
                return false;
            }
        }

        return true;
    }
}

# ============================================================================
# FILE: app/Models/PostingRuleEntry.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Exception;

class PostingRuleEntry extends Model
{
    use HasFactory;

    protected $table = 'posting_rule_entries';

    protected $fillable = [
        'posting_rule_id',
        'account_id',
        'dc_type',
        'amount_type',
        'fixed_amount',
        'source_property',
        'calculation_expression',
        'description_template',
        'sort_order',
    ];

    protected $casts = [
        'fixed_amount' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function postingRule()
    {
        return $this->belongsTo(PostingRule::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function calculateAmount($sourceModel)
    {
        switch ($this->amount_type) {
            case 'Fixed':
                return $this->fixed_amount;

            case 'SourceValue':
                return data_get($sourceModel, $this->source_property, 0);

            case 'Calculated':
                return $this->evaluateExpression($sourceModel);

            default:
                return 0;
        }
    }

    public function generateDescription($sourceModel)
    {
        $template = $this->description_template ?: 'Transaction - {source.id}';

        // Simple placeholder replacement
        $description = preg_replace_callback('/\{([^}]+)\}/', function ($matches) use ($sourceModel) {
            $path = $matches[1];
            return data_get($sourceModel, $path, $matches[0]);
        }, $template);

        return $description;
    }

    private function evaluateExpression($sourceModel)
    {
        try {
            $expression = $this->calculation_expression;

            // Simple expression evaluation for common patterns
            if (preg_match('/^(\w+)\.sum\((\w+)\s*\*\s*(\w+)\)$/', $expression, $matches)) {
                $relation = $matches[1];
                $field1 = $matches[2];
                $field2 = $matches[3];

                $items = data_get($sourceModel, $relation, collect());
                return $items->sum(function ($item) use ($field1, $field2) {
                    return data_get($item, $field1, 0) * data_get($item, $field2, 0);
                });
            }

            // Add more expression patterns as needed
            return 0;

        } catch (Exception $e) {
            Log::error('Error evaluating posting rule expression', [
                'expression' => $this->calculation_expression,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}

# ============================================================================
# FILE: app/Models/Inventory.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inventory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'inventories';

    protected $fillable = [
        'product_id',
        'quantity',
        'unit_cost',
        'total_value',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_value' => 'decimal:2',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Produk::class, 'product_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper method untuk menghitung total value
    public function calculateTotalValue()
    {
        $this->total_value = $this->quantity * $this->unit_cost;
        return $this->total_value;
    }

    // Boot method untuk auto-calculate total_value
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($inventory) {
            $inventory->total_value = $inventory->quantity * ($inventory->unit_cost ?? 0);
        });
    }
}

# ============================================================================
# FILE: app/Models/SalesTransaction.php
# ============================================================================
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SalesTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'sales_transactions';

    protected $fillable = [
        'transaction_code',
        'transaction_date',
        'customer_name',
        'payment_method',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'transaction_date'];

    protected $casts = [
        'transaction_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function journals()
    {
        return $this->morphMany(Journal::class, 'source');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Auto-generate transaction code
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_code)) {
                $transaction->transaction_code = static::generateTransactionCode();
            }
        });
    }

    public static function generateTransactionCode()
    {
        $year = date('Y');
        $month = date('m');
        $lastTransaction = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransaction ? (int)substr($lastTransaction->transaction_code, -4) + 1 : 1;

        return sprintf('TRX-%s%s-%04d', $year, $month, $sequence);
    }
}
