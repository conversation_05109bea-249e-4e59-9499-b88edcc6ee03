<?php

namespace App\Filament\Resources\LabaRugiMappingResource\Pages;

use App\Filament\Resources\LabaRugiMappingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLabaRugiMappings extends ListRecords
{
    protected static string $resource = LabaRugiMappingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
