<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('jadwal_masal', function (Blueprint $table) {
            $table->id();
            $table->string('nama_jadwal');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->unsignedBigInteger('entitas_id');
            $table->unsignedBigInteger('shift_id');
            $table->unsignedBigInteger('created_by');
            $table->text('keterangan')->nullable();
            $table->timestamp('generated_at')->nullable();

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('entitas_id')->references('id')->on('entitas')->onDelete('cascade');
            $table->foreign('shift_id')->references('id')->on('shift')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });

        // Create pivot table for many-to-many relationship between jadwal_masal and karyawan
        Schema::create('jadwal_masal_karyawan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('jadwal_masal_id');
            $table->unsignedBigInteger('karyawan_id');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('jadwal_masal_id')->references('id')->on('jadwal_masal')->onDelete('cascade');
            $table->foreign('karyawan_id')->references('id')->on('users')->onDelete('cascade');

            // Ensure each employee is only assigned once to a specific bulk schedule
            $table->unique(['jadwal_masal_id', 'karyawan_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('jadwal_masal_karyawan');
        Schema::dropIfExists('jadwal_masal');
    }
};
