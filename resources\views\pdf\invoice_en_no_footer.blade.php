{{-- Invoice EN template without footer for combined documents --}}
@php
    // Get logo as base64 if not provided
    if (!isset($logoBase64) || empty($logoBase64)) {
        $logoPath = public_path('images/lrp.png');
        $logoBase64 = '';
        if (File::exists($logoPath)) {
            $logoBase64 = base64_encode(File::get($logoPath));
        }
    }
@endphp

<style>
    body {
        font-family: Arial, sans-serif;
        font-size: 11px;
        line-height: 1.3;
        color: #000;
        margin: 0;
        padding: 0;
    }

    .header {
        text-align: center;
        margin-bottom: 12px;
        border-bottom: 2px solid #000;
        padding-bottom: 6px;
    }

    .logo {
        max-height: 45px;
        margin-bottom: 6px;
    }

    .company-info {
        font-size: 9px;
        line-height: 1.1;
    }

    .title {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin: 12px 0 8px 0;
        text-decoration: underline;
    }

    .invoice-details {
        margin-bottom: 12px;
    }

    .detail-table {
        width: 100%;
        margin-bottom: 15px;
        font-size: 11px;
    }

    .detail-table td {
        padding: 3px 5px;
        vertical-align: top;
    }

    .detail-table td:first-child {
        width: 40%;
        font-weight: bold;
    }

    .detail-table td:nth-child(2) {
        width: 5%;
        text-align: center;
    }

    .detail-table td:last-child {
        width: 55%;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
        font-size: 9px;
    }

    .items-table th,
    .items-table td {
        border: 1px solid #000;
        padding: 3px;
        vertical-align: top;
    }

    .items-table th {
        background-color: #f0f0f0;
        font-weight: bold;
        text-align: center;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    .text-left {
        text-align: left;
    }

    .total-section {
        margin-top: 15px;
        margin-bottom: 0px;
    }

    .total-table {
        width: 50%;
        margin-left: auto;
        border-collapse: collapse;
        font-size: 11px;
    }

    .total-table td {
        padding: 5px;
        border: 1px solid #000;
    }

    .total-table .label {
        font-weight: bold;
        background-color: #f0f0f0;
        width: 60%;
    }

    .total-table .value {
        text-align: right;
        width: 40%;
    }

    .signature-section {
        margin-top: 15px;
        text-align: right;
        font-size: 9px;
    }

    .signature-box {
        display: inline-block;
        text-align: center;
        width: 180px;
    }

    .signature-title {
        font-weight: bold;
        margin-bottom: 3px;
        text-transform: uppercase;
    }

    .signature-space {
        height: 40px;
        border-bottom: 1px solid #000;
        margin: 8px 0;
    }

    .signature-name {
        margin-top: 3px;
        font-weight: bold;
    }

    .signature-location {
        margin-bottom: 5px;
    }
</style>

<div class="invoice-content">
    <!-- Header -->
    <div class="header">
        @if ($logoBase64)
            <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo" class="logo">
        @endif
    </div>

    <!-- Title -->
    <div class="title">
        SALES INVOICE
    </div>

    <!-- Invoice Number Display -->
    <div style="text-align: center; font-size: 12px; font-weight: bold; margin-bottom: 15px;">
        Invoice No: {{ $record->nomor_invoice ?? 'N/A' }}
    </div>

    <!-- Invoice Details - 2 Columns -->
    <div class="invoice-details">
        <div style="display: table; width: 100%; margin-bottom: 12px;">
            <!-- Left Column -->
            <div style="display: table-cell; width: 50%; vertical-align: top; padding-right: 10px;">
                <table class="detail-table">
                    <tr>
                        <td>Customer</td>
                        <td>:</td>
                        <td>{{ $record->getCustomerName() }}</td>
                    </tr>
                    <tr>
                        <td>Address</td>
                        <td>:</td>
                        <td>{{ $record->getCustomerAddress() ?: 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td>Tax ID</td>
                        <td>:</td>
                        <td>{{ $record->npwp_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->npwp ?? 'N/A') }}
                        </td>
                    </tr>
                    <tr>
                        <td>Pertamina Letter No</td>
                        <td>:</td>
                        <td>{{ $record->transaksiPenjualan?->kode ?? 'SO Number' }}</td>
                    </tr>
                </table>
            </div>

            <!-- Right Column -->
            <div style="display: table-cell; width: 50%; vertical-align: top; padding-left: 10px;">
                @php
                    $deliveryOrders = $record->transaksiPenjualan?->deliveryOrders ?? collect();
                    $doNumbers = $deliveryOrders->pluck('kode')->filter()->implode(', ');
                @endphp
                <table class="detail-table">
                    <tr>
                        <td>Delivery Order No</td>
                        <td>:</td>
                        <td>{{ $doNumbers ?: 'No DO yet' }}</td>
                    </tr>
                    <tr>
                        <td>Invoice Date</td>
                        <td>:</td>
                        <td>{{ $record->tanggal_invoice?->format('d/m/Y') ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td>Due Date</td>
                        <td>:</td>
                        <td>{{ $record->tanggal_jatuh_tempo?->format('d/m/Y') ?? 'N/A' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%; text-align: center;">No</th>
                <th style="width: 30%; text-align: left;">Description</th>
                <th style="width: 12%; text-align: right;">Unit Price</th>
                <th style="width: 12%; text-align: right;">Volume</th>
                <th style="width: 12%; text-align: right;">VAT</th>
                <th style="width: 12%; text-align: right;">PBBKB</th>
                <th style="width: 17%; text-align: right;">Total</th>
            </tr>
        </thead>
        <tbody>
            @php
                $itemNumber = 1;
                $invoiceItems = $record->invoiceItems ?? collect();

                if ($invoiceItems->isEmpty() && $record->transaksiPenjualan) {
                    $invoiceItems = $record->transaksiPenjualan->penjualanDetails ?? collect();
                }
            @endphp

            @forelse($invoiceItems as $item)
                @if ($item)
                    {{-- Main item row --}}
                    <tr>
                        <td style="text-align: center;">{{ $itemNumber++ }}</td>
                        <td style="text-align: left; padding-left: 8px;">
                            {{ $item->item_name ?? ($item->item?->name ?? 'Item not found') }}<br>
                            <small
                                style="color: #6b7280;">{{ $item->item_description ?? ($item->item?->description ?? '') }}</small>
                        </td>
                        <td style="text-align: right; padding-right: 8px;">
                            ${{ number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                        <td style="text-align: right; padding-right: 8px;">
                            {{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                            {{ $item->unit ?? 'Liter' }}</td>
                        <td style="text-align: right; padding-right: 8px;">
                            ${{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                        <td style="text-align: right; padding-right: 8px;">
                            ${{ number_format($item->pbbkb_amount ?? 0, 0, ',', '.') }}</td>
                        <td style="text-align: right; padding-right: 8px;">
                            ${{ number_format(($item->unit_price ?? 0) * ($item->quantity ?? 0) + ($item->ppn_amount ?? 0) + ($item->pbbkb_amount ?? 0), 0, ',', '.') }}
                        </td>
                    </tr>

                    {{-- Operational cost row (if included) --}}
                    @if (($item->operasional_amount ?? 0) > 0)
                        <tr>
                            <td style="text-align: center;">{{ $itemNumber++ }}</td>
                            <td style="text-align: left; padding-left: 8px;">Operational Cost multiplied by
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }} liters</td>
                            <td style="text-align: right; padding-right: 8px;">
                                ${{ number_format(($item->operasional_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td style="text-align: right; padding-right: 8px;">
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td style="text-align: right; padding-right: 8px;"></td>
                            <td style="text-align: right; padding-right: 8px;"></td>
                            <td style="text-align: right; padding-right: 8px;">
                                ${{ number_format($item->operasional_amount ?? 0, 0, ',', '.') }}
                            </td>
                        </tr>
                    @endif

                    {{-- PBBKB cost row (if included) --}}
                    @if (($item->pbbkb_amount ?? 0) > 0)
                        <tr>
                            <td style="text-align: center;">{{ $itemNumber++ }}</td>
                            <td style="text-align: left; padding-left: 8px;">PBBKB multiplied by
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }} liters</td>
                            <td style="text-align: right; padding-right: 8px;">
                                ${{ number_format(($item->pbbkb_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td style="text-align: right; padding-right: 8px;">
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td style="text-align: right; padding-right: 8px;"></td>
                            <td style="text-align: right; padding-right: 8px;"></td>
                            <td style="text-align: right; padding-right: 8px;">
                                ${{ number_format($item->pbbkb_amount ?? 0, 0, ',', '.') }}
                            </td>
                        </tr>
                    @endif
                @endif
            @empty
                <tr>
                    <td colspan="7" class="text-center">No items</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Total Section -->
    <div class="total-section">
        <table class="total-table">
            <tr>
                <td class="label">Subtotal</td>
                <td class="value">${{ number_format($record->subtotal ?? 0, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td class="label">VAT (11%)</td>
                <td class="value">${{ number_format($record->total_pajak ?? 0, 0, ',', '.') }}</td>
            </tr>
            <tr style="font-weight: bold; background-color: #f0f0f0;">
                <td class="label">Total Invoice</td>
                <td class="value">${{ number_format($record->total_invoice ?? 0, 0, ',', '.') }}</td>
            </tr>
        </table>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-location">Pekanbaru,
                {{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d F Y') : now()->format('d F Y') }}
            </div>
            <div class="signature-space"></div>
            <div class="signature-name">Agustiawan Syahputra</div>
            <div class="signature-title">Chief Executive Officer</div>
        </div>
    </div>
</div>
