<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TransaksiPenjualan;
use App\Models\PenjualanDetail;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Pelanggan;
use App\Models\Item;
use App\Models\User;

class ServiceTypeInvoiceTestSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Creating test data for service type invoice...');

        // Get or create test customer
        $pelanggan = Pelanggan::firstOrCreate(
            ['kode' => 'CUST-SERVICE-001'],
            [
                'nama' => 'PT Jasa Transport Test',
                'type' => 'Swasta',
                'alamat' => 'Jl. Test Service No. 123',
                'pic_nama' => 'John <PERSON>',
                'pic_phone' => '021-12345678',
                'created_by' => 1,
            ]
        );

        // Create alamat pelanggan
        $alamatPelanggan = \App\Models\AlamatPelanggan::firstOrCreate(
            ['id_pelanggan' => $pelanggan->id, 'is_primary' => true],
            [
                'alamat' => 'Jl. Test Service No. 123, Jakarta',
            ]
        );

        // Get or create test user
        $user = User::first() ?? User::factory()->create();

        // Get or create test item for service
        $item = Item::firstOrCreate(
            ['name' => 'Jasa Transportasi BBM'],
            [
                'kode' => 'JASA-001',
                'description' => 'Jasa transportasi bahan bakar minyak',
                'created_by' => 1,
            ]
        );

        // Create service type transaction
        $transaksi = TransaksiPenjualan::create([
            'kode' => 'SO-SERVICE-' . time(),
            'tipe' => 'jasa', // Service type
            'tanggal' => now(),
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => $alamatPelanggan->id,
            'nomor_po' => 'PO-SERVICE-001',
            'top_pembayaran' => 30,
            'created_by' => $user->id,
        ]);

        // Create transaction details
        PenjualanDetail::create([
            'id_transaksi_penjualan' => $transaksi->id,
            'id_item' => $item->id,
            'volume_item' => 5, // 5 trips
            'volume_do' => 5,
            'harga_jual' => 0, // No unit price for service type
        ]);

        // Use existing DO or create minimal one
        $existingDO = \App\Models\DeliveryOrder::first();
        if (!$existingDO) {
            $this->command->error('No existing Delivery Order found. Please create one first or run the main seeder.');
            return;
        }

        // Calculate totals
        $subtotal = 5 * 2000000; // 10,000,000
        $ppn = $subtotal * 0.11; // 1,100,000
        $total = $subtotal + $ppn; // 11,100,000

        // Create invoice for service type
        $invoice = Invoice::create([
            'nomor_invoice' => 'INV-SERVICE-' . time(),
            'id_do' => $existingDO->id, // Use existing DO
            'id_transaksi' => $transaksi->id,
            'tanggal_invoice' => now(),
            'tanggal_jatuh_tempo' => now()->addDays(30),
            'nama_pelanggan' => $pelanggan->nama,
            'alamat_pelanggan' => $pelanggan->alamat,
            'npwp_pelanggan' => $pelanggan->npwp,
            'subtotal' => $subtotal,
            'total_pajak' => $ppn,
            'total_invoice' => $total,
            'sisa_tagihan' => $total,
            'biaya_ongkos_angkut' => 2000000, // 2 million per trip
            'include_ppn' => true,
            'include_operasional_kerja' => false, // Not applicable for service
            'include_pbbkb' => false, // Not applicable for service
            'status' => 'draft',
            'created_by' => $user->id,
        ]);

        // Create invoice item for service
        InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'item_id' => $item->id,
            'item_name' => $item->name,
            'item_description' => $item->description,
            'quantity' => 5, // 5 trips
            'unit' => 'Trip',
            'unit_price' => 2000000, // Transport cost per trip
            'include_ppn' => true,
            'ppn_rate' => 11,
            'include_operasional' => false,
            'include_pbbkb' => false,
            'notes' => 'Jasa transportasi BBM - 5 trip',
        ]);

        // Invoice totals already set above

        $this->command->info("Created service type invoice: {$invoice->nomor_invoice}");
        $this->command->info("- Transaction: {$transaksi->kode} (Type: {$transaksi->tipe})");
        $this->command->info("- Transport cost per trip: Rp 2,000,000");
        $this->command->info("- Quantity: 5 trips");
        $this->command->info("- Subtotal: Rp " . number_format($subtotal, 0, ',', '.'));
        $this->command->info("- PPN 11%: Rp " . number_format($ppn, 0, ',', '.'));
        $this->command->info("- Total: Rp " . number_format($total, 0, ',', '.'));
    }
}
