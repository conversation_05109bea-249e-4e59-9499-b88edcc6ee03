<?php

namespace App\Filament\Resources\LaporanHarianResource\Widgets;

use App\Models\LaporanHarian;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class LaporanHarianStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Get date ranges
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        // Today's stats
        $todayCount = LaporanHarian::whereDate('tanggal_laporan', $today)->count();

        // This week's stats
        $weekCount = LaporanHarian::where('tanggal_laporan', '>=', $thisWeek)->count();

        // This month's stats
        $monthCount = LaporanHarian::where('tanggal_laporan', '>=', $thisMonth)->count();
        $lastMonthCount = LaporanHarian::whereBetween('tanggal_laporan', [$lastMonth, $thisMonth])->count();

        // Calculate growth
        $growth = $lastMonthCount > 0 ? round((($monthCount - $lastMonthCount) / $lastMonthCount) * 100, 1) : 0;

        // Photo statistics
        $reportsWithPhotos = LaporanHarian::where('tanggal_laporan', '>=', $thisMonth)
            ->get()
            ->filter(function ($laporan) {
                return $laporan->getMedia('foto_mobil_tangki')->count() >= 4;
            })
            ->count();

        $photoComplianceRate = $monthCount > 0 ? round(($reportsWithPhotos / $monthCount) * 100, 1) : 0;

        // Reports with notes (for future use)
        // $reportsWithNotes = LaporanHarian::where('tanggal_laporan', '>=', $thisMonth)
        //     ->whereNotNull('catatan')
        //     ->where('catatan', '!=', '')
        //     ->count();

        return [
            Stat::make('Laporan Hari Ini', $todayCount)
                ->description($todayCount > 0 ? 'Laporan sudah dibuat' : 'Belum ada laporan')
                ->descriptionIcon('heroicon-m-document-text')
                ->color($todayCount > 0 ? 'success' : 'warning')
                ->chart([1, 2, 1, 3, 2, 4, $todayCount]),

            Stat::make('Laporan Minggu Ini', $weekCount)
                ->description('Total laporan 7 hari terakhir')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info')
                ->chart([2, 3, 4, 5, 6, 7, $weekCount]),

            Stat::make('Laporan Bulan Ini', $monthCount)
                ->description(($growth >= 0 ? '+' : '') . $growth . '% dari bulan lalu')
                ->descriptionIcon($growth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($growth >= 0 ? 'success' : 'danger')
                ->chart([15, 18, 22, 25, 28, 30, $monthCount]),

            Stat::make('Kepatuhan Foto', $photoComplianceRate . '%')
                ->description($reportsWithPhotos . ' dari ' . $monthCount . ' laporan lengkap')
                ->descriptionIcon('heroicon-m-camera')
                ->color($photoComplianceRate >= 90 ? 'success' : ($photoComplianceRate >= 70 ? 'warning' : 'danger'))
                ->chart([70, 75, 80, 85, 88, 90, $photoComplianceRate]),
        ];
    }
}
