@php
    $attributes = $record->attributes ?? [];
    $category = $record->category;
@endphp

<div class="space-y-6">
    @if(!empty($attributes))
        @switch($category)
            @case('ganti_oli')
                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-3">
                        🛢️ Detail Ganti Oli Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'jenis_oli' => 'Jenis Oli',
                            'merk_oli' => 'Merk Oli',
                            'volume_oli' => 'Volume Oli (Liter)',
                            'kilometer' => 'Kilometer Saat Ganti',
                            'kilometer_berikutnya' => 'Kilometer Ganti Berikutnya',
                            'bengkel' => 'Nama Bengkel',
                            'alamat_bengkel' => '<PERSON><PERSON><PERSON>',
                            'teknisi' => 'Nama Teknisi',
                            'filter_oli' => 'Ganti Filter Oli',
                            'filter_udara' => 'Ganti Filter Udara',
                            'catatan' => 'Catatan Tambahan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if($key === 'filter_oli' || $key === 'filter_udara')
                                            {{ $attributes[$key] ? 'Ya' : 'Tidak' }}
                                        @elseif($key === 'kilometer' || $key === 'kilometer_berikutnya')
                                            {{ number_format($attributes[$key]) }} km
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('vehicle_maintenance')
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
                        🔧 Detail Perawatan Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'jenis_perawatan' => 'Jenis Perawatan',
                            'suku_cadang' => 'Suku Cadang',
                            'merk_suku_cadang' => 'Merk Suku Cadang',
                            'jumlah_suku_cadang' => 'Jumlah',
                            'bengkel' => 'Nama Bengkel',
                            'alamat_bengkel' => 'Alamat Bengkel',
                            'teknisi' => 'Nama Teknisi',
                            'kilometer' => 'Kilometer Saat Service',
                            'estimasi_selesai' => 'Estimasi Selesai',
                            'garansi' => 'Garansi',
                            'catatan' => 'Catatan Tambahan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if($key === 'kilometer')
                                            {{ number_format($attributes[$key]) }} km
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('vehicle_fuel')
                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
                        ⛽ Detail Bahan Bakar Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'jenis_bbm' => 'Jenis BBM',
                            'liter' => 'Jumlah Liter',
                            'harga_per_liter' => 'Harga per Liter',
                            'spbu' => 'Nama SPBU',
                            'alamat_spbu' => 'Alamat SPBU',
                            'kilometer' => 'Kilometer Saat Isi',
                            'tangki_penuh' => 'Tangki Penuh',
                            'operator' => 'Operator SPBU',
                            'no_struk' => 'Nomor Struk',
                            'catatan' => 'Catatan Tambahan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if($key === 'liter')
                                            {{ number_format($attributes[$key]) }} L
                                        @elseif($key === 'harga_per_liter')
                                            Rp {{ number_format($attributes[$key]) }}
                                        @elseif($key === 'kilometer')
                                            {{ number_format($attributes[$key]) }} km
                                        @elseif($key === 'tangki_penuh')
                                            {{ $attributes[$key] ? 'Ya' : 'Tidak' }}
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('vehicle_tax')
                <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">
                        📋 Detail Pajak dan STNK Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'jenis_pajak' => 'Jenis Pajak',
                            'masa_berlaku' => 'Masa Berlaku',
                            'samsat' => 'Kantor Samsat',
                            'alamat_samsat' => 'Alamat Samsat',
                            'petugas' => 'Nama Petugas',
                            'no_kuitansi' => 'Nomor Kuitansi',
                            'denda' => 'Denda (jika ada)',
                            'catatan' => 'Catatan Tambahan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if($key === 'denda')
                                            Rp {{ number_format($attributes[$key]) }}
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('delivery_reimbursement')
                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">
                        🚚 Detail Reimburs Delivery Order
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'odometer_awal' => 'Odometer Awal',
                            'odometer_akhir' => 'Odometer Akhir',
                            'jarak_tempuh' => 'Jarak Tempuh (km)',
                            'tujuan_delivery' => 'Tujuan Delivery',
                            'no_delivery_order' => 'No. Delivery Order',
                            'nama_customer' => 'Nama Customer',
                            'jenis_produk' => 'Jenis Produk',
                            'biaya_tol' => 'Biaya Tol',
                            'biaya_parkir' => 'Biaya Parkir',
                            'catatan_perjalanan' => 'Catatan Perjalanan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if(in_array($key, ['odometer_awal', 'odometer_akhir']))
                                            {{ number_format($attributes[$key]) }} km
                                        @elseif($key === 'jarak_tempuh')
                                            {{ number_format($attributes[$key]) }} km
                                        @elseif(in_array($key, ['biaya_tol', 'biaya_parkir']))
                                            Rp {{ number_format($attributes[$key]) }}
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('uang_jalan_sc')
                <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-indigo-800 dark:text-indigo-200 mb-3">
                        💰 Detail Uang Jalan SC
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'nama_supir' => 'Nama Supir',
                            'nama_crew' => 'Nama Crew',
                            'no_kendaraan' => 'No. Kendaraan',
                            'rute_perjalanan' => 'Rute Perjalanan',
                            'tujuan' => 'Tujuan',
                            'tanggal_berangkat' => 'Tanggal Berangkat',
                            'tanggal_kembali' => 'Tanggal Kembali',
                            'jarak_tempuh' => 'Jarak Tempuh (km)',
                            'uang_makan' => 'Uang Makan',
                            'uang_inap' => 'Uang Inap',
                            'uang_bensin' => 'Uang Bensin',
                            'uang_tol' => 'Uang Tol',
                            'uang_parkir' => 'Uang Parkir',
                            'lain_lain' => 'Lain-lain',
                            'total_uang_jalan' => 'Total Uang Jalan',
                            'catatan' => 'Catatan'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if($key === 'jarak_tempuh')
                                            {{ number_format($attributes[$key]) }} km
                                        @elseif(in_array($key, ['uang_makan', 'uang_inap', 'uang_bensin', 'uang_tol', 'uang_parkir', 'lain_lain', 'total_uang_jalan']))
                                            Rp {{ number_format($attributes[$key]) }}
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @case('commission_fee')
                <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">
                        💼 Detail Biaya Komisi
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach([
                            'nama_penerima_komisi' => 'Nama Penerima',
                            'npwp_penerima' => 'NPWP Penerima',
                            'jenis_komisi' => 'Jenis Komisi',
                            'dasar_perhitungan' => 'Dasar Perhitungan',
                            'persentase_komisi' => 'Persentase Komisi (%)',
                            'nilai_transaksi' => 'Nilai Transaksi',
                            'komisi_bruto' => 'Komisi Bruto',
                            'pph_komisi' => 'PPh Komisi',
                            'komisi_netto' => 'Komisi Netto',
                            'periode_komisi' => 'Periode Komisi',
                            'bukti_transaksi' => 'Bukti Transaksi',
                            'catatan_pajak' => 'Catatan Pajak'
                        ] as $key => $label)
                            @if(isset($attributes[$key]) && !empty($attributes[$key]))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        @if(in_array($key, ['nilai_transaksi', 'komisi_bruto', 'pph_komisi', 'komisi_netto']))
                                            Rp {{ number_format($attributes[$key]) }}
                                        @elseif($key === 'persentase_komisi')
                                            {{ $attributes[$key] }}%
                                        @else
                                            {{ $attributes[$key] }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @break

            @default
                <div class="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                        📄 Data Tambahan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($attributes as $key => $value)
                            @if(!empty($value))
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ ucfirst(str_replace('_', ' ', $key)) }}</span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        {{ is_array($value) ? implode(', ', $value) : $value }}
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
        @endswitch
    @else
        <div class="text-center py-8">
            <div class="text-gray-400 dark:text-gray-500 text-lg">
                📄 Tidak ada data tambahan untuk expense request ini
            </div>
        </div>
    @endif
</div>
