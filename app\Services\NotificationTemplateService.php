<?php

namespace App\Services;

use App\Models\NotificationSetting;
use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use Illuminate\Support\Str;

class NotificationTemplateService
{
    /**
     * Default templates untuk setiap event
     */
    public static function getDefaultTemplates(): array
    {
        return [
            'penjualan_baru' => [
                'template' => "🔔 *Transaksi Penjualan Baru*\n\nHalo,\nAda transaksi penjualan baru yang membutuhkan persetujuan Anda.\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nSalesperson: *{salesperson_name}*\nPelanggan: *{nama_pelanggan}*\n\nMohon segera ditinjau dan diproses melalui link berikut:\n{view_url}\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'salesperson_name', 'nama_pelanggan', 'view_url'],
                'description' => 'Notifikasi untuk transaksi penjualan baru'
            ],
            'penjualan_rejected' => [
                'template' => "❌ *Transaksi Penjualan Ditolak*\n\nHalo Bpk/Ibu *{salesperson_name}*,\nTransaksi penjualan Anda dengan No. DO: *{kode_transaksi}* telah *ditolak*.\nKeputusan ini diberikan oleh Bpk/Ibu *{approver_name}*.\n\n{note}\n\nMohon segera perbaiki transaksi Anda dan ajukan kembali untuk persetujuan.",
                'variables' => ['salesperson_name', 'kode_transaksi', 'approver_name', 'note'],
                'description' => 'Notifikasi untuk transaksi penjualan yang ditolak'
            ],
            'penjualan_revision' => [
                'template' => "📝 *Penjualan Membutuhkan Revisi*\n\nHalo Bpk/Ibu *{salesperson_name}*,\nTransaksi penjualan Anda dengan No. DO: *{kode_transaksi}* membutuhkan *revisi*.\nKeputusan ini diberikan oleh Bpk/Ibu *{approver_name}*.\n\n{note}\n\nMohon segera perbaiki transaksi Anda sesuai catatan dan ajukan kembali untuk persetujuan.",
                'variables' => ['salesperson_name', 'kode_transaksi', 'approver_name', 'note'],
                'description' => 'Notifikasi untuk transaksi penjualan yang perlu revisi'
            ],
            'penjualan_baru_jasa' => [
                'template' => "🔧 *Transaksi Jasa Baru*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nTanggal: *{tanggal_transaksi}*\nPelanggan: *{nama_pelanggan}*\nJenis Jasa: *{jenis_jasa}*\nTotal: *{total_transaksi}*\nStatus: *{status_transaksi}*\n\nMohon segera ditindaklanjuti.\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'tanggal_transaksi', 'nama_pelanggan', 'jenis_jasa', 'total_transaksi', 'status_transaksi'],
                'description' => 'Notifikasi untuk transaksi jasa baru'
            ],
            'penjualan_approved' => [
                'template' => "✅ *Transaksi Penjualan Disetujui*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nStatus: *Disetujui*\nDisetujui oleh: *{approved_by}*\n\nMohon segera buat Delivery Order melalui link berikut:\n{link_create_do}\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'nama_pelanggan', 'approved_by', 'link_create_do'],
                'description' => 'Notifikasi untuk transaksi penjualan yang disetujui'
            ],
            'penjualan_approved_jasa' => [
                'template' => "✅ *Transaksi Jasa Disetujui*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nJenis Jasa: *{jenis_jasa}*\nStatus: *Disetujui*\nDisetujui oleh: *{approved_by}*\n\nMohon segera buat Delivery Order untuk pelaksanaan jasa.\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'nama_pelanggan', 'jenis_jasa', 'approved_by'],
                'description' => 'Notifikasi untuk transaksi jasa yang disetujui'
            ],
            'penjualan_rejected' => [
                'template' => "❌ *Transaksi Penjualan Ditolak*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nStatus: *Ditolak*\nDitolak oleh: *{rejected_by}*\nAlasan: *{rejection_reason}*\n\nMohon tindak lanjut sesuai kebijakan perusahaan.\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'nama_pelanggan', 'rejected_by', 'rejection_reason'],
                'description' => 'Notifikasi untuk transaksi penjualan yang ditolak'
            ],
            'penjualan_rejected_jasa' => [
                'template' => "❌ *Transaksi Jasa Ditolak*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nJenis Jasa: *{jenis_jasa}*\nStatus: *Ditolak*\nDitolak oleh: *{rejected_by}*\nAlasan: *{rejection_reason}*\n\nMohon tindak lanjut sesuai kebijakan perusahaan.\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'nama_pelanggan', 'jenis_jasa', 'rejected_by', 'rejection_reason'],
                'description' => 'Notifikasi untuk transaksi jasa yang ditolak'
            ],
            'penjualan_revision_jasa' => [
                'template' => "🔄 *Transaksi Jasa Perlu Revisi*\n\n📝 *Detail Transaksi:*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nJenis Jasa: *{jenis_jasa}*\nStatus: *Perlu Revisi*\nCatatan: *{revision_notes}*\n\nMohon lakukan perbaikan sesuai catatan.\n\nTerima kasih.",
                'variables' => ['kode_transaksi', 'nama_pelanggan', 'jenis_jasa', 'revision_notes'],
                'description' => 'Notifikasi untuk transaksi jasa yang perlu revisi'
            ],
            'delivery_order_created_jasa' => [
                'template' => "🚛 *Delivery Order Jasa Dibuat*\n\n📝 *Detail DO:*\nNo. DO: *{kode_do}*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nDriver: *{nama_driver}*\nKendaraan: *{kendaraan}*\nTanggal: *{tanggal_delivery}*\n\nDO siap untuk pelaksanaan jasa.\n\nTerima kasih.",
                'variables' => ['kode_do', 'kode_transaksi', 'nama_pelanggan', 'nama_driver', 'kendaraan', 'tanggal_delivery'],
                'description' => 'Notifikasi untuk delivery order jasa yang dibuat'
            ],
            'delivery_order_completed_jasa' => [
                'template' => "✅ *Delivery Order Jasa Selesai*\n\n📝 *Detail DO:*\nNo. DO: *{kode_do}*\nNo. Transaksi: *{kode_transaksi}*\nPelanggan: *{nama_pelanggan}*\nDriver: *{nama_driver}*\nStatus: *Selesai*\nWaktu Selesai: *{waktu_selesai}*\n\nJasa telah berhasil dilaksanakan.\n\nTerima kasih.",
                'variables' => ['kode_do', 'kode_transaksi', 'nama_pelanggan', 'nama_driver', 'waktu_selesai'],
                'description' => 'Notifikasi untuk delivery order jasa yang selesai'
            ],

            // Expense Request Templates
            'expense_baru' => [
                'template' => "💵 *Approval Permintaan Biaya Baru*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda permintaan biaya baru dari *{requester_name}* yang membutuhkan persetujuan Anda.\n\n🧾 *Detail Permintaan:*\nJudul: {expense_title}\nJumlah: *Rp {expense_amount}*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}",
                'variables' => ['manager_name', 'requester_name', 'expense_title', 'expense_amount', 'view_url'],
                'description' => 'Notifikasi untuk permintaan biaya baru'
            ],
            'expense_approved' => [
                'template' => "✅ *Permintaan Biaya Disetujui*\n\nHalo Bpk/Ibu *{requester_name}*,\nPermintaan biaya Anda dengan No. *{request_number}* telah *disetujui* oleh Bpk/Ibu *{approver_name}*.\n\nJumlah yang disetujui: *Rp {approved_amount}*\n\nSilakan lanjutkan proses sesuai prosedur perusahaan.",
                'variables' => ['requester_name', 'request_number', 'approver_name', 'approved_amount'],
                'description' => 'Notifikasi untuk permintaan biaya yang disetujui'
            ],
            'expense_rejected' => [
                'template' => "❌ *Permintaan Biaya Ditolak*\n\nHalo Bpk/Ibu *{requester_name}*,\nPermintaan biaya Anda dengan No. *{request_number}* telah *ditolak* oleh Bpk/Ibu *{approver_name}*.\n\nAlasan penolakan: *{rejection_reason}*\n\nJika ada pertanyaan, silakan hubungi atasan langsung Anda.",
                'variables' => ['requester_name', 'request_number', 'approver_name', 'rejection_reason'],
                'description' => 'Notifikasi untuk permintaan biaya yang ditolak'
            ],
            'expense_revision' => [
                'template' => "📝 *Permintaan Biaya Butuh Revisi*\n\nHalo Bpk/Ibu *{requester_name}*,\nPermintaan biaya Anda dengan No. *{request_number}* membutuhkan *revisi* dari Bpk/Ibu *{approver_name}*.\n\nCatatan untuk Revisi: *{revision_notes}*\n\nMohon segera perbaiki permintaan Anda melalui link berikut:\n{edit_url}",
                'variables' => ['requester_name', 'request_number', 'approver_name', 'revision_notes', 'edit_url'],
                'description' => 'Notifikasi untuk permintaan biaya yang perlu revisi'
            ],

            // SPH Templates
            'sph_baru' => [
                'template' => "📋 *SPH Baru Membutuhkan Persetujuan*\n\nHalo Bpk/Ibu *{manager_name}*,\nAda SPH baru dari *{creator_name}* yang membutuhkan persetujuan Anda.\n\n📝 *Detail SPH:*\nNo. SPH: *{sph_number}*\nPelanggan: *{customer_name}*\n\nMohon segera ditinjau melalui link berikut:\n{view_url}\n\nTerima kasih.",
                'variables' => ['manager_name', 'creator_name', 'sph_number', 'customer_name', 'view_url'],
                'description' => 'Notifikasi untuk SPH baru'
            ],

            // Invoice Templates
            'invoice_baru' => [
                'template' => "🧾 *Invoice Baru Dikirim*\n\nYth. Bpk/Ibu *{customer_contact}* / Tim Keuangan *{customer_company}*,\n\nKami telah mengirimkan invoice untuk transaksi Anda:\n\n📄 *Detail Invoice:*\nNomor Invoice: *{invoice_number}*\nNo. DO Terkait: *{do_number}*\nJumlah Tagihan: *Rp {invoice_amount}*\nJatuh Tempo: *{due_date}*\n\nInvoice telah dikirim melalui email dan dapat diunduh melalui link berikut:\n{invoice_pdf_url}\n\nMohon untuk melakukan pembayaran sesuai dengan tanggal jatuh tempo.\nJika ada pertanyaan, jangan ragu untuk menghubungi kami.\nTerima kasih atas kerjasamanya.\n\nHormat kami,\nTim Keuangan {app_name}",
                'variables' => ['customer_contact', 'customer_company', 'invoice_number', 'do_number', 'invoice_amount', 'due_date', 'invoice_pdf_url', 'app_name'],
                'description' => 'Notifikasi untuk invoice baru yang dikirim'
            ],
            'invoice_confirmation' => [
                'template' => "📨 *Konfirmasi Penerimaan Invoice {invoice_number}*\n\nYth. Bpk/Ibu *{customer_contact}* / Tim Keuangan *{customer_company}*,\n\nKami ingin memastikan bahwa Anda telah menerima dengan baik invoice kami:\nNomor Invoice: *{invoice_number}*\nNo. DO Terkait: *{do_number}*\nDikirim pada: {sent_date}\n\nMohon kesediaannya untuk memberikan konfirmasi penerimaan dengan membalas pesan ini atau melalui link:\n{confirmation_link}\n\nKonfirmasi Anda sangat berarti bagi kami untuk kelancaran administrasi.\nTerima kasih.",
                'variables' => ['invoice_number', 'customer_contact', 'customer_company', 'do_number', 'sent_date', 'confirmation_link'],
                'description' => 'Notifikasi konfirmasi penerimaan invoice'
            ],

            // Payment Templates
            'pembayaran_diterima' => [
                'template' => "💰 *Konfirmasi Pembayaran Diterima*\n\nYth. Bpk/Ibu *{customer_contact}* / Tim Keuangan *{customer_company}*,\n\nKami dengan senang hati mengkonfirmasi bahwa pembayaran Anda telah kami terima:\n\n💳 *Detail Pembayaran:*\nNomor Invoice: *{invoice_number}*\nJumlah Dibayar: *Rp {amount_paid}*\nTanggal Pembayaran: *{payment_date}*\n\nTerima kasih atas pembayaran yang tepat waktu.\nKami sangat menghargai kerjasama yang baik ini.\n\nHormat kami,\nTim Keuangan {app_name}",
                'variables' => ['customer_contact', 'customer_company', 'invoice_number', 'amount_paid', 'payment_date', 'app_name'],
                'description' => 'Notifikasi untuk pembayaran yang diterima'
            ],
        ];
    }

    /**
     * Generate message dari template dengan data
     */
    public static function generateMessage(string $template, array $data): string
    {
        $message = $template;

        foreach ($data as $key => $value) {
            $placeholder = '{' . $key . '}';
            $message = str_replace($placeholder, $value, $message);
        }

        return $message;
    }

    /**
     * Get data untuk transaksi penjualan
     */
    public static function getTransaksiData(TransaksiPenjualan $transaksi, array $additionalData = []): array
    {
        // Load relationships if not already loaded
        $transaksi->loadMissing(['pelanggan', 'createdBy', 'penjualanDetails.item']);

        // Generate better kode if it's just "-"
        $kodeTransaksi = $transaksi->kode;
        if ($kodeTransaksi === '-' || empty($kodeTransaksi)) {
            $kodeTransaksi = 'TRX-' . str_pad($transaksi->id, 6, '0', STR_PAD_LEFT);
        }

        $data = [
            'kode_transaksi' => $kodeTransaksi,
            'tanggal_transaksi' => $transaksi->tanggal ? $transaksi->tanggal->format('d/m/Y H:i') : '-',
            'nama_pelanggan' => $transaksi->pelanggan->nama ?? '-',
            'salesperson_name' => $transaksi->createdBy->name ?? 'N/A',
            'status_transaksi' => ucfirst($transaksi->status ?? 'pending'),
            'total_transaksi' => 'IDR ' . number_format($transaksi->penjualanDetails->sum(function ($detail) {
                return $detail->volume_item * $detail->harga_jual;
            }), 0, ',', '.'),
            'view_url' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $transaksi->id]),
        ];

        // Add jasa-specific data
        if ($transaksi->tipe === 'jasa') {
            $jenisJasa = $transaksi->penjualanDetails->pluck('item.name')->implode(', ');
            $data['jenis_jasa'] = $jenisJasa ?: 'Jasa Umum';
            // Use the same route but different resource for jasa
            try {
                $data['view_url'] = route('filament.admin.resources.transaksi-jasas.view', ['record' => $transaksi->id]);
            } catch (\Exception $e) {
                // Fallback to transaksi penjualan route if jasa route doesn't exist
                $data['view_url'] = route('filament.admin.resources.transaksi-penjualans.view', ['record' => $transaksi->id]);
            }
        }

        // Merge additional data
        return array_merge($data, $additionalData);
    }

    /**
     * Get data untuk delivery order
     */
    public static function getDeliveryOrderData(DeliveryOrder $deliveryOrder, array $additionalData = []): array
    {
        $data = [
            'kode_do' => $deliveryOrder->kode ?? '-',
            'kode_transaksi' => $deliveryOrder->transaksi->kode ?? '-',
            'nama_pelanggan' => $deliveryOrder->transaksi->pelanggan->nama ?? '-',
            'nama_driver' => $deliveryOrder->user->name ?? '-',
            'kendaraan' => $deliveryOrder->kendaraan->nomor_polisi ?? '-',
            'tanggal_delivery' => $deliveryOrder->tanggal_delivery ? $deliveryOrder->tanggal_delivery->format('d/m/Y H:i') : '-',
        ];

        // Merge additional data
        return array_merge($data, $additionalData);
    }

    /**
     * Get message template untuk event
     */
    public static function getMessageForEvent(string $eventName, array $data, ?int $userId = null): string
    {
        // Cari custom template dari notification setting
        $notificationSetting = NotificationSetting::where('event_name', $eventName)
            ->when($userId, function ($query, $userId) {
                return $query->where('user_id', $userId);
            })
            ->whereNotNull('message_template')
            ->first();

        // Gunakan custom template jika ada
        if ($notificationSetting && !empty($notificationSetting->message_template)) {
            return self::generateMessage($notificationSetting->message_template, $data);
        }

        // Fallback ke default template
        $defaultTemplates = self::getDefaultTemplates();
        $template = $defaultTemplates[$eventName]['template'] ?? "Notifikasi: {event_name}\n\nData: {data}";

        return self::generateMessage($template, array_merge($data, [
            'event_name' => $eventName,
            'data' => json_encode($data, JSON_PRETTY_PRINT)
        ]));
    }

    /**
     * Generate preview message dengan sample data
     */
    public static function generatePreview(string $eventName, string $template): string
    {
        $sampleData = self::getSampleDataForEvent($eventName);
        return self::generateMessage($template, $sampleData);
    }

    /**
     * Get sample data untuk preview
     */
    private static function getSampleDataForEvent(string $eventName): array
    {
        $baseSample = [
            'kode_transaksi' => 'TRX-000001',
            'tanggal_transaksi' => '16/01/2025 10:30',
            'nama_pelanggan' => 'PT. Contoh Pelanggan',
            'salesperson_name' => 'John Doe',
            'status_transaksi' => 'Pending',
            'total_transaksi' => 'IDR 1,500,000',
            'approved_by' => 'Manager Operasional',
            'rejected_by' => 'Manager Keuangan',
            'rejection_reason' => 'Dokumen tidak lengkap',
            'revision_notes' => 'Mohon lengkapi dokumen PO',
            'view_url' => url('/admin/transaksi-penjualans/1'),
            'link_create_do' => url('/admin/delivery-orders/create'),
        ];

        if (Str::contains($eventName, 'jasa')) {
            $baseSample['jenis_jasa'] = 'Transportasi BBM, Maintenance Tangki';
        }

        if (Str::contains($eventName, 'delivery_order')) {
            $baseSample = array_merge($baseSample, [
                'kode_do' => 'DO-2025-001',
                'nama_driver' => 'Budi Santoso',
                'kendaraan' => 'B 1234 ABC',
                'tanggal_delivery' => '16/01/2025 14:00',
                'waktu_selesai' => '16/01/2025 16:30',
            ]);
        }

        return $baseSample;
    }
}
