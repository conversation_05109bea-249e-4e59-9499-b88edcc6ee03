<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class InvoiceDeduction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'invoice_deductions';

    protected $fillable = [
        'invoice_id',
        'deduction_type',
        'amount',
        'percentage',
        'description',
        'deduction_date',
        'reference_number',
        'notes',
        'journal_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'percentage' => 'decimal:2',
        'deduction_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Deduction type constants
     */
    public const TYPE_PPN = 'ppn';
    public const TYPE_PPH = 'pph';
    public const TYPE_LOSIS = 'losis';
    public const TYPE_ADMIN_BANK = 'admin_bank';

    public static function getTypeOptions(): array
    {
        return [
            self::TYPE_PPN => 'PPN (<PERSON>jak <PERSON>)',
            self::TYPE_PPH => 'PPh (Pajak <PERSON>ghasilan)',
            self::TYPE_LOSIS => 'Losis (Loss in Storage)',
            self::TYPE_ADMIN_BANK => 'Admin Bank',
        ];
    }

    public static function getTypeLabels(): array
    {
        return [
            self::TYPE_PPN => 'PPN',
            self::TYPE_PPH => 'PPh',
            self::TYPE_LOSIS => 'Losis',
            self::TYPE_ADMIN_BANK => 'Admin Bank',
        ];
    }

    /**
     * Relationships
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    /**
     * Scopes
     */
    public function scopeByType($query, $type)
    {
        return $query->where('deduction_type', $type);
    }

    public function scopeByInvoice($query, $invoiceId)
    {
        return $query->where('invoice_id', $invoiceId);
    }

    /**
     * Accessors
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'IDR ' . number_format($this->amount, 0, ',', '.');
    }

    public function getTypeNameAttribute(): string
    {
        return self::getTypeLabels()[$this->deduction_type] ?? $this->deduction_type;
    }

    /**
     * Auto-post deduction to journal
     */
    public function createJournalEntry(): ?Journal
    {
        if ($this->journal_id) {
            return $this->journal; // Journal already exists
        }

        try {
            $journalingService = new \App\Services\JournalingService();
            $journalingService->postTransaction('InvoiceDeduction', $this);

            // Find the created journal
            $journal = \App\Models\Journal::where('source_type', 'InvoiceDeduction')
                ->where('source_id', $this->id)
                ->latest()
                ->first();

            if ($journal) {
                $this->update(['journal_id' => $journal->id]);
                return $journal;
            }

            return null;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to create journal entry for invoice deduction', [
                'deduction_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function getFormattedDeductionDateAttribute(): string
    {
        return $this->deduction_date ? $this->deduction_date->format('d/m/Y H:i') : '';
    }

    /**
     * Boot method for auto-setting user fields
     */
    protected static function booted(): void
    {
        static::creating(function (InvoiceDeduction $deduction) {
            if (auth()->check()) {
                $deduction->created_by = auth()->id();
            }
        });

        static::updating(function (InvoiceDeduction $deduction) {
            if (auth()->check()) {
                $deduction->updated_by = auth()->id();
            }
        });
    }
}
