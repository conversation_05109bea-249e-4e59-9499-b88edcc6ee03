<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Sph;
use App\Services\SphService;
use Illuminate\Support\Facades\Log;

class TestSphNotification extends Command
{
    protected $signature = 'test:sph-notification {sph_id?}';
    protected $description = 'Test SPH notification system';

    public function handle()
    {
        $sphId = $this->argument('sph_id');
        
        if (!$sphId) {
            // Get first draft SPH
            $sph = Sph::where('status', 'draft')->first();
            if (!$sph) {
                $this->error('No draft SPH found');
                return;
            }
        } else {
            $sph = Sph::find($sphId);
            if (!$sph) {
                $this->error("SPH with ID {$sphId} not found");
                return;
            }
        }

        $this->info("Testing SPH notification for SPH ID: {$sph->id}");
        $this->info("SPH Number: {$sph->sph_number}");
        $this->info("Current Status: {$sph->status}");

        try {
            $sphService = app(SphService::class);
            
            $this->info("Calling submitForApproval...");
            $sphService->submitForApproval($sph);
            
            $this->info("✅ SPH submitted successfully!");
            $this->info("Check the log file for detailed output: storage/logs/laravel.log");
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            Log::error("Test SPH notification failed", [
                'sph_id' => $sph->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
