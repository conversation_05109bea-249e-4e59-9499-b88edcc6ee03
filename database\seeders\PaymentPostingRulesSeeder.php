<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class PaymentPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Delete existing payment posting rules
        PostingRule::where('source_type', 'Payment')->delete();

        $this->createPaymentPostingRules();
    }

    private function createPaymentPostingRules()
    {
        // Get required accounts based on new COA
        $accounts = [
            'cash' => Akun::where('kode_akun', '100')->first(), // Cash in Hand
            'bank_mandiri' => Akun::where('kode_akun', '101')->first(), // Giro Bank Mandiri
            'bank_bni' => Akun::where('kode_akun', '102')->first(), // Giro Bank BNI
            'bank_bni_usd' => Akun::where('kode_akun', '103')->first(), // Giro USD BNI
            'bank_cimb' => Akun::where('kode_akun', '104')->first(), // Giro Bank CIMB Niaga
            'bank_bsi' => Akun::where('kode_akun', '105')->first(), // Giro Bank BSI
            'hutang_usaha' => Akun::where('kode_akun', '306')->first(), // Hutang Usaha
            'hutang_bank' => Akun::where('kode_akun', '307')->first(), // Hutang Bank
            'hutang_leasing' => Akun::where('kode_akun', '308')->first(), // Hutang Leasing/Kendaraan
            'hutang_pajak' => Akun::where('kode_akun', '309')->first(), // Hutang Pajak
        ];

        // Validate required accounts
        if (!$accounts['cash'] || !$accounts['hutang_usaha']) {
            $this->command->error('Required accounts not found. Please run CoaSeeder first.');
            return;
        }

        $this->createSupplierPaymentRules($accounts);
        $this->createLoanPaymentRules($accounts);
        $this->createTaxPaymentRules($accounts);
        $this->createLeasingPaymentRules($accounts);

        $this->command->info('✅ Payment Posting Rules created successfully!');
    }

    private function createSupplierPaymentRules($accounts)
    {
        // 1. Pembayaran Hutang Supplier - Cash
        $supplierCashRule = PostingRule::create([
            'rule_name' => 'Pembayaran Hutang Supplier - Cash',
            'source_type' => 'Payment',
            'trigger_condition' => ['payment_type' => 'supplier', 'payment_method' => 'cash'],
            'description' => 'Auto posting untuk pembayaran hutang supplier dengan cash',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Hutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $supplierCashRule->id,
            'account_id' => $accounts['hutang_usaha']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran hutang supplier {source.supplier_name} - {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Cash
        PostingRuleEntry::create([
            'posting_rule_id' => $supplierCashRule->id,
            'account_id' => $accounts['cash']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pengeluaran cash untuk hutang supplier {source.supplier_name} - {source.reference_number}',
            'sort_order' => 2,
        ]);

        // 2. Pembayaran Hutang Supplier - Bank Transfer
        $supplierBankRule = PostingRule::create([
            'rule_name' => 'Pembayaran Hutang Supplier - Bank Transfer',
            'source_type' => 'Payment',
            'trigger_condition' => ['payment_type' => 'supplier', 'payment_method' => 'bank_transfer'],
            'description' => 'Auto posting untuk pembayaran hutang supplier dengan bank transfer',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Hutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $supplierBankRule->id,
            'account_id' => $accounts['hutang_usaha']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran hutang supplier {source.supplier_name} - {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Bank (default to Mandiri)
        PostingRuleEntry::create([
            'posting_rule_id' => $supplierBankRule->id,
            'account_id' => $accounts['bank_mandiri']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Transfer bank untuk hutang supplier {source.supplier_name} - {source.reference_number}',
            'sort_order' => 2,
        ]);
    }

    private function createLoanPaymentRules($accounts)
    {
        // 3. Pembayaran Hutang Bank
        $loanPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Hutang Bank',
            'source_type' => 'Payment',
            'trigger_condition' => ['payment_type' => 'loan', 'payment_method' => 'bank_transfer'],
            'description' => 'Auto posting untuk pembayaran hutang bank',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Debit: Hutang Bank
        PostingRuleEntry::create([
            'posting_rule_id' => $loanPaymentRule->id,
            'account_id' => $accounts['hutang_bank']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran hutang bank {source.bank_name} - {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Bank
        PostingRuleEntry::create([
            'posting_rule_id' => $loanPaymentRule->id,
            'account_id' => $accounts['bank_mandiri']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Transfer untuk hutang bank {source.bank_name} - {source.reference_number}',
            'sort_order' => 2,
        ]);
    }

    private function createTaxPaymentRules($accounts)
    {
        // 4. Pembayaran Pajak
        $taxPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Pajak',
            'source_type' => 'Payment',
            'trigger_condition' => ['payment_type' => 'tax'],
            'description' => 'Auto posting untuk pembayaran pajak',
            'is_active' => true,
            'priority' => 4,
            'created_by' => 1,
        ]);

        // Debit: Hutang Pajak
        PostingRuleEntry::create([
            'posting_rule_id' => $taxPaymentRule->id,
            'account_id' => $accounts['hutang_pajak']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran pajak {source.tax_type} - {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Bank (default to Mandiri)
        PostingRuleEntry::create([
            'posting_rule_id' => $taxPaymentRule->id,
            'account_id' => $accounts['bank_mandiri']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Transfer untuk pembayaran pajak {source.tax_type} - {source.reference_number}',
            'sort_order' => 2,
        ]);
    }

    private function createLeasingPaymentRules($accounts)
    {
        // 5. Pembayaran Leasing Kendaraan
        $leasingPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Leasing Kendaraan',
            'source_type' => 'Payment',
            'trigger_condition' => ['payment_type' => 'leasing'],
            'description' => 'Auto posting untuk pembayaran leasing kendaraan',
            'is_active' => true,
            'priority' => 5,
            'created_by' => 1,
        ]);

        // Debit: Hutang Leasing/Kendaraan
        PostingRuleEntry::create([
            'posting_rule_id' => $leasingPaymentRule->id,
            'account_id' => $accounts['hutang_leasing']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Pembayaran leasing {source.leasing_company} - {source.reference_number}',
            'sort_order' => 1,
        ]);

        // Credit: Bank (default to Mandiri)
        PostingRuleEntry::create([
            'posting_rule_id' => $leasingPaymentRule->id,
            'account_id' => $accounts['bank_mandiri']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'amount',
            'description_template' => 'Transfer untuk leasing {source.leasing_company} - {source.reference_number}',
            'sort_order' => 2,
        ]);
    }
}
