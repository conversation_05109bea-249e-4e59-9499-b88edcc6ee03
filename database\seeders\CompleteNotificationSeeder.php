<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationSetting;
use App\Models\User;
use App\Services\NotificationTemplateService;
use Illuminate\Support\Facades\Log;

class CompleteNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Creates notification settings for ALL events with templates.
     */
    public function run(): void
    {
        Log::info('Starting CompleteNotificationSeeder...');

        // Cari user dengan role manager atau admin yang memiliki nomor HP
        $managers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super_admin', 'admin', 'manager']);
        })
            ->whereNotNull('hp')
            ->where('hp', '!=', '')
            ->get();

        if ($managers->isEmpty()) {
            Log::warning('No managers/admins with phone numbers found. Creating notification settings for all users with phone numbers.');

            // Jika tidak ada manager, ambil semua user yang punya HP
            $managers = User::whereNotNull('hp')
                ->where('hp', '!=', '')
                ->limit(3) // Batasi maksimal 3 user untuk menghindari spam
                ->get();
        }

        // Get all available templates
        $allTemplates = NotificationTemplateService::getDefaultTemplates();
        
        $createdCount = 0;
        $existingCount = 0;
        $updatedCount = 0;

        foreach ($managers as $manager) {
            foreach ($allTemplates as $eventName => $templateData) {
                // Check if notification setting already exists
                $existing = NotificationSetting::where('event_name', $eventName)
                    ->where('user_id', $manager->id)
                    ->where('channel', 'whatsapp')
                    ->first();

                if ($existing) {
                    // Update template if not set
                    if (empty($existing->message_template)) {
                        $existing->update([
                            'message_template' => $templateData['template'],
                            'template_variables' => $templateData['variables'],
                            'message_preview' => NotificationTemplateService::generatePreview($eventName, $templateData['template']),
                        ]);
                        $updatedCount++;
                        Log::info("Updated template for existing event '{$eventName}' for user " . ($manager->name ?? 'Unknown'));
                    } else {
                        $existingCount++;
                        Log::info("Event '{$eventName}' for user " . ($manager->name ?? 'Unknown') . " already has template");
                    }
                } else {
                    // Create new notification setting with template
                    NotificationSetting::create([
                        'event_name' => $eventName,
                        'user_id' => $manager->id,
                        'channel' => 'whatsapp',
                        'is_active' => true,
                        'message_template' => $templateData['template'],
                        'template_variables' => $templateData['variables'],
                        'message_preview' => NotificationTemplateService::generatePreview($eventName, $templateData['template']),
                    ]);

                    $createdCount++;
                    Log::info("Created new notification setting with template: {$eventName} for user " . ($manager->name ?? 'Unknown'));
                }
            }
        }

        Log::info("CompleteNotificationSeeder completed. Created {$createdCount} new settings, updated {$updatedCount} templates, {$existingCount} already complete for " . $managers->count() . " users.");

        // Tampilkan ringkasan
        $this->command->info("✅ Created {$createdCount} new notification settings");
        $this->command->info("🔄 Updated {$updatedCount} existing settings with templates");
        if ($existingCount > 0) {
            $this->command->info("ℹ️  {$existingCount} settings already complete");
        }
        $this->command->info("📱 Configured for " . $managers->count() . " users:");

        foreach ($managers as $manager) {
            $this->command->info("   - {$manager->name} ({$manager->hp})");
        }

        $this->command->info("🔔 All Events with templates:");
        foreach ($allTemplates as $eventName => $templateData) {
            $count = NotificationSetting::where('event_name', $eventName)->count();
            $this->command->info("   - {$eventName}: {$count} users");
        }

        // Show total templates
        $totalWithTemplates = NotificationSetting::whereNotNull('message_template')->count();
        $totalSettings = NotificationSetting::count();
        $this->command->info("📊 Total notification settings with templates: {$totalWithTemplates}/{$totalSettings}");
    }
}
