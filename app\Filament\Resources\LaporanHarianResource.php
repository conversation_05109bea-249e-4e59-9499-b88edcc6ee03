<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LaporanHarianResource\Pages;
use App\Filament\Resources\LaporanHarianResource\RelationManagers;
use App\Models\LaporanHarian;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class LaporanHarianResource extends Resource
{
    protected static ?string $model = LaporanHarian::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Laporan Harian';
    protected static ?string $modelLabel = 'Laporan Harian';
    protected static ?string $pluralModelLabel = 'Laporan Harian';
    protected static ?string $navigationGroup = 'Security';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Laporan')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_laporan')
                            ->label('Tanggal Laporan')
                            ->required()
                            ->default(now())
                            ->maxDate(now()),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Foto Mobil Tangki dengan Muatan')
                    ->description('Upload minimal 4 foto mobil tangki yang ada muatan')
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('foto_mobil_tangki')
                            ->label('Foto Mobil Tangki')
                            ->collection('foto_mobil_tangki')
                            ->multiple()
                            ->reorderable()
                            ->minFiles(4)
                            ->maxFiles(10)
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->required()
                            ->helperText('Upload minimal 4 foto mobil tangki dengan muatan'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Foto Laporan Tamu Keluar Masuk')
                    ->description('Upload minimal 2 foto laporan tamu (jika ada)')
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('foto_laporan_tamu')
                            ->label('Foto Laporan Tamu')
                            ->collection('foto_laporan_tamu')
                            ->multiple()
                            ->reorderable()
                            ->minFiles(0)
                            ->maxFiles(10)
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->helperText('Upload minimal 2 foto laporan tamu keluar masuk (jika ada)'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Catatan')
                    ->schema([
                        Forms\Components\Textarea::make('catatan')
                            ->label('Catatan')
                            ->rows(4)
                            ->placeholder('Masukkan catatan tambahan...'),
                    ])
                    ->columns(1),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_laporan')
                    ->label('Tanggal Laporan')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('foto_mobil_tangki_count')
                    ->label('Foto Mobil Tangki')
                    ->getStateUsing(fn(LaporanHarian $record) => $record->getMedia('foto_mobil_tangki')->count())
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        $state >= 4 => 'success',
                        $state > 0 => 'warning',
                        default => 'danger',
                    }),

                Tables\Columns\TextColumn::make('foto_laporan_tamu_count')
                    ->label('Foto Laporan Tamu')
                    ->getStateUsing(fn(LaporanHarian $record) => $record->getMedia('foto_laporan_tamu')->count())
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        $state >= 2 => 'success',
                        $state > 0 => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('catatan')
                    ->label('Catatan')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('tanggal_laporan')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_laporan', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_laporan', '<=', $date),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('tanggal_laporan', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            \App\Filament\Resources\LaporanHarianResource\Widgets\LaporanHarianStatsWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLaporanHarians::route('/'),
            'create' => Pages\CreateLaporanHarian::route('/create'),
            'view' => Pages\ViewLaporanHarian::route('/{record}'),
            'edit' => Pages\EditLaporanHarian::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
