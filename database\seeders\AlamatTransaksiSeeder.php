<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TransaksiPenjualan;
use App\Models\AlamatTransaksi;

class AlamatTransaksiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing transaksi penjualan
        $transaksiList = TransaksiPenjualan::limit(10)->get();

        if ($transaksiList->isEmpty()) {
            $this->command->info('No transaksi penjualan found. Please run TransaksiPenjualanSeeder first.');
            return;
        }

        foreach ($transaksiList as $transaksi) {
            // Generate random number of addresses (1-3)
            $addressCount = rand(1, 3);
            
            for ($i = 1; $i <= $addressCount; $i++) {
                AlamatTransaksi::create([
                    'id_transaksi_penjualan' => $transaksi->id,
                    'alamat' => $this->generateAlamat($i),
                    'keterangan' => $this->generateKeterangan($i, $addressCount),
                    'urutan' => $i,
                    'created_by' => 1, // Admin user
                ]);
            }
        }

        $this->command->info('Alamat transaksi seeded successfully!');
    }

    /**
     * Generate alamat based on urutan
     */
    private function generateAlamat(int $urutan): string
    {
        $alamatTemplates = [
            1 => [
                'Jl. Sudirman No. 123, Blok A, RT 01/RW 05, Kelurahan Menteng, Kecamatan Menteng, Jakarta Pusat 10310',
                'Jl. Gatot Subroto Km. 5, Komplek Industri Blok B-15, RT 03/RW 08, Kelurahan Kuningan, Jakarta Selatan 12950',
                'Jl. Ahmad Yani No. 456, Perumahan Griya Asri Blok C-7, RT 02/RW 04, Kelurahan Kemayoran, Jakarta Pusat 10630',
                'Jl. Thamrin No. 789, Gedung Perkantoran Lt. 15, RT 05/RW 02, Kelurahan Tanah Abang, Jakarta Pusat 10230',
            ],
            2 => [
                'Jl. Raya Bekasi Km. 18, Kawasan Industri MM2100 Blok A-12, RT 01/RW 03, Kelurahan Wanasari, Cibitung, Bekasi 17520',
                'Jl. Raya Bogor No. 234, Komplek Pergudangan Sentul Blok D-8, RT 04/RW 07, Kelurahan Babakan Madang, Bogor 16810',
                'Jl. Raya Tangerang Km. 12, Kawasan Berikat Nusantara Blok F-3, RT 02/RW 05, Kelurahan Benda, Tangerang 15125',
            ],
            3 => [
                'Jl. Bypass Ngurah Rai No. 567, Kawasan Industri Tuban Blok E-9, RT 03/RW 06, Kelurahan Tuban, Badung, Bali 80361',
                'Jl. Raya Surabaya-Malang Km. 45, Komplek Logistik Singosari Blok G-4, RT 01/RW 02, Kelurahan Ardimulyo, Malang 65153',
                'Jl. Soekarno-Hatta No. 890, Terminal BBM Regional Blok H-11, RT 05/RW 09, Kelurahan Bandara, Semarang 50282',
            ]
        ];

        $templates = $alamatTemplates[$urutan] ?? $alamatTemplates[1];
        return $templates[array_rand($templates)];
    }

    /**
     * Generate keterangan based on scenario
     */
    private function generateKeterangan(int $urutan, int $total): ?string
    {
        if ($total === 1) {
            return null; // Single address, no special notes needed
        }

        $keteranganOptions = [
            1 => [
                'Alamat utama pengiriman - akses 24 jam',
                'Lokasi utama - koordinasi dengan security',
                'Alamat primer - hubungi PIC sebelum pengiriman',
                'Lokasi utama - tersedia crane untuk bongkar muat',
            ],
            2 => [
                'Alamat alternatif - jika lokasi utama tidak dapat diakses',
                'Lokasi cadangan - gunakan jika ada kendala di alamat pertama',
                'Alamat sekunder - koordinasi dengan supervisor lapangan',
                'Lokasi backup - akses terbatas jam kerja saja',
            ],
            3 => [
                'Alamat darurat - hanya untuk kondisi khusus',
                'Lokasi emergency - gunakan jika kedua alamat sebelumnya bermasalah',
                'Alamat tersier - memerlukan izin khusus untuk akses',
                'Lokasi cadangan terakhir - koordinasi intensif diperlukan',
            ]
        ];

        $options = $keteranganOptions[$urutan] ?? [
            'Alamat tambahan untuk transaksi ini',
            'Lokasi pengiriman alternatif',
            'Alamat khusus sesuai permintaan customer',
        ];

        return $options[array_rand($options)];
    }
}
