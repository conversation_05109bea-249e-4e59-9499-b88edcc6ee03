<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SuratResource\Pages;
use App\Models\Surat;
use App\Models\User;
use App\Services\SuratService;
use App\Services\SuratPdfService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class SuratResource extends Resource
{
    protected static ?string $model = Surat::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Surat';

    protected static ?string $modelLabel = 'Surat';

    protected static ?string $pluralModelLabel = 'Surat';

    protected static ?int $navigationSort = 25;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Informasi Surat')
                            ->schema([
                                Select::make('letter_setting_id')
                                    ->label('Penerbitan Surat')
                                    ->relationship('letterSetting', 'name', fn(Builder $query) => $query->where('is_active', true))
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live(),

                                TextInput::make('surat_number')
                                    ->label('Nomor Surat')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->helperText('Nomor surat akan dibuat otomatis.'),

                                TextInput::make('title')
                                    ->label('Judul Surat')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpanFull(),

                                DatePicker::make('surat_date')
                                    ->label('Tanggal Surat')
                                    ->required()
                                    ->default(now()),

                                Select::make('signed_by_id')
                                    ->label('Ditandatangani Oleh')
                                    ->relationship('signedBy', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->placeholder('Pilih penandatangan')
                                    ->helperText('Pilih siapa yang akan menandatangani surat ini'),

                                Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'draft' => 'Draft',
                                        'sent' => 'Terkirim',
                                        'archived' => 'Diarsipkan',
                                    ])
                                    ->default('draft')
                                    ->required(),
                            ])->columns(2),

                        Section::make('Konten Surat')
                            ->schema([
                                RichEditor::make('content')
                                    ->label('Isi Surat')
                                    ->required()
                                    ->columnSpanFull()
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'underline',
                                        'strike',
                                        'link',
                                        'heading',
                                        'bulletList',
                                        'orderedList',
                                        'blockquote',
                                        'codeBlock',
                                    ]),
                            ]),

                        Section::make('Dokumen & Catatan')
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('dokumen_surat')
                                    ->label('Unggah Dokumen')
                                    ->collection('dokumen_surat')
                                    ->multiple()
                                    ->maxSize(10240),

                                Textarea::make('notes_internal')
                                    ->label('Catatan Internal')
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ])->collapsible(),
                    ])->columnSpan(['lg' => 2]),
            ])->columns(['lg' => 2]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('surat_number')
                    ->label('No. Surat')
                    ->searchable()
                    ->weight(FontWeight::Bold),

                Tables\Columns\TextColumn::make('title')
                    ->label('Judul Surat')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('letterSetting.name')
                    ->label('Penerbitan')
                    ->searchable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(Surat $record) => $record->status_color)
                    ->formatStateUsing(fn(Surat $record) => $record->status_label),

                Tables\Columns\TextColumn::make('surat_date')
                    ->label('Tanggal Surat')
                    ->date('d M Y'),

                Tables\Columns\TextColumn::make('signedBy.name')
                    ->label('Ditandatangani Oleh')
                    ->placeholder('Belum ditentukan'),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'sent' => 'Terkirim',
                        'archived' => 'Diarsipkan',
                    ])
                    ->multiple(),

                Tables\Filters\SelectFilter::make('letter_setting_id')
                    ->label('Penerbitan')
                    ->relationship('letterSetting', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('preview_pdf')
                    ->label('Preview PDF')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn(Surat $record) => route('surat.preview-pdf', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('download_pdf')
                    ->label('Download PDF')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->url(fn(Surat $record) => route('surat.download-pdf', $record)),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSurats::route('/'),
            'create' => Pages\CreateSurat::route('/create'),
            'view' => Pages\ViewSurat::route('/{record}'),
            'edit' => Pages\EditSurat::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
