<?php

namespace App\Services;

use App\Models\Surat;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SuratService
{
    protected NumberingService $numberingService;

    public function __construct(NumberingService $numberingService)
    {
        $this->numberingService = $numberingService;
    }

    /**
     * Creates a new Surat.
     */
    public function createSurat(User $creator, array $data): Surat
    {
        return DB::transaction(function () use ($creator, $data) {
            // Prepare and create the surat record
            // Use 'sph' type to continue the same numbering sequence as SPH
            $suratData = array_merge($data, [
                'surat_number' => $this->numberingService->generateNumber('sph'),
                'created_by' => $creator->id,
                'status' => 'draft',
            ]);

            $surat = Surat::create($suratData);

            Log::info("Surat created successfully", [
                'surat_id' => $surat->id,
                'surat_number' => $surat->surat_number,
                'created_by' => $creator->id,
            ]);

            return $surat;
        });
    }

    /**
     * Updates an existing Surat.
     */
    public function updateSurat(Surat $surat, array $data): Surat
    {
        return DB::transaction(function () use ($surat, $data) {
            // Don't allow updating surat_number and created_by
            unset($data['surat_number'], $data['created_by']);

            $surat->update($data);

            Log::info("Surat updated successfully", [
                'surat_id' => $surat->id,
                'surat_number' => $surat->surat_number,
            ]);

            return $surat->fresh();
        });
    }

    /**
     * Changes the status of a Surat.
     */
    public function changeStatus(Surat $surat, string $status): Surat
    {
        $validStatuses = ['draft', 'sent', 'archived'];

        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException("Invalid status: {$status}");
        }

        $surat->update(['status' => $status]);

        Log::info("Surat status changed", [
            'surat_id' => $surat->id,
            'surat_number' => $surat->surat_number,
            'old_status' => $surat->getOriginal('status'),
            'new_status' => $status,
        ]);

        return $surat->fresh();
    }

    /**
     * Deletes a Surat (soft delete).
     */
    public function deleteSurat(Surat $surat): bool
    {
        return DB::transaction(function () use ($surat) {
            $result = $surat->delete();

            Log::info("Surat deleted successfully", [
                'surat_id' => $surat->id,
                'surat_number' => $surat->surat_number,
            ]);

            return $result;
        });
    }

    /**
     * Get the next surat number that would be generated (for preview).
     */
    public function previewNextNumber(): string
    {
        return $this->numberingService->previewNextNumber('surat');
    }

    /**
     * Check if a surat can be edited.
     */
    public function canEdit(Surat $surat): bool
    {
        return $surat->status === 'draft';
    }

    /**
     * Check if a surat can be sent.
     */
    public function canSend(Surat $surat): bool
    {
        return $surat->status === 'draft';
    }

    /**
     * Check if a surat can be archived.
     */
    public function canArchive(Surat $surat): bool
    {
        return in_array($surat->status, ['sent']);
    }
}
