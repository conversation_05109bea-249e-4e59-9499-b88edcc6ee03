<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Creates the invoice_payments table for managing multi-payment history for invoices.
     */
    public function up(): void
    {
        Schema::create('invoice_payments', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to invoice
            $table->foreignId('invoice_id')
                ->constrained('invoice')
                ->onDelete('cascade');
            
            // Payment details
            $table->datetime('payment_date');
            $table->decimal('amount', 15, 2);
            
            // Foreign key to payment method
            $table->foreignId('payment_method_id')
                ->nullable()
                ->constrained('payment_methods')
                ->nullOnDelete();
            
            // Payment reference and notes
            $table->string('reference_number')->nullable();
            $table->text('notes')->nullable();
            
            // Payment status
            $table->enum('status', ['pending', 'accepted', 'rejected', 'cancelled'])
                ->default('pending');
            
            // Journal integration
            $table->foreignId('journal_id')
                ->nullable()
                ->constrained('journals')
                ->nullOnDelete();
            
            // Audit fields
            $table->foreignId('created_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->foreignId('updated_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['invoice_id', 'status']);
            $table->index(['payment_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_payments');
    }
};
