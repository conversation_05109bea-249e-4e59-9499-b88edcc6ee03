<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class AllPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting All Posting Rules Seeding...');

        // 1. Seed COA first (if not already done)
        $this->command->info('📊 Seeding Chart of Accounts...');
        $this->call(CoaSeeder::class);

        // 2. Seed Expense Categories (if not already done)
        $this->command->info('💸 Seeding Expense Categories...');
        $this->call(ExpenseCategorySeeder::class);

        // 3. Seed Expense Request Posting Rules
        $this->command->info('📋 Seeding Expense Request Posting Rules...');
        $this->call(NewExpenseRequestPostingRulesSeeder::class);

        // 4. Seed Invoice Posting Rules
        $this->command->info('📄 Seeding Invoice Posting Rules...');
        $this->call(NewInvoicePostingRulesSeeder::class);

        // 5. Seed Payment Posting Rules
        $this->command->info('💳 Seeding Payment Posting Rules...');
        $this->call(PaymentPostingRulesSeeder::class);

        // 6. Seed Payment Methods
        $this->command->info('🏦 Seeding Payment Methods...');
        $this->call(PaymentMethodSeeder::class);

        $this->command->info('✅ All Posting Rules Seeding Completed Successfully!');
        $this->command->info('');
        $this->command->info('📊 Summary:');
        $this->command->info('   - Chart of Accounts: 98 accounts (updated with specific banks)');
        $this->command->info('   - Expense Categories: 19 categories (added commission & uang jalan)');
        $this->command->info('   - Expense Request Rules: 19 rules');
        $this->command->info('   - Invoice Rules: 6 rules');
        $this->command->info('   - Payment Rules: 5 rules');
        $this->command->info('   - Payment Methods: 7 methods (with real account numbers)');
        $this->command->info('   - Total Posting Rules: 30 rules');
        $this->command->info('');
        $this->command->info('🎯 Ready for auto-posting transactions!');
    }
}
