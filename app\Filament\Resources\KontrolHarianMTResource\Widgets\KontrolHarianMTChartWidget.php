<?php

namespace App\Filament\Resources\KontrolHarianMTResource\Widgets;

use App\Models\KontrolHarianMT;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class KontrolHarianMTChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Trend Kontrol Harian MT (30 Hari Terakhir)';
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        // Get last 30 days data
        $endDate = Carbon::now();
        $startDate = Carbon::now()->subDays(29);

        $data = [];
        $labels = [];
        $safetyScores = [];

        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayData = KontrolHarianMT::whereDate('tanggal_kontrol', $date)->get();
            $count = $dayData->count();

            // Calculate safety score for the day
            $totalInspections = $count;
            $passedInspections = 0;

            foreach ($dayData as $inspection) {
                $inspectionItems = [
                    'perlengkapan_bongkar_muatan',
                    'ban_serap_ada',
                    'lampu_rotari_menyala',
                    'apar_ada',
                    'surat_kendaraan_dibawa',
                    'awak_mt_sehat',
                    'awak_mt_safety',
                    'kelengkapan_surat_awak_ready',
                ];

                $passed = 0;
                foreach ($inspectionItems as $item) {
                    if ($inspection->$item) {
                        $passed++;
                    }
                }

                if ($passed >= 6) { // 75% pass rate
                    $passedInspections++;
                }
            }

            $safetyScore = $totalInspections > 0 ? round(($passedInspections / $totalInspections) * 100, 1) : 0;

            $labels[] = $date->format('d/m');
            $data[] = $count;
            $safetyScores[] = $safetyScore;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Kontrol',
                    'data' => $data,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Skor Keselamatan (%)',
                    'data' => $safetyScores,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Jumlah Kontrol',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Skor Keselamatan (%)',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'min' => 0,
                    'max' => 100,
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
        ];
    }
}
