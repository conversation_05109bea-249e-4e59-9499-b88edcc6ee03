# Multiple Seals System for Delivery Orders

## Overview

Sistem nomor segel telah diubah dari single input menjadi multiple input untuk mendukung penggunaan beberapa segel pada satu delivery order. Perubahan ini memungkinkan pencatatan yang lebih detail dan akurat untuk setiap jenis segel yang digunakan.

## Perubahan yang Dilakukan

### 1. Database Structure

#### Tabel Baru: `delivery_order_seals`
- **id**: Primary key
- **id_delivery_order**: Foreign key ke delivery_order
- **nomor_segel**: Nomor segel (max 100 karakter)
- **jenis_segel**: Enum ('atas', 'bawah', 'samping', 'lainnya')
- **keterangan**: Keterangan tambahan (opsional)
- **urutan**: Urutan segel (1-99)
- **created_by**: User yang membuat
- **timestamps & soft deletes**

#### Constraints & Indexes
- Unique constraint: `(id_delivery_order, nomor_segel)` - mencegah duplikasi
- Indexes pada: `id_delivery_order`, `jenis_segel`, `nomor_segel`
- Foreign keys dengan cascade delete

### 2. Models

#### Model Baru: `DeliveryOrderSeal`
```php
// Jenis segel yang tersedia
public const JENIS_SEGEL = [
    'atas' => 'Segel Atas',
    'bawah' => 'Segel Bawah', 
    'samping' => 'Segel Samping',
    'lainnya' => 'Lainnya',
];

// Helper methods
public function getJenisSegalLabel(): string
public static function getNextUrutan(int $deliveryOrderId): int
public static function createMultiple(int $deliveryOrderId, array $seals, ?int $createdBy = null): void
```

#### Update Model `DeliveryOrder`
```php
// Relasi baru
public function seals(): HasMany

// Helper methods
public function getSealNumbersAttribute(): string  // "SGL-001, SGL-002"
public function getSealsByType(): array           // ['atas' => ['SGL-001'], 'bawah' => ['SGL-002']]
public function addSeals(array $seals, ?int $createdBy = null): void
```

### 3. User Interface Changes

#### DeliveryOrderResource Form
- **Sebelum**: Single TextInput untuk `no_segel`
- **Sesudah**: Repeater dengan schema:
  - Grid 3 kolom: `nomor_segel`, `jenis_segel`, `urutan`
  - Textarea untuk `keterangan`
  - Reorderable & collapsible
  - Item label menampilkan nomor segel

#### Table Columns
- **DeliveryOrderResource**: Kolom `seal_numbers` menampilkan semua segel (comma-separated)
- **FuelDeliveryDashboard**: Update kolom `seal_numbers` dengan format yang sama

#### Driver Delivery Detail
- Menampilkan semua segel dengan badge jenis segel
- Layout: nomor segel di kiri, badge jenis di kanan
- Conditional display (hanya tampil jika ada segel)

### 4. Data Migration

#### Migration `migrate_existing_seals_data`
- Otomatis memindahkan data dari kolom `no_segel` lama
- Memisahkan multiple seals yang dipisah dengan koma/semicolon/pipe
- Default jenis segel: 'atas'
- Rollback support untuk mengembalikan data

### 5. Features

#### Input Features
- **Multiple seals per DO**: Tidak terbatas jumlahnya
- **Jenis segel**: 4 pilihan (atas, bawah, samping, lainnya)
- **Urutan otomatis**: Auto-increment berdasarkan existing seals
- **Keterangan opsional**: Untuk detail tambahan
- **Reorderable**: Drag & drop untuk mengubah urutan
- **Validation**: Unique nomor segel per DO

#### Display Features
- **Compact view**: Comma-separated dalam table
- **Detailed view**: Badge dengan jenis segel di detail page
- **Search support**: Dapat dicari berdasarkan nomor segel
- **Copy support**: Dapat di-copy dari table

### 6. File Structure

```
app/
├── Models/
│   ├── DeliveryOrderSeal.php (NEW)
│   └── DeliveryOrder.php (UPDATED)
├── Filament/
│   ├── Resources/
│   │   └── DeliveryOrderResource.php (UPDATED)
│   └── Pages/
│       └── FuelDeliveryDashboard.php (UPDATED)

database/
├── migrations/
│   ├── 2025_07_28_000002_create_delivery_order_seals_table.php (NEW)
│   └── 2025_07_28_000003_migrate_existing_seals_data.php (NEW)
└── seeders/
    └── DeliveryOrderSealSeeder.php (NEW)

resources/views/filament/pages/
└── driver-delivery-detail.blade.php (UPDATED)
```

## Usage Examples

### 1. Creating Multiple Seals via Form
```php
// Dalam DeliveryOrderResource form, user dapat:
// 1. Klik "Tambah Segel"
// 2. Isi nomor segel: "SGL-001"
// 3. Pilih jenis: "Segel Atas"
// 4. Urutan: auto-filled
// 5. Keterangan: "Segel tangki utama"
// 6. Repeat untuk segel lainnya
```

### 2. Programmatic Creation
```php
// Menggunakan helper method
$deliveryOrder->addSeals([
    ['nomor_segel' => 'SGL-001', 'jenis_segel' => 'atas'],
    ['nomor_segel' => 'SGL-002', 'jenis_segel' => 'bawah'],
]);

// Atau langsung create
DeliveryOrderSeal::createMultiple($doId, $sealsArray, $userId);
```

### 3. Displaying Seals
```php
// Dalam table (compact)
$record->seal_numbers  // "SGL-001, SGL-002, SGL-003"

// Dalam detail (grouped)
$record->getSealsByType()  // ['atas' => ['SGL-001'], 'bawah' => ['SGL-002']]

// Individual seals
foreach ($record->seals as $seal) {
    echo $seal->nomor_segel . ' (' . $seal->getJenisSegalLabel() . ')';
}
```

## Benefits

1. **Akurasi Data**: Pencatatan yang lebih detail untuk setiap jenis segel
2. **Fleksibilitas**: Mendukung berbagai konfigurasi segel
3. **Traceability**: Tracking yang lebih baik untuk audit
4. **User Experience**: Interface yang intuitif dengan drag & drop
5. **Backward Compatibility**: Data lama otomatis dimigrasikan

## Testing

Jalankan seeder untuk testing:
```bash
php artisan db:seed --class=DeliveryOrderSealSeeder
```

Seeder akan membuat:
- DO pertama: 1 segel atas
- DO kedua: 3 segel berbeda jenis (atas, bawah, samping)
- DO ketiga: 4 segel atas (kompartemen berbeda)

## Migration Notes

- **Safe Migration**: Data existing otomatis dipindahkan
- **Rollback Support**: Dapat dikembalikan jika diperlukan
- **No Data Loss**: Semua data nomor segel lama tetap tersimpan
- **Validation**: Unique constraint mencegah duplikasi
