<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Adds message template fields to notification_settings table.
     */
    public function up(): void
    {
        Schema::table('notification_settings', function (Blueprint $table) {
            $table->text('message_template')
                ->nullable()
                ->after('is_active')
                ->comment('Template pesan dengan placeholder variables');
            
            $table->json('template_variables')
                ->nullable()
                ->after('message_template')
                ->comment('Available variables untuk template (JSON format)');
            
            $table->text('message_preview')
                ->nullable()
                ->after('template_variables')
                ->comment('Preview pesan dengan sample data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notification_settings', function (Blueprint $table) {
            $table->dropColumn(['message_template', 'template_variables', 'message_preview']);
        });
    }
};
