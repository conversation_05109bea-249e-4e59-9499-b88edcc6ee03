{"compilerOptions": {"target": "es5", "lib": ["es5", "dom"], "types": ["cypress", "node"], "moduleResolution": "node", "allowJs": true, "noEmit": true, "strict": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.js", "**/*.cy.ts", "**/*.cy.js"], "exclude": ["node_modules"]}