<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('aset', function (Blueprint $table) {
            $table->id();
            $table->string('kode_aset');
            $table->string('nama_aset');
            $table->text('deskripsi_aset')->nullable();
            $table->string('kategori_aset', 50);
            $table->decimal('nilai_aset', 10, 2);

            $table->boolean('is_depreciable')
                ->default(true)
                ->comment('Apakah asset dapat didepresiasi');

            $table->integer('useful_life_months')
                ->default(5)
                ->comment('Masa manfaat dalam bulan');

            $table->decimal('salvage_value', 15, 2)
                ->default(0)
                ->comment('Nilai sisa/residu');

            $table->date('depreciation_start_date')
                ->nullable()
                ->comment('Tanggal mulai depresiasi');

            $table->date('last_depreciation_date')
                ->nullable()
                ->comment('Tanggal depresiasi terakhir');

            $table->text('depreciation_notes')
                ->nullable()
                ->comment('Catatan depresiasi');

            $table->dateTime('tanggal_akuisisi');
            $table->unsignedBigInteger('id_akun_aset')->nullable();
            $table->unsignedBigInteger('id_akun_depresiasi')->nullable();
            $table->unsignedBigInteger('id_akun_akuisisi')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aset');
    }
};
