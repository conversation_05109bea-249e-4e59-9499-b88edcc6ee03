<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KontrolHarianMTResource\Pages;
use App\Filament\Resources\KontrolHarianMTResource\RelationManagers;
use App\Models\KontrolHarianMT;
use App\Models\Kendaraan;
use App\Models\User;
use App\Models\Jabatan;
use App\Models\Item;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class KontrolHarianMTResource extends Resource
{
    protected static ?string $model = KontrolHarianMT::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';
    protected static ?string $navigationLabel = 'Kontrol Harian MT';
    protected static ?string $modelLabel = 'Kontrol Harian MT';
    protected static ?string $pluralModelLabel = 'Kontrol Harian MT';
    protected static ?string $navigationGroup = 'Security';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\DatePicker::make('tanggal_kontrol')
                            ->label('Tanggal Kontrol')
                            ->required()
                            ->default(now())
                            ->maxDate(now()),

                        Forms\Components\Select::make('plat_kendaraan')
                            ->label('Plat Kendaraan')
                            ->required()
                            ->options(function () {
                                return Kendaraan::query()
                                    ->whereNotNull('no_pol_kendaraan')
                                    ->pluck('no_pol_kendaraan', 'no_pol_kendaraan')
                                    ->toArray();
                            })
                            ->searchable()
                            ->placeholder('Pilih plat kendaraan'),

                        Forms\Components\Select::make('awak_mobil_tangki')
                            ->label('Awak Mobil Tangki')
                            ->required()
                            ->options(function () {
                                return User::query()
                                    ->whereHas('jabatan', function ($query) {
                                        $query->where('nama', 'like', '%driver%')
                                            ->orWhere('nama', 'like', '%Driver%')
                                            ->orWhere('nama', 'like', '%DRIVER%');
                                    })
                                    ->where('is_active', true)
                                    ->pluck('name', 'name')
                                    ->toArray();
                            })
                            ->searchable()
                            ->placeholder('Pilih awak mobil tangki'),

                        Forms\Components\Select::make('petugas_sekuriti')
                            ->label('Petugas Sekuriti')
                            ->required()
                            ->options(function () {
                                return User::query()
                                    ->whereHas('jabatan', function ($query) {
                                        $query->where('nama', 'like', '%security%')
                                            ->orWhere('nama', 'like', '%Security%')
                                            ->orWhere('nama', 'like', '%SECURITY%')
                                            ->orWhere('nama', 'like', '%sekuriti%')
                                            ->orWhere('nama', 'like', '%Sekuriti%')
                                            ->orWhere('nama', 'like', '%SEKURITI%');
                                    })
                                    ->where('is_active', true)
                                    ->pluck('name', 'name')
                                    ->toArray();
                            })
                            ->searchable()
                            ->placeholder('Pilih petugas sekuriti'),

                        Forms\Components\TextInput::make('tujuan_mt')
                            ->label('Tujuan MT')
                            ->required()
                            ->placeholder('Tujuan mobil tangki'),

                        Forms\Components\TimePicker::make('jam_keberangkatan')
                            ->label('Jam Keberangkatan')
                            ->seconds(false),

                        Forms\Components\TimePicker::make('jam_pulang')
                            ->label('Jam Pulang')
                            ->seconds(false),

                        Forms\Components\Select::make('jenis_muatan')
                            ->label('Jenis Muatan')
                            ->options(function () {
                                return Item::query()
                                    ->whereNotNull('name')
                                    ->pluck('name', 'name')
                                    ->toArray();
                            })
                            ->searchable()
                            ->placeholder('Pilih jenis muatan'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Pre Trip Inspection MT')
                    ->description('Checklist pemeriksaan sebelum perjalanan')
                    ->schema([
                        Forms\Components\Checkbox::make('perlengkapan_bongkar_muatan')
                            ->label('Apakah perlengkapan bongkar muatan (Robin, selang dll) sudah ada?')
                            ->default(false),

                        Forms\Components\Checkbox::make('ban_serap_ada')
                            ->label('Apakah Ban serap pada mobil Tangki ada?')
                            ->default(false),

                        Forms\Components\Checkbox::make('lampu_rotari_menyala')
                            ->label('Apakah Lampu rotari pada mobil tangki menyala dengan baik?')
                            ->default(false),

                        Forms\Components\Checkbox::make('apar_ada')
                            ->label('Apakah Apar pada Mobil Tangki (di luar dan di dalam mobil tangki) ada?')
                            ->default(false),

                        Forms\Components\Radio::make('kondisi_kebersihan')
                            ->label('Bagaimana kondisi kebersihan dan kesiapan mobil tangki?')
                            ->options([
                                'bersih' => 'A. Bersih',
                                'kurang_bersih' => 'B. Kurang bersih',
                            ])
                            ->inline(),

                        Forms\Components\Checkbox::make('surat_kendaraan_dibawa')
                            ->label('Tanyakan kepada awak Mobil tangki, apakah surat-surat kendaraan sudah dibawa? (STNK, KIR, TERA, BO, dan surat jalan)')
                            ->default(false),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('AMT Inspection')
                    ->description('Checklist pemeriksaan awak mobil tangki')
                    ->schema([
                        Forms\Components\Checkbox::make('awak_mt_sehat')
                            ->label('Apakah awak mobil tangki dalam keadaan sehat?')
                            ->default(false),

                        Forms\Components\Checkbox::make('awak_mt_safety')
                            ->label('Apakah awak mobil tangki sudah dalam keadaan Safety? (Seragam, sepatu, sarung tangan dan helm)')
                            ->default(false),

                        Forms\Components\Checkbox::make('kelengkapan_surat_awak_ready')
                            ->label('Apakah kelengkapan surat awak mobil tangki sudah ready? (Badge id depot, HsSe depot)')
                            ->default(false),
                    ])
                    ->columns(1),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_kontrol')
                    ->label('Tanggal Kontrol')
                    ->date('d M Y')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('plat_kendaraan')
                    ->label('Plat Kendaraan')
                    ->sortable()
                    ->searchable()
                    ->description(
                        fn(KontrolHarianMT $record): ?string =>
                        $record->kendaraan?->merk . ' ' . $record->kendaraan?->tipe
                    ),

                Tables\Columns\TextColumn::make('awak_mobil_tangki')
                    ->label('Awak MT')
                    ->sortable()
                    ->searchable()
                    ->description(
                        fn(KontrolHarianMT $record): ?string =>
                        $record->awakMobilTangki?->jabatan?->nama
                    ),

                Tables\Columns\TextColumn::make('jenis_muatan')
                    ->label('Jenis Muatan')
                    ->badge()
                    ->color(fn(string $state): string => match (true) {
                        str_contains(strtolower($state), 'solar') => 'success',
                        str_contains(strtolower($state), 'pertalite') => 'info',
                        str_contains(strtolower($state), 'pertamax') => 'warning',
                        str_contains(strtolower($state), 'dex') => 'primary',
                        str_contains(strtolower($state), 'premium') => 'danger',
                        default => 'secondary',
                    }),

                Tables\Columns\TextColumn::make('jam_keberangkatan')
                    ->label('Jam Berangkat')
                    ->time('H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('jam_pulang')
                    ->label('Jam Pulang')
                    ->time('H:i')
                    ->sortable(),


                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('tanggal_kontrol')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_kontrol', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_kontrol', '<=', $date),
                            );
                    }),

                Tables\Filters\SelectFilter::make('jenis_muatan')
                    ->label('Jenis Muatan')
                    ->options(function () {
                        return Item::query()
                            ->whereNotNull('name')
                            ->pluck('name', 'name')
                            ->toArray();
                    }),

                Tables\Filters\SelectFilter::make('kondisi_kebersihan')
                    ->label('Kondisi Kebersihan')
                    ->options([
                        'bersih' => 'Bersih',
                        'kurang_bersih' => 'Kurang Bersih',
                    ]),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('tanggal_kontrol', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKontrolHarianMTS::route('/'),
            'create' => Pages\CreateKontrolHarianMT::route('/create'),
            'edit' => Pages\EditKontrolHarianMT::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
