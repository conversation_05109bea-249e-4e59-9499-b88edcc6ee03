@php
    $record = $getRecord();
    $kendaraan = $record->kendaraan;
@endphp

@if($kendaraan)
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4">
        {{-- Header dengan <PERSON> --}}
        <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-3">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ $kendaraan->no_pol_kendaraan }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $kendaraan->merk }} {{ $kendaraan->tipe }}</p>
                </div>
            </div>
            <div>
                @php
                    $statusColor = match($kendaraan->status_kendaraan) {
                        'aktif' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                        'maintenance' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                        'non_aktif' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                        default => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    };
                    $statusLabel = match($kendaraan->status_kendaraan) {
                        'aktif' => 'Aktif',
                        'maintenance' => 'Maintenance',
                        'non_aktif' => 'Non Aktif',
                        default => ucfirst($kendaraan->status_kendaraan)
                    };
                @endphp
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColor }}">
                    {{ $statusLabel }}
                </span>
            </div>
        </div>

        {{-- Detail Grid --}}
        {{-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Tahun Pembuatan</p>
                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ $kendaraan->tahun_pembuatan }}</p>
                </div>
            </div>

            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Warna</p>
                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ $kendaraan->warna }}</p>
                </div>
            </div>
        </div> --}}

        {{-- Nomor Rangka & Mesin --}}
        {{-- <div class="space-y-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Nomor Rangka</p>
                        <p class="text-sm text-gray-900 dark:text-gray-100 font-mono">{{ $kendaraan->no_rangka }}</p>
                    </div>
                </div>
                <button
                    onclick="navigator.clipboard.writeText('{{ $kendaraan->no_rangka }}');
                             this.querySelector('.copy-text').textContent = 'Disalin!';
                             setTimeout(() => this.querySelector('.copy-text').textContent = 'Salin', 2000)"
                    class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span class="copy-text">Salin</span>
                </button>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Nomor Mesin</p>
                        <p class="text-sm text-gray-900 dark:text-gray-100 font-mono">{{ $kendaraan->no_mesin }}</p>
                    </div>
                </div>
                <button
                    onclick="navigator.clipboard.writeText('{{ $kendaraan->no_mesin }}');
                             this.querySelector('.copy-text').textContent = 'Disalin!';
                             setTimeout(() => this.querySelector('.copy-text').textContent = 'Salin', 2000)"
                    class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span class="copy-text">Salin</span>
                </button>
            </div>
        </div> --}}
    </div>
@else
    <div class="text-gray-500 dark:text-gray-400 italic text-center py-4">
        Tidak ada kendaraan terkait dengan expense request ini.
    </div>
@endif
