<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class NewExpenseRequestPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Delete existing expense request posting rules
        PostingRule::where('source_type', 'ExpenseRequest')->delete();

        $this->createExpenseRequestPostingRules();
    }

    private function createExpenseRequestPostingRules()
    {
        // Get specific accounts based on new COA structure
        $accounts = [
            'vehicle_maintenance' => Akun::where('kode_akun', '608.3')->first(), // Beban Maintanance Kendaraan Service
            'fuel' => Akun::where('kode_akun', '612')->first(), // BBM/Tol Operasional
            'vehicle_tax' => Akun::where('kode_akun', '619')->first(), // Lisensi
            'business_travel' => Akun::where('kode_akun', '610')->first(), // Perjalanan Dinas
            'transportation' => Akun::where('kode_akun', '613')->first(), // Beban Jasa Transport
            'office_utilities' => Akun::where('kode_akun', '606')->first(), // Beban Listrik
            'office_supplies' => Akun::where('kode_akun', '604.1')->first(), // ATK Kantor
            'license' => Akun::where('kode_akun', '619')->first(), // Lisensi
            'legal' => Akun::where('kode_akun', '624')->first(), // Jasa Konsultasi
            'equipment_maintenance' => Akun::where('kode_akun', '609')->first(), // Beban Maintanance Inventaris
            'communication' => Akun::where('kode_akun', '607')->first(), // Beban Telepon
            'marketing' => Akun::where('kode_akun', '615')->first(), // Beban Marketing
            'training' => Akun::where('kode_akun', '617')->first(), // Beban Operasional
            'consumption' => Akun::where('kode_akun', '614')->first(), // Konsumsi
            'miscellaneous' => Akun::where('kode_akun', '699')->first(), // Beban Lain-lain
            'commission_fee' => Akun::where('kode_akun', '611')->first(), // Fee
            'hpp_jasa_angkut' => Akun::where('kode_akun', '506')->first(), // HPP Jasa Angkut APMS
        ];



        // Fallback account
        $fallbackAccount = Akun::where('kategori_akun', 'Beban')->first();

        if (!$fallbackAccount) {
            $this->command->error('No expense accounts found. Please run CoaSeeder first.');
            return;
        }

        // Create posting rules for each expense category
        $this->createVehicleExpenseRules($accounts, $fallbackAccount);
        $this->createBusinessTravelRules($accounts, $fallbackAccount);
        $this->createOfficeExpenseRules($accounts, $fallbackAccount);
        $this->createLegalExpenseRules($accounts, $fallbackAccount);
        $this->createMaintenanceRules($accounts, $fallbackAccount);
        $this->createCommunicationMarketingRules($accounts, $fallbackAccount);
        $this->createTrainingMiscRules($accounts, $fallbackAccount);
        $this->createCommissionOperationalRules($accounts, $fallbackAccount);

        $this->command->info('✅ Expense Request Posting Rules created successfully!');
    }

    private function createVehicleExpenseRules($accounts, $fallbackAccount)
    {
        // 1. Ganti Oli Kendaraan
        $this->createPostingRule(
            'Expense Request - Ganti Oli Kendaraan',
            ['category' => 'ganti_oli'],
            'Auto posting untuk expense request ganti oli kendaraan',
            $accounts['vehicle_maintenance'] ?? $fallbackAccount,
            1
        );

        // 2. Perawatan Kendaraan
        $this->createPostingRule(
            'Expense Request - Perawatan Kendaraan',
            ['category' => 'vehicle_maintenance'],
            'Auto posting untuk expense request perawatan kendaraan',
            $accounts['vehicle_maintenance'] ?? $fallbackAccount,
            2
        );

        // 3. Bahan Bakar Kendaraan
        $this->createPostingRule(
            'Expense Request - Bahan Bakar Kendaraan',
            ['category' => 'vehicle_fuel'],
            'Auto posting untuk expense request bahan bakar kendaraan',
            $accounts['fuel'] ?? $fallbackAccount,
            3
        );

        // 4. Pajak dan STNK Kendaraan
        $this->createPostingRule(
            'Expense Request - Pajak dan STNK Kendaraan',
            ['category' => 'vehicle_tax'],
            'Auto posting untuk expense request pajak dan STNK kendaraan',
            $accounts['vehicle_tax'] ?? $fallbackAccount,
            4
        );

        // 5. Reimburs Delivery Order
        $this->createPostingRule(
            'Expense Request - Reimburs Delivery Order',
            ['category' => 'delivery_reimbursement'],
            'Auto posting untuk expense request reimburs delivery order',
            $accounts['vehicle_maintenance'] ?? $fallbackAccount,
            5
        );
    }

    private function createBusinessTravelRules($accounts, $fallbackAccount)
    {
        // 6. Perjalanan Dinas
        $this->createPostingRule(
            'Expense Request - Perjalanan Dinas',
            ['category' => 'business_travel'],
            'Auto posting untuk expense request perjalanan dinas',
            $accounts['business_travel'] ?? $fallbackAccount,
            6
        );

        // 7. Transportasi
        $this->createPostingRule(
            'Expense Request - Transportasi',
            ['category' => 'transportation'],
            'Auto posting untuk expense request transportasi',
            $accounts['transportation'] ?? $fallbackAccount,
            7
        );
    }

    private function createOfficeExpenseRules($accounts, $fallbackAccount)
    {
        // 8. Utilitas Kantor
        $this->createPostingRule(
            'Expense Request - Utilitas Kantor',
            ['category' => 'office_utilities'],
            'Auto posting untuk expense request utilitas kantor',
            $accounts['office_utilities'] ?? $fallbackAccount,
            8
        );

        // 9. Perlengkapan Kantor
        $this->createPostingRule(
            'Expense Request - Perlengkapan Kantor',
            ['category' => 'office_supplies'],
            'Auto posting untuk expense request perlengkapan kantor',
            $accounts['office_supplies'] ?? $fallbackAccount,
            9
        );
    }

    private function createLegalExpenseRules($accounts, $fallbackAccount)
    {
        // 10. Biaya Lisensi
        $this->createPostingRule(
            'Expense Request - Biaya Lisensi',
            ['category' => 'license_fee'],
            'Auto posting untuk expense request biaya lisensi',
            $accounts['license'] ?? $fallbackAccount,
            10
        );

        // 11. Biaya Legal
        $this->createPostingRule(
            'Expense Request - Biaya Legal',
            ['category' => 'legal_fee'],
            'Auto posting untuk expense request biaya legal',
            $accounts['legal'] ?? $fallbackAccount,
            11
        );
    }

    private function createMaintenanceRules($accounts, $fallbackAccount)
    {
        // 12. Perawatan Peralatan
        $this->createPostingRule(
            'Expense Request - Perawatan Peralatan',
            ['category' => 'equipment_maintenance'],
            'Auto posting untuk expense request perawatan peralatan',
            $accounts['equipment_maintenance'] ?? $fallbackAccount,
            12
        );
    }

    private function createCommunicationMarketingRules($accounts, $fallbackAccount)
    {
        // 13. Komunikasi
        $this->createPostingRule(
            'Expense Request - Komunikasi',
            ['category' => 'communication'],
            'Auto posting untuk expense request komunikasi',
            $accounts['communication'] ?? $fallbackAccount,
            13
        );

        // 14. Promosi dan Marketing
        $this->createPostingRule(
            'Expense Request - Promosi dan Marketing',
            ['category' => 'marketing'],
            'Auto posting untuk expense request promosi dan marketing',
            $accounts['marketing'] ?? $fallbackAccount,
            14
        );
    }

    private function createTrainingMiscRules($accounts, $fallbackAccount)
    {
        // 15. Pelatihan dan Pengembangan
        $this->createPostingRule(
            'Expense Request - Pelatihan dan Pengembangan',
            ['category' => 'training'],
            'Auto posting untuk expense request pelatihan dan pengembangan',
            $accounts['training'] ?? $fallbackAccount,
            15
        );

        // 16. Konsumsi
        $this->createPostingRule(
            'Expense Request - Konsumsi',
            ['category' => 'consumption'],
            'Auto posting untuk expense request konsumsi',
            $accounts['consumption'] ?? $fallbackAccount,
            16
        );

        // 17. Lain-lain
        $this->createPostingRule(
            'Expense Request - Lain-lain',
            ['category' => 'miscellaneous'],
            'Auto posting untuk expense request lain-lain',
            $accounts['miscellaneous'] ?? $fallbackAccount,
            17
        );
    }

    private function createPostingRule($ruleName, $triggerCondition, $description, $expenseAccount, $priority)
    {
        $rule = PostingRule::create([
            'rule_name' => $ruleName,
            'source_type' => 'ExpenseRequest',
            'trigger_condition' => $triggerCondition,
            'description' => $description,
            'is_active' => true,
            'priority' => $priority,
            'created_by' => 1,
        ]);

        // Debit: Expense Account
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $expenseAccount->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'approved_amount',
            'description_template' => $description . ' - {source.request_number}',
            'sort_order' => 1,
        ]);

        // Credit: Payment Account (will be updated when payment method is selected)
        // For now, use Cash as default
        $cashAccount = Akun::where('kode_akun', '100')->first();
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $cashAccount->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'approved_amount',
            'description_template' => 'Pembayaran ' . strtolower($description) . ' - {source.request_number}',
            'sort_order' => 2,
        ]);

        return $rule;
    }

    private function createCommissionOperationalRules($accounts, $fallbackAccount)
    {
        // 18. Biaya Komisi
        $this->createPostingRule(
            'Expense Request - Biaya Komisi',
            ['category' => 'commission_fee'],
            'Auto posting untuk expense request biaya komisi',
            $accounts['commission_fee'] ?? $fallbackAccount,
            18
        );

        // 19. Uang Jalan SC
        $this->createPostingRule(
            'Expense Request - Uang Jalan SC',
            ['category' => 'uang_jalan_sc'],
            'Auto posting untuk expense request uang jalan SC',
            $accounts['hpp_jasa_angkut'] ?? $fallbackAccount,
            19
        );
    }
}
