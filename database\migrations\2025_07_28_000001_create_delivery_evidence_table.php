<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_evidence', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_pengiriman_driver');
            $table->enum('evidence_type', [
                'foto_pemutusan_segel_atas',
                'foto_pemutusan_segel_bawah', 
                'foto_tera',
                'foto_sample_bbm',
                'foto_tangki_mt_kosong',
                'foto_mt',
                'foto_pembongkaran'
            ]);
            $table->string('file_name');
            $table->string('file_path');
            $table->string('original_name');
            $table->string('mime_type');
            $table->unsignedBigInteger('file_size');
            $table->text('description')->nullable();
            $table->integer('sequence_number')->default(1)->comment('For multiple photos of same type');
            $table->unsignedBigInteger('uploaded_by');
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('id_pengiriman_driver')->references('id')->on('pengiriman_driver')->onDelete('cascade');
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index(['id_pengiriman_driver', 'evidence_type']);
            $table->index(['evidence_type']);
            $table->index(['uploaded_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_evidence');
    }
};
