<x-filament-panels::page>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter Driver & Fleet Management</h3>
            {{ $this->form }}
        </div>

        <!-- Fleet KPI Cards -->
        @php
            $kpiData = $this->getFleetKpiData();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Trips -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 border-2 border-blue-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.3), 0 4px 6px -2px rgba(59, 130, 246, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-truck class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Total Trips
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['total_trips']) }}
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    {{ number_format($kpiData['completed_trips']) }} completed
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fleet Utilization -->
            <div class="bg-gradient-to-r from-green-600 to-green-700 border-2 border-green-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(34, 197, 94, 0.3), 0 4px 6px -2px rgba(34, 197, 94, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-chart-pie class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Fleet Utilization
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ $kpiData['fleet_utilization'] }}%
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    {{ $kpiData['active_vehicles'] }}/{{ $kpiData['total_vehicles'] }} vehicles
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- On-Time Rate -->
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 border-2 border-purple-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(147, 51, 234, 0.3), 0 4px 6px -2px rgba(147, 51, 234, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clock class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    On-Time Rate
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ $kpiData['on_time_rate'] }}%
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    {{ number_format($kpiData['on_time_trips']) }} on-time trips
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Drivers -->
            <div class="bg-gradient-to-r from-orange-600 to-orange-700 border-2 border-orange-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-users class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Active Drivers
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['active_drivers']) }}
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    Avg {{ $kpiData['avg_delivery_time'] }} hrs/trip
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Completion Rate -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-check-circle class="h-6 w-6 text-green-500 dark:text-green-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Completion Rate
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $kpiData['completion_rate'] }}%
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Volume Delivered -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-cube class="h-6 w-6 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Volume Delivered
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['total_volume_delivered'], 2) }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Avg Delivery Time -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clock class="h-6 w-6 text-yellow-500 dark:text-yellow-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Avg Delivery Time
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $kpiData['avg_delivery_time'] }} jam
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Driver Efficiency Trend -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Driver Efficiency Trend</h3>
                <div class="h-64">
                    <canvas id="driverEfficiencyChart"></canvas>
                </div>
            </div>

            <!-- Vehicle Performance Chart -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Vehicle Performance</h3>
                <div class="h-64">
                    <canvas id="vehiclePerformanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tables Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Top Drivers Table -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Top Drivers Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Driver
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Trips
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Rate
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Volume
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach (array_slice($this->getTopDriversData(), 0, 8) as $driver)
                                <tr>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $driver->driver_name }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                        {{ $driver->total_trips }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($driver->completion_rate >= 90) bg-green-100 text-green-800
                                            @elseif($driver->completion_rate >= 70) bg-yellow-100 text-yellow-800
                                            @else bg-red-100 text-red-800 @endif">
                                            {{ $driver->completion_rate }}%
                                        </span>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                        {{ number_format($driver->total_volume_delivered ?? 0, 1) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Maintenance Alerts Table -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Maintenance Alerts</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Vehicle
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Trips
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Days
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Status
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach (array_slice($this->getMaintenanceAlertsData(), 0, 8) as $alert)
                                <tr>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $alert['vehicle_plate'] }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                        {{ $alert['total_trips'] }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                        {{ $alert['days_since_maintenance'] }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($alert['alert_level'] === 'high') bg-red-100 text-red-800
                                            @elseif($alert['alert_level'] === 'medium') bg-yellow-100 text-yellow-800
                                            @else bg-green-100 text-green-800 @endif">
                                            {{ $alert['alert_message'] }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            let driverEfficiencyChart;
            let vehiclePerformanceChart;

            function initializeCharts() {
                // Driver Efficiency Trend Chart
                const efficiencyData = @json($this->getDriverEfficiencyTrendData());
                const efficiencyCtx = document.getElementById('driverEfficiencyChart').getContext('2d');
                
                if (driverEfficiencyChart) {
                    driverEfficiencyChart.destroy();
                }
                
                driverEfficiencyChart = new Chart(efficiencyCtx, {
                    type: 'line',
                    data: {
                        labels: efficiencyData.map(d => new Date(d.date).toLocaleDateString('id-ID')),
                        datasets: [{
                            label: 'Total Trips',
                            data: efficiencyData.map(d => d.total_trips),
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1
                        }, {
                            label: 'Completed Trips',
                            data: efficiencyData.map(d => d.completed_trips),
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1
                        }, {
                            label: 'On-Time Trips',
                            data: efficiencyData.map(d => d.on_time_trips),
                            borderColor: 'rgb(139, 92, 246)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Vehicle Performance Chart
                const vehicleData = @json(array_slice($this->getVehiclePerformanceData(), 0, 8));
                const vehicleCtx = document.getElementById('vehiclePerformanceChart').getContext('2d');
                
                if (vehiclePerformanceChart) {
                    vehiclePerformanceChart.destroy();
                }
                
                vehiclePerformanceChart = new Chart(vehicleCtx, {
                    type: 'bar',
                    data: {
                        labels: vehicleData.map(v => v.vehicle_plate),
                        datasets: [{
                            label: 'Utilization Rate (%)',
                            data: vehicleData.map(v => v.utilization_rate),
                            backgroundColor: 'rgba(34, 197, 94, 0.8)',
                            borderColor: 'rgb(34, 197, 94)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        }, {
                            label: 'Total Trips',
                            data: vehicleData.map(v => v.total_trips),
                            type: 'line',
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                max: 100
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
            }

            // Initialize charts on page load
            document.addEventListener('DOMContentLoaded', function() {
                initializeCharts();
            });

            // Listen for refresh events from Livewire
            document.addEventListener('livewire:init', () => {
                Livewire.on('refresh-charts', () => {
                    setTimeout(() => {
                        location.reload();
                    }, 100);
                });
            });
        </script>
    @endpush
</x-filament-panels::page>
