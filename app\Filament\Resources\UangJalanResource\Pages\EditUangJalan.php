<?php

namespace App\Filament\Resources\UangJalanResource\Pages;

use App\Models\UangJalan;
use App\Filament\Resources\UangJalanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditUangJalan extends EditRecord
{
    protected static string $resource = UangJalanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // lihat
            Actions\ViewAction::make(),
            Actions\Action::make('view_do')
                ->label('Lihat DO')
                ->icon('heroicon-o-document-text')
                ->color('primary')
                ->url(fn(UangJalan $record): string => $record->deliveryOrderUrl)
                ->visible(fn(UangJalan $record): bool => $record->deliveryOrderUrl !== null)
                ->openUrlInNewTab(false),
            Actions\DeleteAction::make(),
        ];
    }
}
