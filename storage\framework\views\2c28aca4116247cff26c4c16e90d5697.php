<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Tagihan Pola - <?php echo e($record->nomor_invoice); ?></title>
    
     <style>
        @page {
            size: A4;
            margin: 0.5in;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #000;
            padding: 20px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            position: relative;
        }

        .company-section {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            flex: 1;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e40af, #fbbf24);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            line-height: 1.1;
            flex-shrink: 0;
            overflow: hidden;
        }

        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .company-info {
            flex: 1;
            margin-left: 10px;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 3px;
        }

        .company-tagline {
            font-size: 12px;
            color: #000;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-services {
            font-size: 10px;
            color: #666;
            margin-bottom: 8px;
        }

        .header-right {
            width: 20px;
            height: 80px;
            background: #1e40af;
        }

        /* Invoice Title Section */
        .invoice-title-section {
            text-align: center;
            margin: 30px 0;
        }

        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
            text-decoration: underline;
        }

        .invoice-number {
            font-size: 12px;
            color: #000;
        }

        /* Customer Details Section */
        .customer-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .customer-left,
        .customer-right {
            width: 48%;
        }

        .detail-row {
            display: flex;
            margin-bottom: 4px;
            font-size: 11px;
        }

        .detail-label {
            width: 120px;
            color: #000;
            flex-shrink: 0;
        }

        .detail-colon {
            width: 15px;
            text-align: center;
            flex-shrink: 0;
        }

        .detail-value {
            flex: 1;
            color: #000;
        }

        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10px;
        }

        .items-table th {
            background-color: #fff;
            border: 1px solid #000;
            padding: 6px 4px;
            text-align: center;
            font-weight: bold;
            color: #000;
            font-size: 10px;
        }

        .items-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            text-align: center;
            font-size: 10px;
            color: #000;
            vertical-align: top;
        }

        .items-table .text-left {
            text-align: left;
        }

        .items-table .text-right {
            text-align: right;
        }

        .items-table .no-border-bottom {
            border-bottom: none;
        }

        .totals-row {
            background-color: #fff;
        }

        .totals-row td {
            font-weight: bold;
        }

        .final-total-row {
            background-color: #fff;
        }

        .final-total-row td {
            font-weight: bold;
            font-size: 12px;
        }

        /* Terbilang Section */
        .terbilang-section {
            margin: 15px 0;
            font-size: 11px;
            border: 1px solid #000;
            padding: 8px;
        }

        /* Payment Notes */
        .payment-notes {
            margin: 15px 0;
            font-size: 10px;
            line-height: 1.5;
        }

        /* Signature Section */
        .signature-section {
            display: flex;
            justify-content: flex-end;
            margin-top: 40px;
        }

        .signature-box {
            text-align: center;
            width: 200px;
        }

        .signature-location {
            margin-bottom: 10px;
            font-size: 11px;
        }

        .signature-space {
            height: 80px;
            margin: 20px 0;
        }

        .signature-name {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 3px;
        }

        .signature-title {
            font-size: 10px;
        }

        /* Footer Section */
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 10px;
        }

        .footer table {
            width: 100%;
            border-collapse: collapse;
        }

        .footer td {
            vertical-align: top;
            padding: 5px;
        }

        .footer .center {
            text-align: center;
        }

        .footer .right {
            text-align: right;
        }

        .footer p {
            margin: 2px 0;
            line-height: 1.3;
        }

        @media print {
            body {
                margin: 0;
                padding: 10px;
            }

            .header {
                page-break-inside: avoid;
            }

            .items-table {
                page-break-inside: avoid;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
       
         <div class="header">
            <div class="company-section">
                <div class="company-logo <?php echo e(empty($logoBase64) ? 'company-logo-fallback' : ''); ?>">
                    <?php if(!empty($logoBase64)): ?>
                        <img src="data:image/png;base64,<?php echo e($logoBase64); ?>" alt="Company Logo">
                    <?php else: ?>
                        LINTAS<br>RIAU<br>PRIMA
                    <?php endif; ?>
                </div>
                <div class="company-info">
                    <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                    <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                    <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Service</div>
                </div>
            </div>
            <div class="header-right"></div>
        </div>
         <!-- Invoice Title -->
        <div class="invoice-title-section">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number"><?php echo e($record->nomor_invoice); ?></div>
        </div>


        <!-- Tagihan Pola Notice -->
        <div class="tagihan-pola-note">
            <div class="note-title">Keterangan tambahan</div>
            <div><?php echo e($record->keterangan_jasa_angkut_periode); ?></div>
        </div>

        <!-- Customer & Invoice Details -->
                <!-- Customer Details -->
        <div class="customer-details">
            <div style="margin-bottom: 15px;">
                <div class="detail-row">
                    <span class="detail-label">Nama Pelanggan</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value"><?php echo e($record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A')); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Alamat Pelanggan</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value"><?php echo e($record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamatUtama?->alamat ?? 'N/A')); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">No Surat Pengantar Pengiriman Pertamina</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value"><?php echo e($record->transaksiPenjualan?->kode ?? 'nomor so'); ?></span>
                </div>
                <?php
                    $deliveryOrders = $record->transaksiPenjualan?->deliveryOrders ?? collect();
                    $doNumbers = $deliveryOrders->pluck('kode')->filter()->implode(', ');
                ?>
                <div class="detail-row">
                    <span class="detail-label">No Tanda Bukti</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value"><?php echo e($doNumbers ?: '15090, 15091'); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Pengiriman Barang</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">No PO</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value"><?php echo e($record->transaksiPenjualan?->nomor_po ?? ''); ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Tanggal PO</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value"><?php echo e($record->transaksiPenjualan?->tanggal ? $record->transaksiPenjualan->tanggal->format('Y-m-d') : ''); ?></span>
                </div>
            </div>
        </div>


        <!-- Items Section -->
        <div class="items-section">
            <div class="section-title">DETAIL TAGIHAN</div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 5%;">No</th>
                        <th style="width: 45%;">Deskripsi Layanan</th>
                        <th style="width: 10%;" class="text-center">Qty</th>
                        <th style="width: 10%;" class="text-center">Satuan</th>
                        <th style="width: 15%;" class="text-right">Total Amount</th>
                        <?php if($record->invoiceItems->where('include_ppn', true)->count() > 0): ?>
                        <th style="width: 15%;" class="text-right">Total + PPN</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $record->invoiceItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="text-center"><?php echo e($index + 1); ?></td>
                        <td><?php echo e($item->item_name); ?></td>
                        <td class="text-center"><?php echo e(number_format($item->quantity, 0)); ?></td>
                        <td class="text-center"><?php echo e($item->unit); ?></td>
                        <td class="text-right">Rp <?php echo e(number_format($item->total_amount, 0, ',', '.')); ?></td>
                        <?php if($record->invoiceItems->where('include_ppn', true)->count() > 0): ?>
                        <td class="text-right">
                            <?php if($item->include_ppn): ?>
                                Rp <?php echo e(number_format($item->total_amount + $item->ppn_amount, 0, ',', '.')); ?>

                            <?php else: ?>
                                Rp <?php echo e(number_format($item->total_amount, 0, ',', '.')); ?>

                            <?php endif; ?>
                        </td>
                        <?php endif; ?>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="summary-label">Subtotal:</td>
                    <td class="summary-value">Rp <?php echo e(number_format($record->subtotal, 0, ',', '.')); ?></td>
                </tr>
                <?php if($record->total_pajak > 0): ?>
                <tr>
                    <td class="summary-label">PPN (11%):</td>
                    <td class="summary-value">Rp <?php echo e(number_format($record->total_pajak, 0, ',', '.')); ?></td>
                </tr>
                <?php endif; ?>
                <tr class="total-row">
                    <td class="summary-label">TOTAL INVOICE:</td>
                    <td class="summary-value">Rp <?php echo e(number_format($record->total_invoice, 0, ',', '.')); ?></td>
                </tr>
            </table>
        </div>

         
        <div class="footer">
            <table>
                <tr>
                    <td style="width: 33%;" class="iso-logos">
                        <?php
                            $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
                            $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
                                ->whereIn('name', $isoNamesToDisplay)
                                ->get();
                        ?>

                        <?php $__currentLoopData = $isoCertifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $logoPath = public_path('storage/' . $cert->logo_path);
                            ?>
                            <?php if(file_exists($logoPath)): ?>
                                <img src="data:image/jpeg;base64,<?php echo e(base64_encode(file_get_contents($logoPath))); ?>"
                                    alt="<?php echo e($cert->name); ?>" style="height: 40px; margin-right: 10px;">
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </td>
                    <td style="width: 34%;" class="center">
                        <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                        <?php if($record->letterSetting): ?>
                            <p><?php echo e($record->letterSetting->address); ?></p>
                            <p><?php echo e($record->letterSetting->city); ?>, <?php echo e($record->letterSetting->province); ?>.
                                <?php echo e($record->letterSetting->postal_code); ?></p>
                        <?php else: ?>
                            <p>Jl. Mesjid Al Furqon No. 26</p>
                            <p>Pekanbaru, Riau. 28144</p>
                        <?php endif; ?>
                    </td>
                    <td style="width: 33%;" class="right">
                        <?php if($record->letterSetting): ?>
                            <p>Tel: <?php echo e($record->letterSetting->phone_number); ?></p>
                            <p>Email: <?php echo e($record->letterSetting->email); ?></p>
                            <p>Web: <?php echo e($record->letterSetting->website); ?></p>
                        <?php else: ?>
                            <p>Tel: 0761-22369</p>
                            <p>Email: <EMAIL></p>
                            <p>Web: www.lintasriauprima.com</p>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
<?php /**PATH D:\laragon\www\lrp\resources\views\pdf\invoice_tagihan_pola.blade.php ENDPATH**/ ?>