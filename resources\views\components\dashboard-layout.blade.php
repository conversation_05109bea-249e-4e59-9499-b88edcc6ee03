@props([
    'title' => 'Dashboard',
    'description' => null,
    'filters' => null,
    'showFilters' => true
])

<x-filament-panels::page>
    <!-- Include Dashboard Theme CSS and JS -->
    @push('styles')
        @vite(['resources/css/dashboard-theme.css'])
    @endpush

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        @vite(['resources/js/dashboard-theme.js'])
    @endpush

    <div class="dashboard-container">
        @if($showFilters && $filters)
            <!-- Filters Section -->
            <div class="dashboard-filter">
                <h3>{{ $title }}</h3>
                @if($description)
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{{ $description }}</p>
                @endif
                {{ $filters }}
            </div>
        @endif

        <!-- Main Content -->
        {{ $slot }}
    </div>
</x-filament-panels::page>
