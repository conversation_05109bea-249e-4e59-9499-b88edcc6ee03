<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExpenseCategoryResource\Pages;
use App\Filament\Resources\ExpenseCategoryResource\RelationManagers;
use App\Models\Akun;
use App\Models\ExpenseCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseCategoryResource extends Resource
{
    protected static ?string $model = ExpenseCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationLabel = 'Kategori Pengeluaran';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Detail Kategori Pengeluaran')
                    ->description('Atur detail kategori pengeluaran di sini.')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama')
                            ->required()
                            ->placeholder('Contoh: Biaya Truk')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('code')
                            ->label('Kode')
                            ->required()
                            ->unique(ExpenseCategory::class, 'code', ignoreRecord: true)
                            ->placeholder('Contoh: truck_fee')
                            ->maxLength(255),

                        Forms\Components\Select::make('default_account_id')
                            ->label('Akun Default')
                            ->options(Akun::where('kategori_akun', 'Beban')->pluck('nama_akun', 'id'))
                            ->required()
                            ->searchable()
                            ->placeholder('Pilih Akun Default')
                            ->preload(),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull()
                            ->nullable()
                            ->placeholder('Deskripsi tambahan tentang kategori pengeluaran ini')
                            ->maxLength(65535),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->helperText('Nonaktifkan jika kategori pengeluaran ini tidak lagi digunakan.')
                            ->default(true),

                        Forms\Components\Toggle::make('is_vehicle_expense')
                            ->label('Expense Kendaraan')
                            ->helperText('Aktifkan jika kategori ini memerlukan pemilihan kendaraan dalam permintaan expense.')
                            ->default(false),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Data Pengeluaran Tambahan')
                    ->description('Tambahkan data pengeluaran tambahan yang diperlukan.')
                    ->schema([
                        Forms\Components\KeyValue::make('attributes')
                            ->label('Data Pengeluaran Tambahan')
                            ->keyLabel('Nama')
                            ->valueLabel('Deskripsi')
                            ->addActionLabel('Tambah Atribut')
                            ->columnSpanFull()
                            ->nullable(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label('Nama')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('code')->label('Kode')->searchable()->sortable()->badge(),
                Tables\Columns\TextColumn::make('defaultAccount.nama_akun')->label('Akun Default')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('is_active')->badge()->label('Aktif')->sortable()->state(function ($record) {
                    return $record->is_active ? 'Aktif' : 'Tidak Aktif';
                })->color(
                    fn($state) => $state ? 'success' : 'danger'
                ),

                Tables\Columns\IconColumn::make('is_vehicle_expense')
                    ->label('Expense Kendaraan')
                    ->boolean()
                    ->sortable()
                    ->tooltip(fn($record) => $record->is_vehicle_expense ? 'Memerlukan pemilihan kendaraan' : 'Tidak memerlukan kendaraan'),
                Tables\Columns\TextColumn::make('created_at')->label('Dibuat Pada')->date()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenseCategories::route('/'),
            'create' => Pages\CreateExpenseCategory::route('/create'),
            'view' => Pages\ViewExpenseCategory::route('/{record}'),
            'edit' => Pages\EditExpenseCategory::route('/{record}/edit'),
        ];
    }
}
