<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\Pelanggan;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\Log;

class CustomerAnalyticsDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'Customer Analytics';
    protected static ?string $title = 'Dashboard Customer Analytics';
    protected static string $view = 'filament.pages.customer-analytics-dashboard';
    protected static ?int $navigationSort = 11;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedPeriod = null;

    #[Url(keep: true)]
    public ?string $selectedCustomerType = null;

    #[Url(keep: true)]
    public ?string $selectedTbbm = null;

    #[Url(keep: true)]
    public $startDate = null;

    #[Url(keep: true)]
    public $endDate = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('director') ?? false;
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';
        $this->startDate = now()->startOfYear()->format('Y-m-d');
        $this->endDate = now()->endOfYear()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'last_year' => 'Tahun Lalu',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedCustomerType')
                    ->label('Tipe Customer (Opsional)')
                    ->options([
                        'corporate' => 'Corporate',
                        'individual' => 'Individual',
                    ])
                    ->placeholder('Semua Tipe')
                    ->live(),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live(),
            ])
            ->columns(5);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            'last_year' => [
                $this->startDate = $now->subYear()->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getCustomerKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $baseQuery = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomerType) {
            $baseQuery->where('pelanggan.type', $this->selectedCustomerType);
        }

        if ($this->selectedTbbm) {
            $baseQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        // Total customers
        $totalCustomers = Pelanggan::when($this->selectedCustomerType, fn($q) => $q->where('type', $this->selectedCustomerType))->count();

        // Active customers
        $activeCustomers = (clone $baseQuery)->distinct('pelanggan.id')->count('pelanggan.id');

        // New customers (first transaction in period)
        $newCustomers = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->whereRaw('transaksi_penjualan.tanggal = (
                SELECT MIN(tp2.tanggal)
                FROM transaksi_penjualan tp2
                WHERE tp2.id_pelanggan = transaksi_penjualan.id_pelanggan
            )')
            ->when($this->selectedCustomerType, fn($q) => $q->where('pelanggan.type', $this->selectedCustomerType))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->distinct('pelanggan.id')
            ->count('pelanggan.id');

        // Customer retention rate
        $previousPeriod = $this->getPreviousPeriodRange();
        $previousActiveCustomers = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', $previousPeriod)
            ->when($this->selectedCustomerType, fn($q) => $q->where('pelanggan.type', $this->selectedCustomerType))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->distinct('pelanggan.id')
            ->count('pelanggan.id');

        $retainedCustomers = DB::table('transaksi_penjualan as tp1')
            ->join('pelanggan', 'tp1.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('tp1.tanggal', [$startDate, $endDate])
            ->whereExists(function ($query) use ($previousPeriod) {
                $query->select(DB::raw(1))
                    ->from('transaksi_penjualan as tp2')
                    ->whereColumn('tp2.id_pelanggan', 'tp1.id_pelanggan')
                    ->whereBetween('tp2.tanggal', $previousPeriod);
            })
            ->when($this->selectedCustomerType, fn($q) => $q->where('pelanggan.type', $this->selectedCustomerType))
            ->when($this->selectedTbbm, fn($q) => $q->where('tp1.id_tbbm', $this->selectedTbbm))
            ->distinct('pelanggan.id')
            ->count('pelanggan.id');

        $retentionRate = $previousActiveCustomers > 0 ? round(($retainedCustomers / $previousActiveCustomers) * 100, 1) : 0;

        // Average Customer Lifetime Value
        $clvQuery = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomerType) {
            $clvQuery->where('pelanggan.type', $this->selectedCustomerType);
        }

        if ($this->selectedTbbm) {
            $clvQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        $avgCLV = $clvQuery->select([
            'transaksi_penjualan.id_pelanggan',
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as customer_revenue')
        ])
            ->groupBy('transaksi_penjualan.id_pelanggan')
            ->get()
            ->avg('customer_revenue') ?? 0;

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'new_customers' => $newCustomers,
            'retention_rate' => $retentionRate,
            'avg_clv' => $avgCLV,
            'customer_activation_rate' => $totalCustomers > 0 ? round(($activeCustomers / $totalCustomers) * 100, 1) : 0,
        ];
    }

    public function getPreviousPeriodRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'current_quarter' => [$now->subQuarter()->startOfQuarter(), $now->subQuarter()->endOfQuarter()],
            'current_year' => [$now->subYear()->startOfYear(), $now->subYear()->endOfYear()],
            'last_year' => [$now->subYears(2)->startOfYear(), $now->subYears(2)->endOfYear()],
            default => [$now->subYear()->startOfYear(), $now->subYear()->endOfYear()],
        };
    }

    public function getCustomerSegmentationData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomerType) {
            $query->where('pelanggan.type', $this->selectedCustomerType);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        $customerData = $query->select([
            'pelanggan.id',
            'pelanggan.nama',
            'pelanggan.type',
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            DB::raw('AVG(penjualan_detail.volume_item * penjualan_detail.harga_jual) as avg_order_value'),
            DB::raw('DATEDIFF(MAX(transaksi_penjualan.tanggal), MIN(transaksi_penjualan.tanggal)) as customer_lifespan_days'),
        ])
            ->groupBy('pelanggan.id', 'pelanggan.nama', 'pelanggan.type')
            ->get();

        // Segment customers based on RFM analysis
        $segments = [
            'champions' => 0,
            'loyal_customers' => 0,
            'potential_loyalists' => 0,
            'new_customers' => 0,
            'at_risk' => 0,
            'cannot_lose_them' => 0,
            'hibernating' => 0,
        ];

        foreach ($customerData as $customer) {
            $recency = Carbon::parse($endDate)->diffInDays(Carbon::now()); // Simplified
            $frequency = $customer->total_orders;
            $monetary = $customer->total_revenue;

            // Simple segmentation logic
            if ($frequency >= 10 && $monetary >= 100000000) {
                $segments['champions']++;
            } elseif ($frequency >= 5 && $monetary >= 50000000) {
                $segments['loyal_customers']++;
            } elseif ($frequency >= 3 && $monetary >= 20000000) {
                $segments['potential_loyalists']++;
            } elseif ($frequency <= 2) {
                $segments['new_customers']++;
            } elseif ($recency > 90) {
                $segments['hibernating']++;
            } elseif ($monetary >= 50000000) {
                $segments['cannot_lose_them']++;
            } else {
                $segments['at_risk']++;
            }
        }

        return $segments;
    }

    public function getTopCustomersByValueData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomerType) {
            $query->where('pelanggan.type', $this->selectedCustomerType);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        return $query->select([
            'pelanggan.nama as customer_name',
            'pelanggan.type as customer_type',
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            DB::raw('AVG(penjualan_detail.volume_item * penjualan_detail.harga_jual) as avg_order_value'),
            DB::raw('SUM(penjualan_detail.volume_item) as total_volume'),
            DB::raw('MAX(transaksi_penjualan.tanggal) as last_order_date'),
            DB::raw('MIN(transaksi_penjualan.tanggal) as first_order_date'),
        ])
            ->groupBy('pelanggan.id', 'pelanggan.nama', 'pelanggan.type')
            ->orderBy('total_revenue', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    public function getCustomerGrowthTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        if ($this->selectedCustomerType) {
            $query->where('pelanggan.type', $this->selectedCustomerType);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        return $query->select([
            DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m") as month'),
            DB::raw('COUNT(DISTINCT CASE WHEN transaksi_penjualan.tanggal = (
                    SELECT MIN(tp2.tanggal)
                    FROM transaksi_penjualan tp2
                    WHERE tp2.id_pelanggan = transaksi_penjualan.id_pelanggan
                ) THEN transaksi_penjualan.id_pelanggan END) as new_customers'),
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id_pelanggan) as active_customers'),
        ])
            ->groupBy(DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getCustomerTypeDistributionData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->select([
                'pelanggan.type',
                DB::raw('COUNT(DISTINCT pelanggan.id) as customer_count'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            ])
            ->groupBy('pelanggan.type')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedCustomerType(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTbbm(): void
    {
        $this->dispatch('refresh-charts');
    }
}
