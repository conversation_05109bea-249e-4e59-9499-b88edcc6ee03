<div class="space-y-6">
    <!-- Informasi Alamat -->
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Urutan
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white font-semibold">
                {{ $record->urutan }}
            </p>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Dibuat Oleh
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ $record->createdBy->name ?? 'System' }}
            </p>
        </div>
    </div>

    <!-- Alamat Lengkap -->
    <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Alamat Lengkap
        </label>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <p class="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{{ $record->alamat }}</p>
        </div>
    </div>

    <!-- Keterangan -->
    @if($record->keterangan)
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Keterangan
            </label>
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                <p class="text-sm text-blue-900 dark:text-blue-100 whitespace-pre-wrap">{{ $record->keterangan }}</p>
            </div>
        </div>
    @else
        <div class="text-center py-4">
            <div class="mx-auto h-8 w-8 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Tidak ada keterangan tambahan
            </p>
        </div>
    @endif

    <!-- Informasi Tambahan -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
        <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400">
            <div>
                <span class="font-medium">Dibuat:</span>
                {{ $record->created_at->format('d/m/Y H:i') }}
            </div>
            @if($record->updated_at != $record->created_at)
                <div>
                    <span class="font-medium">Diupdate:</span>
                    {{ $record->updated_at->format('d/m/Y H:i') }}
                </div>
            @endif
        </div>
        
        @if($record->updatedBy)
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                <span class="font-medium">Terakhir diupdate oleh:</span>
                {{ $record->updatedBy->name }}
            </div>
        @endif
    </div>

    <!-- Summary Card -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
        <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
                <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                </div>
            </div>
            <div class="flex-1">
                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Alamat Transaksi #{{ $record->urutan }}
                </h4>
                <p class="text-sm text-blue-700 dark:text-blue-200 mt-1">
                    Alamat khusus untuk transaksi {{ $record->transaksiPenjualan->kode ?? 'ini' }}
                </p>
            </div>
        </div>
    </div>
</div>
