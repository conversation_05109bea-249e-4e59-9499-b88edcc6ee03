<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Akun;
use Illuminate\Support\Facades\DB;

class CoaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate existing data
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Akun::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $accounts = [
            // ASET - KAS & BANK (100-199)
            ['kode_akun' => '100', 'nama_akun' => 'Cash in Hand', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '101', 'nama_akun' => 'Giro Bank Mandiri - *************', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '102', 'nama_akun' => 'Giro Bank BNI - *********', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '103', 'nama_akun' => 'Giro USD BNI - **********', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '104', 'nama_akun' => 'Giro Bank CIMB Niaga - ************', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '105', 'nama_akun' => 'Giro Bank BSI - **********', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '106', 'nama_akun' => 'Deposito', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],

            // ASET - PIUTANG (200-299)
            ['kode_akun' => '200.1', 'nama_akun' => 'Piutang Usaha PT. ABC', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '200.2', 'nama_akun' => 'Piutang Usaha PT. DEF', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '200.3', 'nama_akun' => 'Piutang Usaha PT. GHI', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '200.4', 'nama_akun' => 'Piutang Usaha PT. JKL', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '200.5', 'nama_akun' => 'Piutang Usaha PT. MNO', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '201.1', 'nama_akun' => 'Piutang Pemegang saham An. Budi', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '201.2', 'nama_akun' => 'Piutang Pemegang saham An. Sari', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '201.3', 'nama_akun' => 'Piutang Pemegang saham An. Andi', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '201.4', 'nama_akun' => 'Piutang Pemegang saham An. Dewi', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '201.5', 'nama_akun' => 'Piutang Pemegang saham An. Rudi', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '202.1', 'nama_akun' => 'Piutang Karyawan An. Ahmad', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '202.2', 'nama_akun' => 'Piutang Karyawan An. Siti', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '202.3', 'nama_akun' => 'Piutang Karyawan An. Joko', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '202.4', 'nama_akun' => 'Piutang Karyawan An. Maya', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '202.5', 'nama_akun' => 'Piutang Karyawan An. Doni', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '203', 'nama_akun' => 'PPN Keluaran', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '204', 'nama_akun' => 'PPN Masukan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '299', 'nama_akun' => 'Piutang Lain - lain', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],

            // ASET TETAP (300-399)
            ['kode_akun' => '300', 'nama_akun' => 'Tanah', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '301', 'nama_akun' => 'Bangunan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '302', 'nama_akun' => 'Kendaraan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '303', 'nama_akun' => 'Akumulasi Penyusutan Kendaraan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '304', 'nama_akun' => 'Inventaris', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '305', 'nama_akun' => 'Akumulasi Penyusutan Inventaris', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // KEWAJIBAN & EKUITAS (306-399)
            ['kode_akun' => '306', 'nama_akun' => 'Hutang Usaha', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '307', 'nama_akun' => 'Hutang Bank', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '308', 'nama_akun' => 'Hutang Leasing/Kendaraan', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '309', 'nama_akun' => 'Hutang Pajak', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '310', 'nama_akun' => 'Modal Disetor', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '311', 'nama_akun' => 'Laba ditahan sd tahun 2024', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '312', 'nama_akun' => 'Dividen', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '313', 'nama_akun' => 'Laba tahun berjalan', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
        ];

        // PENDAPATAN (400-499)
        $pendapatan = [
            ['kode_akun' => '400', 'nama_akun' => 'Penjualan BBM', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '401', 'nama_akun' => 'OAT Penjualan BBM', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '402', 'nama_akun' => 'PBBKB', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '403', 'nama_akun' => 'Pendapatan Jasa Angkut', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
        ];

        // HPP & PERSEDIAAN (500-599)
        $hpp = [
            ['kode_akun' => '500', 'nama_akun' => 'Persediaan Awal', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '501', 'nama_akun' => 'Harga Dasar BBM', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '502', 'nama_akun' => 'Rounding', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '503', 'nama_akun' => 'Disc Pembeliaan', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '504', 'nama_akun' => 'Persediaan Akhir', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '505', 'nama_akun' => 'PBBKB', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '506', 'nama_akun' => 'HPP Jasa Angkut APMS', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '507', 'nama_akun' => 'HPP BBM', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        // BEBAN OPERASIONAL (600-699)
        $beban = [
            ['kode_akun' => '600', 'nama_akun' => 'Gaji', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '601', 'nama_akun' => 'Upah/Honor', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '602', 'nama_akun' => 'THR', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '603', 'nama_akun' => 'Beban Ekspedisi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '604.1', 'nama_akun' => 'ATK Kantor', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '604.2', 'nama_akun' => 'ATK Materai', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '605', 'nama_akun' => 'Beban Kesehatan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '606', 'nama_akun' => 'Beban Listrik', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '607', 'nama_akun' => 'Beban Telepon', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '608.1', 'nama_akun' => 'Beban Maintanance Bangunan Kantor', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '608.2', 'nama_akun' => 'Beban Maintanance Bangunan pool', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '608.3', 'nama_akun' => 'Beban Maintanance Kendaraan Service', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '608.4', 'nama_akun' => 'Beban Maintanance Kendaraan Spare Part', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '608.5', 'nama_akun' => 'Beban Maintanance Kendaraan Ban', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '609', 'nama_akun' => 'Beban Maintanance Inventaris', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '610', 'nama_akun' => 'Perjalanan Dinas', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '611', 'nama_akun' => 'Fee', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '612', 'nama_akun' => 'BBM/Tol Operasional', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '613', 'nama_akun' => 'Beban Jasa Transport', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '614', 'nama_akun' => 'Konsumsi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '615', 'nama_akun' => 'Beban Marketing', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '616', 'nama_akun' => 'Beban Digital Marketing', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '617', 'nama_akun' => 'Beban Operasional', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '618', 'nama_akun' => 'Sumbangan Kelembagaan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '619', 'nama_akun' => 'Lisensi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '620', 'nama_akun' => 'Entertaint', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '621', 'nama_akun' => 'Beban Pralatan kantor', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '622', 'nama_akun' => 'Zakat', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '623.1', 'nama_akun' => 'Beban Sewa Kendaraan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '623.2', 'nama_akun' => 'Beban Sewa Kapal', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '624', 'nama_akun' => 'Jasa Konsultasi', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '699', 'nama_akun' => 'Beban Lain-lain', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        // PENDAPATAN LAIN-LAIN (700-799)
        $pendapatanLain = [
            ['kode_akun' => '700', 'nama_akun' => 'Pendapatan Jasa Giro', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '701', 'nama_akun' => 'Pendapatan Bunga Bank', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '799', 'nama_akun' => 'Pendapatan Lain Lain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
        ];

        // BIAYA LAIN-LAIN (800-899)
        $biayaLain = [
            ['kode_akun' => '800', 'nama_akun' => 'Adm Bank', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '801', 'nama_akun' => 'Pajak Bank', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '802', 'nama_akun' => 'Bunga Bank', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '899', 'nama_akun' => 'Lain Lain', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        // BEBAN PAJAK (900-999)
        $bebanPajak = [
            ['kode_akun' => '900', 'nama_akun' => 'PBB', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '901', 'nama_akun' => 'PPN', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '902', 'nama_akun' => 'Pajak Pasal 21', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '903', 'nama_akun' => 'Pajak Pasal 22', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '904', 'nama_akun' => 'Pajak Pasal 23', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '905', 'nama_akun' => 'Pajak Pasal 25', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '906', 'nama_akun' => 'Pajak Pasal 29', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '999', 'nama_akun' => 'Pajak Lainnnya', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        // Merge all accounts
        $allAccounts = array_merge($accounts, $pendapatan, $hpp, $beban, $pendapatanLain, $biayaLain, $bebanPajak);

        foreach ($allAccounts as $account) {
            Akun::create($account);
        }

        $this->command->info('✅ COA seeded successfully!');
        $this->command->info('📊 Total accounts created: ' . count($allAccounts));
        $this->command->info('💰 Aset & Kewajiban: ' . count($accounts));
        $this->command->info('💵 Pendapatan: ' . count($pendapatan));
        $this->command->info('📦 HPP & Persediaan: ' . count($hpp));
        $this->command->info('💸 Beban Operasional: ' . count($beban));
        $this->command->info('🏦 Pendapatan Lain: ' . count($pendapatanLain));
        $this->command->info('🔴 Biaya Lain: ' . count($biayaLain));
        $this->command->info('🏛️ Beban Pajak: ' . count($bebanPajak));
        $this->command->info('🏦 Bank Accounts Updated:');
        $this->command->info('   - 100: Cash in Hand');
        $this->command->info('   - 101: Giro Bank Mandiri - *************');
        $this->command->info('   - 102: Giro Bank BNI - *********');
        $this->command->info('   - 103: Giro USD BNI - **********');
        $this->command->info('   - 104: Giro Bank CIMB Niaga - ************');
        $this->command->info('   - 105: Giro Bank BSI - **********');
    }
}
