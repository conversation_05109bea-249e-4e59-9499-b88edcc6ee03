<x-filament-panels::page>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter Financial Performance</h3>
            {{ $this->form }}
        </div>

        <!-- Financial KPI Cards -->
        @php
            $kpiData = $this->getFinancialKpiData();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Revenue -->
            <div class="bg-gradient-to-r from-green-600 to-green-700 border-2 border-green-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(34, 197, 94, 0.3), 0 4px 6px -2px rgba(34, 197, 94, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-banknotes class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Total Revenue
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['total_revenue'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Net Profit -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 border-2 border-blue-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.3), 0 4px 6px -2px rgba(59, 130, 246, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-chart-pie class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Net Profit
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['net_profit'], 0, ',', '.') }}
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    Margin: {{ $kpiData['profit_margin'] }}%
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Net Cash Flow -->
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 border-2 border-purple-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(147, 51, 234, 0.3), 0 4px 6px -2px rgba(147, 51, 234, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-arrow-trending-up class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Net Cash Flow
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['net_cash_flow'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Outstanding Receivables -->
            <div class="bg-gradient-to-r from-orange-600 to-orange-700 border-2 border-orange-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-clock class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Outstanding Receivables
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['outstanding_receivables'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Gross Profit -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-currency-dollar class="h-6 w-6 text-green-500 dark:text-green-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Gross Profit
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['gross_profit'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Costs -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-minus-circle class="h-6 w-6 text-red-500 dark:text-red-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Total Costs
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['total_costs'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cash Inflow -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-arrow-down-circle class="h-6 w-6 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Cash Inflow
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['cash_inflow'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cash Outflow -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-arrow-up-circle class="h-6 w-6 text-red-500 dark:text-red-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Cash Outflow
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['cash_outflow'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Monthly P&L Chart -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monthly Profit & Loss</h3>
                <div class="h-64">
                    <canvas id="monthlyPLChart"></canvas>
                </div>
            </div>

            <!-- Cost Breakdown Chart -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Cost Breakdown</h3>
                <div class="h-64">
                    <canvas id="costBreakdownChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Cash Flow Chart -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Daily Cash Flow</h3>
            <div class="h-80">
                <canvas id="cashFlowChart"></canvas>
            </div>
        </div>

        <!-- Revenue by TBBM Table -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Revenue by TBBM</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                TBBM
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Orders
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Revenue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Estimated Profit
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($this->getRevenueByTbbmData() as $tbbm)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $tbbm->tbbm_name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ number_format($tbbm->total_orders) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($tbbm->revenue, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($tbbm->estimated_profit, 0, ',', '.') }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            let monthlyPLChart;
            let costBreakdownChart;
            let cashFlowChart;

            function initializeCharts() {
                // Monthly P&L Chart
                const plData = @json($this->getMonthlyProfitLossData());
                const plCtx = document.getElementById('monthlyPLChart').getContext('2d');
                
                if (monthlyPLChart) {
                    monthlyPLChart.destroy();
                }
                
                monthlyPLChart = new Chart(plCtx, {
                    type: 'line',
                    data: {
                        labels: plData.map(d => d.month),
                        datasets: [{
                            label: 'Revenue',
                            data: plData.map(d => d.revenue),
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1
                        }, {
                            label: 'Gross Profit',
                            data: plData.map(d => d.gross_profit),
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1
                        }, {
                            label: 'Net Profit',
                            data: plData.map(d => d.net_profit),
                            borderColor: 'rgb(139, 92, 246)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Cost Breakdown Chart
                const costData = @json($this->getCostBreakdownData());
                const costCtx = document.getElementById('costBreakdownChart').getContext('2d');
                
                if (costBreakdownChart) {
                    costBreakdownChart.destroy();
                }
                
                costBreakdownChart = new Chart(costCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['COGS', 'Operational', 'Delivery', 'Admin', 'Marketing', 'Other'],
                        datasets: [{
                            data: [costData.cogs, costData.operational_costs, costData.delivery_costs, costData.admin_costs, costData.marketing_costs, costData.other_costs],
                            backgroundColor: ['#EF4444', '#F97316', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6'],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });

                // Cash Flow Chart
                const cashFlowData = @json($this->getCashFlowData());
                const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
                
                if (cashFlowChart) {
                    cashFlowChart.destroy();
                }
                
                cashFlowChart = new Chart(cashFlowCtx, {
                    type: 'bar',
                    data: {
                        labels: cashFlowData.map(d => new Date(d.date).toLocaleDateString('id-ID')),
                        datasets: [{
                            label: 'Cash Inflow',
                            data: cashFlowData.map(d => d.inflow),
                            backgroundColor: 'rgba(34, 197, 94, 0.8)',
                            borderColor: 'rgb(34, 197, 94)',
                            borderWidth: 1
                        }, {
                            label: 'Cash Outflow',
                            data: cashFlowData.map(d => d.outflow),
                            backgroundColor: 'rgba(239, 68, 68, 0.8)',
                            borderColor: 'rgb(239, 68, 68)',
                            borderWidth: 1
                        }, {
                            label: 'Net Cash Flow',
                            data: cashFlowData.map(d => d.net_flow),
                            type: 'line',
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
            }

            // Initialize charts on page load
            document.addEventListener('DOMContentLoaded', function() {
                initializeCharts();
            });

            // Listen for refresh events from Livewire
            document.addEventListener('livewire:init', () => {
                Livewire.on('refresh-charts', () => {
                    setTimeout(() => {
                        location.reload();
                    }, 100);
                });
            });
        </script>
    @endpush
</x-filament-panels::page>
