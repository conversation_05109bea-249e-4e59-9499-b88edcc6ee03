<?php

namespace App\Filament\Pages;

use App\Models\DeliveryOrder;
use App\Models\Kendaraan;
use App\Models\User;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class OperationalDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationLabel = 'Dashboard Operasional';
    protected static ?string $title = 'Dashboard Monitoring Operasional';
    protected static string $view = 'filament.pages.operational-dashboard';
    protected static ?int $navigationSort = 7;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedDriver = null;
    #[Url(keep: true)]

    public ?string $selectedVehicle = null;
    #[Url(keep: true)]

    public ?string $selectedTbbm = null;
    #[Url(keep: true)]

    public ?string $selectedStatus = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('director') ?? false;
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_month';
        $this->startDate = $this->startDate ?? now()->startOfMonth()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'today' => 'Hari Ini',
                        'yesterday' => 'Kemarin',
                        'current_week' => 'Minggu Ini',
                        'current_month' => 'Bulan Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_month')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedDriver')
                    ->label('Driver (Opsional)')
                    ->options(User::whereHas('roles', function ($query) {
                        $query->where('name', 'driver');
                    })->pluck('name', 'id'))
                    ->searchable()
                    ->placeholder('Semua Driver')
                    ->live(),

                Select::make('selectedVehicle')
                    ->label('Kendaraan (Opsional)')
                    ->options(Kendaraan::pluck('no_pol_kendaraan', 'id'))
                    ->searchable()
                    ->placeholder('Semua Kendaraan')
                    ->live(),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live(),

                Select::make('selectedStatus')
                    ->label('Status (Opsional)')
                    ->options([
                        'pending' => 'Pending',
                        'muat' => 'Sedang Muat',
                        'selesai' => 'Selesai',
                    ])
                    ->placeholder('Semua Status')
                    ->live(),
            ])
            ->columns(7);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'today' => [
                $this->startDate = $now->format('Y-m-d'),
                $this->endDate = $now->format('Y-m-d')
            ],
            'yesterday' => [
                $this->startDate = $now->subDay()->format('Y-m-d'),
                $this->endDate = $now->format('Y-m-d')
            ],
            'current_week' => [
                $this->startDate = $now->startOfWeek()->format('Y-m-d'),
                $this->endDate = $now->endOfWeek()->format('Y-m-d')
            ],
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getOperationalKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $baseQuery = DeliveryOrder::query()
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedDriver) {
            $baseQuery->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $baseQuery->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        if ($this->selectedTbbm) {
            $baseQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $baseQuery->where('delivery_order.status_muat', $this->selectedStatus);
        }

        // Basic metrics
        $totalDeliveries = $baseQuery->count();
        $completedDeliveries = (clone $baseQuery)->where('delivery_order.status_muat', 'selesai')->count();
        $pendingDeliveries = (clone $baseQuery)->whereIn('delivery_order.status_muat', ['pending', 'muat'])->count();

        // Efficiency metrics
        $onTimeDeliveries = (clone $baseQuery)
            ->where('delivery_order.status_muat', 'selesai')
            ->whereRaw('DATE(delivery_order.waktu_selesai_muat) <= DATE(delivery_order.tanggal_delivery)')
            ->count();

        $completionRate = $totalDeliveries > 0 ? round(($completedDeliveries / $totalDeliveries) * 100, 1) : 0;
        $onTimeRate = $completedDeliveries > 0 ? round(($onTimeDeliveries / $completedDeliveries) * 100, 1) : 0;

        // Vehicle utilization
        $activeVehicles = (clone $baseQuery)->distinct('delivery_order.id_kendaraan')->count('delivery_order.id_kendaraan');
        $totalVehicles = Kendaraan::count();
        $vehicleUtilization = $totalVehicles > 0 ? round(($activeVehicles / $totalVehicles) * 100, 1) : 0;

        // Average delivery time
        $avgDeliveryTime = (clone $baseQuery)
            ->where('delivery_order.status_muat', 'selesai')
            ->whereNotNull('delivery_order.waktu_muat')
            ->whereNotNull('delivery_order.waktu_selesai_muat')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, delivery_order.waktu_muat, delivery_order.waktu_selesai_muat)) as avg_time')
            ->value('avg_time') ?? 0;

        return [
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'pending_deliveries' => $pendingDeliveries,
            'completion_rate' => $completionRate,
            'on_time_deliveries' => $onTimeDeliveries,
            'on_time_rate' => $onTimeRate,
            'active_vehicles' => $activeVehicles,
            'total_vehicles' => $totalVehicles,
            'vehicle_utilization' => $vehicleUtilization,
            'avg_delivery_time' => round($avgDeliveryTime, 1),
        ];
    }

    public function getDriverPerformanceData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('users', 'delivery_order.id_user', '=', 'users.id')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('users.deleted_at')
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        return $query->select([
            'users.name as driver_name',
            DB::raw('COUNT(delivery_order.id) as total_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
            DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as completion_rate'),
            DB::raw('AVG(CASE WHEN delivery_order.status_muat = "selesai" AND delivery_order.waktu_muat IS NOT NULL AND delivery_order.waktu_selesai_muat IS NOT NULL THEN TIMESTAMPDIFF(HOUR, delivery_order.waktu_muat, delivery_order.waktu_selesai_muat) END) as avg_delivery_time'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" AND DATE(delivery_order.waktu_selesai_muat) <= DATE(delivery_order.tanggal_delivery) THEN 1 END) as on_time_deliveries'),
        ])
            ->groupBy('users.id', 'users.name')
            ->orderBy('total_trips', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function getVehicleUtilizationData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('kendaraans', 'delivery_order.id_kendaraan', '=', 'kendaraans.id')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        return $query->select([
            'kendaraans.no_pol_kendaraan as vehicle_plate',
            'kendaraans.tipe as vehicle_type',
            'kendaraans.kapasitas as vehicle_capacity',
            DB::raw('COUNT(delivery_order.id) as total_trips'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_trips'),
            DB::raw('ROUND((COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) / COUNT(delivery_order.id)) * 100, 1) as utilization_rate'),
            DB::raw('SUM(delivery_order.volume_do) as total_volume_delivered'),
        ])
            ->groupBy('kendaraans.id', 'kendaraans.no_pol_kendaraan', 'kendaraans.tipe', 'kendaraans.kapasitas')
            ->orderBy('total_trips', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    public function getDailyOperationalTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->whereNull('transaksi_penjualan.deleted_at');

        // Apply filters
        if ($this->selectedDriver) {
            $query->where('delivery_order.id_user', $this->selectedDriver);
        }

        if ($this->selectedVehicle) {
            $query->where('delivery_order.id_kendaraan', $this->selectedVehicle);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedStatus) {
            $query->where('delivery_order.status_muat', $this->selectedStatus);
        }

        return $query->select([
            DB::raw('DATE(delivery_order.tanggal_delivery) as date'),
            DB::raw('COUNT(delivery_order.id) as total_deliveries'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "selesai" THEN 1 END) as completed_deliveries'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "pending" THEN 1 END) as pending_deliveries'),
            DB::raw('COUNT(CASE WHEN delivery_order.status_muat = "muat" THEN 1 END) as loading_deliveries'),
            DB::raw('COUNT(DISTINCT delivery_order.id_kendaraan) as active_vehicles'),
            DB::raw('COUNT(DISTINCT delivery_order.id_user) as active_drivers'),
        ])
            ->groupBy(DB::raw('DATE(delivery_order.tanggal_delivery)'))
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedDriver(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedVehicle(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedTbbm(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedSelectedStatus(): void
    {
        $this->dispatch('refresh-charts');
    }
}
