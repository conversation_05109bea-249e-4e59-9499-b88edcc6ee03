<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class InvoicePublishPostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     * Creates posting rules for invoice publishing (when invoice is published/issued)
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating Invoice Publish Posting Rules...');

        // Clear existing invoice publish posting rules
        $this->clearExistingPublishRules();

        // Create posting rules for invoice publishing
        $this->createInvoicePublishRules();

        $this->command->info('✅ Invoice Publish Posting Rules created successfully!');
    }

    private function clearExistingPublishRules()
    {
        // Delete existing invoice publish posting rules
        $publishRules = PostingRule::where('source_type', 'Invoice')
            ->where('rule_name', 'LIKE', '%Publish%')
            ->orWhere('rule_name', 'LIKE', '%Terbit%')
            ->get();

        foreach ($publishRules as $rule) {
            PostingRuleEntry::where('posting_rule_id', $rule->id)->delete();
            $rule->delete();
        }

        $this->command->info('Cleared existing invoice publish posting rules.');
    }

    private function createInvoicePublishRules()
    {
        // Get required accounts with robust lookup
        $accounts = $this->getRequiredAccounts();

        if (!$accounts) {
            $this->command->error('Required accounts not found. Please run CoaSeeder first.');
            return;
        }

        // 1. Posting Rule untuk Penerbitan Invoice - General (BBM)
        $this->createBBMPublishRule($accounts);

        // 2. Posting Rule untuk Penerbitan Invoice - Jasa Angkut
        $this->createJasaAngkutPublishRule($accounts);

        // 3. Posting Rule untuk PPN (jika include_ppn = true)
        $this->createPPNRule($accounts);

        // 4. Posting Rule untuk Operasional (jika include_operasional_kerja = true)
        $this->createOperasionalRule($accounts);

        // 5. Posting Rule untuk PBBKB (jika include_pbbkb = true)
        $this->createPBBKBRule($accounts);
    }

    private function getRequiredAccounts()
    {
        $accounts = [];

        // Piutang Usaha
        $accounts['piutang'] = Akun::where('nama_akun', 'LIKE', '%Piutang Usaha%')
            ->orWhere('nama_akun', 'LIKE', '%Piutang%')
            ->first();

        // Penjualan BBM
        $accounts['penjualan_bbm'] = Akun::where('nama_akun', 'LIKE', '%Penjualan BBM%')
            ->orWhere('nama_akun', 'LIKE', '%Penjualan%')
            ->first();

        // Pendapatan Jasa Angkut
        $accounts['jasa_angkut'] = Akun::where('nama_akun', 'LIKE', '%Jasa Angkut%')
            ->orWhere('nama_akun', 'LIKE', '%Pendapatan Jasa%')
            ->first();

        // PPN Keluaran
        $accounts['ppn'] = Akun::where('nama_akun', 'LIKE', '%PPN%')
            ->where('nama_akun', 'NOT LIKE', '%Masukan%')
            ->first();

        // Pendapatan Operasional
        $accounts['operasional'] = Akun::where('nama_akun', 'LIKE', '%Operasional%')
            ->orWhere('nama_akun', 'LIKE', '%Pendapatan Lain%')
            ->first();

        // Pendapatan PBBKB
        $accounts['pbbkb'] = Akun::where('nama_akun', 'LIKE', '%PBBKB%')
            ->orWhere('nama_akun', 'LIKE', '%Pendapatan Lain%')
            ->first();

        // Check if all required accounts exist
        if (!$accounts['piutang'] || !$accounts['penjualan_bbm']) {
            return null;
        }

        return $accounts;
    }

    private function createBBMPublishRule($accounts)
    {
        $rule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - BBM (Publish)',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'publish', 'type' => 'bbm'],
            'description' => 'Auto posting ketika invoice BBM diterbitkan',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['piutang']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari penerbitan invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Penjualan BBM
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['penjualan_bbm']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Penjualan BBM dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        $this->command->info('✅ Created BBM publish posting rule');
    }

    private function createJasaAngkutPublishRule($accounts)
    {
        if (!$accounts['jasa_angkut']) {
            $this->command->warn('Jasa Angkut account not found, skipping...');
            return;
        }

        $rule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - Jasa Angkut (Publish)',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'publish', 'type' => 'jasa_angkut'],
            'description' => 'Auto posting ketika invoice jasa angkut diterbitkan',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['piutang']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari penerbitan invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Pendapatan Jasa Angkut
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['jasa_angkut']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Pendapatan jasa angkut dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        $this->command->info('✅ Created Jasa Angkut publish posting rule');
    }

    private function createPPNRule($accounts)
    {
        if (!$accounts['ppn']) {
            $this->command->warn('PPN account not found, skipping...');
            return;
        }

        $rule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - PPN (Publish)',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'publish', 'include_ppn' => true],
            'description' => 'Auto posting PPN ketika invoice dengan PPN diterbitkan',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Credit: PPN Keluaran
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['ppn']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_pajak',
            'description_template' => 'PPN dari penerbitan invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        $this->command->info('✅ Created PPN publish posting rule');
    }

    private function createOperasionalRule($accounts)
    {
        if (!$accounts['operasional']) {
            $this->command->warn('Operasional account not found, skipping...');
            return;
        }

        $rule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - Operasional (Publish)',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'publish', 'include_operasional_kerja' => true],
            'description' => 'Auto posting biaya operasional ketika invoice dengan operasional diterbitkan',
            'is_active' => true,
            'priority' => 4,
            'created_by' => 1,
        ]);

        // Credit: Pendapatan Operasional
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['operasional']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_operasional_kerja',
            'description_template' => 'Pendapatan operasional dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        $this->command->info('✅ Created Operasional publish posting rule');
    }

    private function createPBBKBRule($accounts)
    {
        if (!$accounts['pbbkb']) {
            $this->command->warn('PBBKB account not found, skipping...');
            return;
        }

        $rule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - PBBKB (Publish)',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'publish', 'include_pbbkb' => true],
            'description' => 'Auto posting PBBKB ketika invoice dengan PBBKB diterbitkan',
            'is_active' => true,
            'priority' => 5,
            'created_by' => 1,
        ]);

        // Credit: Pendapatan PBBKB
        PostingRuleEntry::create([
            'posting_rule_id' => $rule->id,
            'account_id' => $accounts['pbbkb']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_pbbkb',
            'description_template' => 'Pendapatan PBBKB dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        $this->command->info('✅ Created PBBKB publish posting rule');
    }
}
