# Dashboard Troubleshooting Guide

## Common Issues and Solutions

### 1. SQL Error: "Duplicate column name 'id'"

**Error Message:**
```
SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'id'
```

**Cause:** Multiple table joins causing column name conflicts.

**Solution:**
- Use table aliases in queries
- Specify column names explicitly with table prefixes
- Avoid using `select *` in complex joins

**Example Fix:**
```php
// Instead of:
DB::table('transaksi_penjualan')
    ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
    ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')

// Use:
DB::table('transaksi_penjualan as tp')
    ->join('pelanggan as p', 'tp.id_pelanggan', '=', 'p.id')
    ->join('penjualan_detail as pd', 'tp.id', '=', 'pd.id_transaksi_penjualan')
    ->select('tp.id as transaction_id', 'p.id as customer_id', 'pd.id as detail_id')
```

### 2. Dashboard Loading Slowly

**Causes:**
- Complex queries without proper indexing
- Large datasets without pagination
- Missing database indexes

**Solutions:**
1. Add database indexes:
```sql
CREATE INDEX idx_transaksi_tanggal ON transaksi_penjualan(tanggal);
CREATE INDEX idx_delivery_tanggal ON delivery_order(tanggal_delivery);
CREATE INDEX idx_pelanggan_type ON pelanggan(type);
```

2. Implement query caching:
```php
$data = Cache::remember('dashboard_kpi_' . $this->selectedPeriod, 300, function () {
    return $this->getKpiData();
});
```

3. Use pagination for large datasets:
```php
->paginate(50)
```

### 3. Charts Not Displaying

**Causes:**
- JavaScript errors
- Missing Chart.js library
- Data format issues

**Solutions:**
1. Check browser console for JavaScript errors
2. Ensure Chart.js is loaded:
```html
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
```

3. Validate data format:
```php
// Ensure data is properly formatted for charts
$chartData = collect($rawData)->map(function ($item) {
    return [
        'label' => $item->label,
        'value' => (float) $item->value
    ];
})->toArray();
```

### 4. Filter Not Working

**Causes:**
- Missing Livewire `live()` attribute
- Query not applying filters
- JavaScript not triggering refresh

**Solutions:**
1. Add `live()` to form components:
```php
Select::make('selectedCustomer')
    ->live()
    ->afterStateUpdated(fn() => $this->dispatch('refresh-charts'))
```

2. Ensure filters are applied in queries:
```php
$query = DB::table('transaksi_penjualan');

if ($this->selectedCustomer) {
    $query->where('id_pelanggan', $this->selectedCustomer);
}
```

### 5. Memory Limit Exceeded

**Causes:**
- Processing too much data at once
- Inefficient queries

**Solutions:**
1. Use chunking for large datasets:
```php
DB::table('large_table')->chunk(1000, function ($records) {
    // Process records in batches
});
```

2. Optimize queries:
```php
// Instead of loading all data
$allData = Model::all();

// Use specific selections
$data = Model::select('id', 'name', 'created_at')->get();
```

## Performance Optimization

### 1. Database Optimization

**Recommended Indexes:**
```sql
-- For transaction queries
CREATE INDEX idx_transaksi_tanggal_pelanggan ON transaksi_penjualan(tanggal, id_pelanggan);
CREATE INDEX idx_transaksi_tanggal_tbbm ON transaksi_penjualan(tanggal, id_tbbm);

-- For delivery queries
CREATE INDEX idx_delivery_tanggal_status ON delivery_order(tanggal_delivery, status_muat);
CREATE INDEX idx_delivery_tanggal_user ON delivery_order(tanggal_delivery, id_user);

-- For detail queries
CREATE INDEX idx_detail_transaksi ON penjualan_detail(id_transaksi_penjualan);
```

### 2. Caching Strategy

```php
// In dashboard methods
public function getKpiData(): array
{
    $cacheKey = "kpi_data_{$this->selectedPeriod}_{$this->selectedCustomer}";
    
    return Cache::remember($cacheKey, 300, function () {
        // Expensive query here
        return $this->calculateKpiData();
    });
}
```

### 3. Query Optimization

```php
// Use eager loading
$transactions = TransaksiPenjualan::with(['pelanggan', 'penjualanDetails'])
    ->whereBetween('tanggal', [$startDate, $endDate])
    ->get();

// Use raw queries for complex aggregations
$result = DB::select("
    SELECT 
        DATE_FORMAT(tanggal, '%Y-%m') as month,
        COUNT(*) as total_orders,
        SUM(total_amount) as total_revenue
    FROM transaksi_penjualan 
    WHERE tanggal BETWEEN ? AND ?
    GROUP BY DATE_FORMAT(tanggal, '%Y-%m')
", [$startDate, $endDate]);
```

## Maintenance Tasks

### Daily
- Check error logs: `storage/logs/laravel.log`
- Monitor dashboard loading times
- Verify data accuracy

### Weekly
- Clear old cache: `php artisan cache:clear`
- Check database performance
- Review slow query log

### Monthly
- Update database statistics: `ANALYZE TABLE table_name`
- Review and optimize slow queries
- Check disk space usage

## Monitoring

### Key Metrics to Monitor
1. Dashboard loading time
2. Database query execution time
3. Memory usage
4. Cache hit ratio
5. Error rate

### Logging
```php
// Add logging to dashboard methods
Log::info('Dashboard KPI calculation started', [
    'period' => $this->selectedPeriod,
    'customer' => $this->selectedCustomer
]);

$startTime = microtime(true);
$data = $this->getKpiData();
$executionTime = microtime(true) - $startTime;

Log::info('Dashboard KPI calculation completed', [
    'execution_time' => $executionTime,
    'data_count' => count($data)
]);
```

## Emergency Procedures

### If Dashboard is Down
1. Check Laravel logs: `tail -f storage/logs/laravel.log`
2. Check web server logs
3. Verify database connectivity: `php artisan tinker` then `DB::connection()->getPdo()`
4. Clear all caches: `php artisan optimize:clear`
5. Restart services if needed

### If Data is Incorrect
1. Check data source integrity
2. Verify filter logic
3. Compare with raw database queries
4. Check for timezone issues
5. Validate calculation logic

## Contact Information

For technical support:
- Developer: [Your Name]
- Email: [<EMAIL>]
- Emergency: [emergency.contact]
