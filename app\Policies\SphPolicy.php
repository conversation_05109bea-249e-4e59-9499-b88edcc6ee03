<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Sph;
use Illuminate\Auth\Access\HandlesAuthorization;

class SphPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_sph');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Sph $sph): bool
    {
        return $user->can('view_sph');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_sph');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Sph $sph): bool
    {
        return $user->can('update_sph');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Sph $sph): bool
    {
        return $user->can('delete_sph');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_sph');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Sph $sph): bool
    {
        return $user->can('force_delete_sph');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_sph');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Sph $sph): bool
    {
        return $user->can('restore_sph');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_sph');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Sph $sph): bool
    {
        return $user->can('replicate_sph');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_sph');
    }
}
