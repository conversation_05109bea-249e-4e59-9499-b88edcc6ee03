# Delivery Evidence Upload System

## Overview

Sistem upload bukti pengiriman telah ditambahkan ke dalam Driver Delivery Detail page untuk memungkinkan driver mengupload foto-foto bukti pengiriman sesuai dengan tahapan proses.

## Fitur yang Ditambahkan

### 1. Database Structure

#### Tabel `delivery_evidence`
- **id**: Primary key
- **id_pengiriman_driver**: Foreign key ke tabel pengiriman_driver
- **evidence_type**: Enum untuk jenis bukti
- **file_name**: Nama file
- **file_path**: Path file
- **original_name**: Nama file asli
- **mime_type**: Tipe MIME file
- **file_size**: Ukuran file
- **description**: Desk<PERSON>si (opsional)
- **sequence_number**: Nomor urut untuk multiple foto
- **uploaded_by**: User yang mengupload
- **timestamps & soft deletes**

#### Evidence Types
1. **foto_pemutusan_segel_atas** - Foto Pemutusan Segel Atas
2. **foto_pemutusan_segel_bawah** - Foto Pemutusan Segel Bawah
3. **foto_tera** - Foto Tera
4. **foto_sample_bbm** - Foto Sample BBM
5. **foto_tangki_mt_kosong** - Foto Tangki MT Kosong Setelah Selesai Bongkar
6. **foto_mt** - Foto MT
7. **foto_pembongkaran** - Foto Pembongkaran/Proses Bongkar dan Tangki Bongkar (minimal 3 foto)

### 2. Models

#### DeliveryEvidence Model
- Menggunakan Spatie Media Library untuk file management
- Relasi dengan PengirimanDriver dan User
- Media collections untuk setiap jenis bukti
- Helper methods untuk URL dan konversi gambar

#### PengirimanDriver Model Updates
- Ditambahkan relasi `deliveryEvidences()`
- Ditambahkan media collections untuk semua jenis bukti
- Updated media conversions untuk thumbnail dan preview

### 3. User Interface

#### Driver Delivery Detail Page
- **Upload Bukti Button**: Button orange dengan icon camera
- **Evidence Upload Form**: Form dengan grid layout untuk upload multiple files
- **Evidence Display Section**: Menampilkan bukti yang sudah diupload dalam grid
- **Image Modal**: Modal untuk melihat gambar dalam ukuran penuh

#### Form Features
- Multiple file upload untuk setiap jenis bukti
- Maximum 3 files untuk sebagian besar jenis, 5 files untuk foto pembongkaran
- File type validation (JPEG, PNG)
- File size limit (10MB)
- Required validation untuk foto pembongkaran

#### Display Features
- Grid layout responsif untuk menampilkan bukti
- Thumbnail preview dengan hover effects
- Click to view full size dalam modal
- Organized by evidence type dengan labels

### 4. File Structure

```
app/
├── Models/
│   ├── DeliveryEvidence.php (NEW)
│   └── PengirimanDriver.php (UPDATED)
├── Filament/Pages/
│   └── DriverDeliveryDetail.php (UPDATED)

database/
├── migrations/
│   └── 2025_07_28_000001_create_delivery_evidence_table.php (NEW)
└── seeders/
    └── DeliveryEvidenceSeeder.php (NEW)

resources/views/filament/pages/
└── driver-delivery-detail.blade.php (UPDATED)
```

## Usage

### For Drivers
1. Buka Driver Delivery Detail page
2. Klik button "Upload Bukti" (orange dengan icon camera)
3. Upload foto sesuai dengan jenis bukti yang diperlukan
4. Klik "Simpan Bukti" untuk menyimpan
5. Lihat bukti yang sudah diupload di section "Bukti Pengiriman"
6. Klik pada thumbnail untuk melihat gambar dalam ukuran penuh

### For Administrators
- Bukti pengiriman dapat dilihat di Driver Delivery Detail page
- Media files disimpan menggunakan Spatie Media Library
- Automatic thumbnail dan preview generation
- File validation dan size limits

## Technical Implementation

### Media Collections
Setiap jenis bukti memiliki media collection terpisah:
```php
$pengirimanDriver->addMedia($file)->toMediaCollection('foto_pemutusan_segel_atas');
```

### File Conversions
- **thumb**: 150x150px untuk grid display
- **preview**: 300x300px untuk modal preview

### Validation Rules
- File types: image/jpeg, image/png
- Max file size: 10MB
- Max files per type: 3-5 depending on evidence type
- Required: foto_pembongkaran (minimum 3 photos)

## Security Considerations

- File upload validation
- User authentication required
- Driver can only upload to their own delivery orders
- Soft deletes for audit trail
- Media library handles secure file storage

## Future Enhancements

1. **Bulk Upload**: Upload multiple files at once
2. **Image Compression**: Automatic image optimization
3. **GPS Coordinates**: Capture location data with photos
4. **Timestamp Validation**: Ensure photos are taken during delivery timeframe
5. **Admin Review**: Approval workflow for uploaded evidence
6. **Export Functionality**: Download all evidence as ZIP file
7. **Mobile Optimization**: Better mobile camera integration

## Testing

Run the seeder untuk testing:
```bash
php artisan db:seed --class=DeliveryEvidenceSeeder
```

Seeder akan membuat sample images untuk testing purposes.
