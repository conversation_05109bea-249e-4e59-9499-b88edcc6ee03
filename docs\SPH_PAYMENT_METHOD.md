# SPH Payment Method Integration

## Overview
Sistem SPH telah diupdate untuk mendukung metode pembayaran yang dapat dipilih dari master data payment methods. Fitur ini memungkinkan setiap SPH memiliki metode pembayaran yang spesifik dan akan ditampilkan di preview dan PDF.

## Changes Made

### 1. Database Migration
- **File**: `database/migrations/2025_09_04_120724_add_payment_method_id_to_sph_table.php`
- **Purpose**: Menambahkan kolom `payment_method_id` ke tabel `sph`
- **Features**:
  - Foreign key ke tabel `payment_methods`
  - Nullable (opsional)
  - Cascade delete: `nullOnDelete()`

### 2. Model Updates
- **File**: `app/Models/Sph.php`
- **Changes**:
  - Menambahkan `payment_method_id` ke `$fillable` array
  - Menambahkan relationship `paymentMethod()` ke model `PaymentMethod`
  - Import model `PaymentMethod`

### 3. Filament Resource Updates
- **File**: `app/Filament/Resources/SphResource.php`
- **Changes**:
  - Menambahkan field `Select` untuk payment method di form
  - Menambahkan kolom payment method di tabel list
  - Field bersifat nullable dan searchable

### 4. Template Updates
Semua template SPH telah diupdate untuk menampilkan informasi payment method:

#### Preview Templates:
- `resources/views/sph/sph-preview-id.blade.php`
- `resources/views/sph/sph-preview-en.blade.php`

#### PDF Templates:
- `resources/views/sph/sph-pdf-id.blade.php`
- `resources/views/sph/sph-pdf-en.blade.php`

## Features

### 1. Payment Method Selection
- Dropdown di form SPH untuk memilih metode pembayaran
- Data diambil dari master data `payment_methods` yang aktif
- Field bersifat opsional (nullable)

### 2. Display in Templates
Informasi yang ditampilkan:
- **Nama metode pembayaran** (bold)
- **Deskripsi** (jika ada)
- **Nomor rekening** (jika ada)
- **Nama pemilik rekening** (jika ada)

### 3. Multi-language Support
- **Indonesia**: "Metode Pembayaran", "No. Rekening", "A.n."
- **English**: "Payment Method", "Account No", "On behalf of"

### 4. Conditional Display
- Section payment method hanya ditampilkan jika SPH memiliki payment method
- Menggunakan `@if($record->paymentMethod)` untuk conditional rendering

## Template Structure

### Preview Templates
```blade
{{-- Payment Method --}}
@if($record->paymentMethod)
<section class="mb-6 text-sm">
    <h2 class="font-semibold">Metode Pembayaran</h2>
    <div class="mt-2">
        <p><strong>{{ $record->paymentMethod->name }}</strong></p>
        @if($record->paymentMethod->description)
            <p class="text-gray-600 mt-1">{{ $record->paymentMethod->description }}</p>
        @endif
        @if($record->paymentMethod->account_number)
            <div class="mt-2">
                <p class="font-semibold">No. Rekening: {{ $record->paymentMethod->account_number }}</p>
                @if($record->paymentMethod->account_name)
                    <p>A.n. {{ $record->paymentMethod->account_name }}</p>
                @endif
            </div>
        @endif
    </div>
</section>
@endif
```

### PDF Templates
```blade
{{-- Payment Method --}}
@if($record->paymentMethod)
<div class="payment-method" style="margin-bottom: 12px;">
    <h2>Metode Pembayaran</h2>
    <div style="font-size: 11px; margin-top: 4px;">
        <p style="margin: 0; font-weight: bold;">{{ $record->paymentMethod->name }}</p>
        @if($record->paymentMethod->description)
            <p style="margin: 2px 0; color: #666;">{{ $record->paymentMethod->description }}</p>
        @endif
        @if($record->paymentMethod->account_number)
            <div style="margin-top: 4px;">
                <p style="margin: 0; font-weight: bold;">No. Rekening: {{ $record->paymentMethod->account_number }}</p>
                @if($record->paymentMethod->account_name)
                    <p style="margin: 0;">A.n. {{ $record->paymentMethod->account_name }}</p>
                @endif
            </div>
        @endif
    </div>
</div>
@endif
```

## Usage

### 1. Creating SPH with Payment Method
1. Buka form create/edit SPH
2. Pilih metode pembayaran dari dropdown "Metode Pembayaran"
3. Field ini opsional, bisa dikosongkan
4. Save SPH

### 2. Viewing Payment Method
- Payment method akan muncul di preview SPH (sebelum Terms & Conditions)
- Payment method akan muncul di PDF SPH
- Informasi lengkap termasuk nomor rekening akan ditampilkan

### 3. Managing Payment Methods
- Payment methods dikelola di master data `PaymentMethod`
- Hanya payment methods yang aktif (`is_active = true`) yang muncul di dropdown
- Perubahan di master data akan langsung terrefleksi di SPH

## Database Schema

### SPH Table Addition
```sql
ALTER TABLE `sph` ADD `payment_method_id` BIGINT UNSIGNED NULL AFTER `letter_setting_id`;
ALTER TABLE `sph` ADD CONSTRAINT `sph_payment_method_id_foreign` 
    FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE SET NULL;
```

## Benefits

1. **Flexibility**: Setiap SPH dapat memiliki metode pembayaran yang berbeda
2. **Consistency**: Data payment method tersentralisasi di master data
3. **Professional**: Informasi pembayaran yang lengkap dan terstruktur
4. **Multi-language**: Mendukung tampilan dalam bahasa Indonesia dan Inggris
5. **Optional**: Field bersifat opsional, tidak wajib diisi

## Next Steps

1. Pastikan master data payment methods sudah terisi dengan data yang benar
2. Test create/edit SPH dengan memilih payment method
3. Verify tampilan di preview dan PDF SPH
4. Train user untuk menggunakan fitur payment method di SPH
