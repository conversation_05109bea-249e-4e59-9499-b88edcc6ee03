<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Support\Utils;
use <PERSON><PERSON>\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"super_admin","guard_name":"web","permissions":["view_user","view_any_user","create_user","update_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","restore_user","restore_any_user","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_province","view_any_province","create_province","update_province","delete_province","delete_any_province","force_delete_province","force_delete_any_province","restore_province","restore_any_province","view_regency","view_any_regency","create_regency","update_regency","delete_regency","delete_any_regency","force_delete_regency","force_delete_any_regency","restore_regency","restore_any_regency","view_district","view_any_district","create_district","update_district","delete_district","delete_any_district","force_delete_district","force_delete_any_district","restore_district","restore_any_district","view_subdistrict","view_any_subdistrict","create_subdistrict","update_subdistrict","delete_subdistrict","delete_any_subdistrict","force_delete_subdistrict","force_delete_any_subdistrict","restore_subdistrict","restore_any_subdistrict","view_akun","view_any_akun","create_akun","update_akun","delete_akun","delete_any_akun","force_delete_akun","force_delete_any_akun","restore_akun","restore_any_akun","view_item","view_any_item","create_item","update_item","delete_item","delete_any_item","force_delete_item","force_delete_any_item","restore_item","restore_any_item","view_tbbm","view_any_tbbm","create_tbbm","update_tbbm","delete_tbbm","delete_any_tbbm","force_delete_tbbm","force_delete_any_tbbm","restore_tbbm","restore_any_tbbm","view_pelanggan","view_any_pelanggan","create_pelanggan","update_pelanggan","delete_pelanggan","delete_any_pelanggan","force_delete_pelanggan","force_delete_any_pelanggan","restore_pelanggan","restore_any_pelanggan","view_supplier","view_any_supplier","create_supplier","update_supplier","delete_supplier","delete_any_supplier","force_delete_supplier","force_delete_any_supplier","restore_supplier","restore_any_supplier","view_kendaraan","view_any_kendaraan","create_kendaraan","update_kendaraan","delete_kendaraan","delete_any_kendaraan","force_delete_kendaraan","force_delete_any_kendaraan","restore_kendaraan","restore_any_kendaraan","replicate_akun","reorder_akun","view_delivery::order","view_any_delivery::order","create_delivery::order","update_delivery::order","restore_delivery::order","restore_any_delivery::order","replicate_delivery::order","reorder_delivery::order","delete_delivery::order","delete_any_delivery::order","force_delete_delivery::order","force_delete_any_delivery::order","replicate_district","reorder_district","view_expense::request","view_any_expense::request","create_expense::request","update_expense::request","restore_expense::request","restore_any_expense::request","replicate_expense::request","reorder_expense::request","delete_expense::request","delete_any_expense::request","force_delete_expense::request","force_delete_any_expense::request","view_faktur::pajak","view_any_faktur::pajak","create_faktur::pajak","update_faktur::pajak","restore_faktur::pajak","restore_any_faktur::pajak","replicate_faktur::pajak","reorder_faktur::pajak","delete_faktur::pajak","delete_any_faktur::pajak","force_delete_faktur::pajak","force_delete_any_faktur::pajak","replicate_item","reorder_item","replicate_kendaraan","reorder_kendaraan","replicate_pelanggan","reorder_pelanggan","view_pengiriman::driver","view_any_pengiriman::driver","create_pengiriman::driver","update_pengiriman::driver","restore_pengiriman::driver","restore_any_pengiriman::driver","replicate_pengiriman::driver","reorder_pengiriman::driver","delete_pengiriman::driver","delete_any_pengiriman::driver","force_delete_pengiriman::driver","force_delete_any_pengiriman::driver","replicate_province","reorder_province","replicate_regency","reorder_regency","replicate_subdistrict","reorder_subdistrict","replicate_supplier","reorder_supplier","replicate_surat","reorder_surat","replicate_tbbm","reorder_tbbm","view_transaksi::penjualan","view_any_transaksi::penjualan","create_transaksi::penjualan","update_transaksi::penjualan","restore_transaksi::penjualan","restore_any_transaksi::penjualan","replicate_transaksi::penjualan","reorder_transaksi::penjualan","delete_transaksi::penjualan","delete_any_transaksi::penjualan","force_delete_transaksi::penjualan","force_delete_any_transaksi::penjualan","view_uang::jalan","view_any_uang::jalan","create_uang::jalan","update_uang::jalan","restore_uang::jalan","restore_any_uang::jalan","replicate_uang::jalan","reorder_uang::jalan","delete_uang::jalan","delete_any_uang::jalan","force_delete_uang::jalan","force_delete_any_uang::jalan","replicate_user","reorder_user"]},{"name":"admin","guard_name":"web","permissions":["view_user","view_any_user","restore_user","restore_any_user","view_role","view_any_role","restore_role","restore_any_role","view_province","view_any_province","create_province","update_province","delete_province","delete_any_province","force_delete_province","force_delete_any_province","restore_province","restore_any_province","view_regency","view_any_regency","create_regency","update_regency","delete_regency","delete_any_regency","force_delete_regency","force_delete_any_regency","restore_regency","restore_any_regency","view_district","view_any_district","create_district","update_district","delete_district","delete_any_district","force_delete_district","force_delete_any_district","restore_district","restore_any_district","view_subdistrict","view_any_subdistrict","create_subdistrict","update_subdistrict","delete_subdistrict","delete_any_subdistrict","force_delete_subdistrict","force_delete_any_subdistrict","restore_subdistrict","restore_any_subdistrict","view_entitas_tipe","view_any_entitas_tipe","create_entitas_tipe","update_entitas_tipe","delete_entitas_tipe","delete_any_entitas_tipe","force_delete_entitas_tipe","force_delete_any_entitas_tipe","restore_entitas_tipe","restore_any_entitas_tipe","view_jabatan","view_any_jabatan","create_jabatan","update_jabatan","delete_jabatan","delete_any_jabatan","force_delete_jabatan","force_delete_any_jabatan","restore_jabatan","restore_any_jabatan","view_divisi","view_any_divisi","create_divisi","update_divisi","delete_divisi","delete_any_divisi","force_delete_divisi","force_delete_any_divisi","restore_divisi","restore_any_divisi","view_item_kategori","view_any_item_kategori","create_item_kategori","update_item_kategori","delete_item_kategori","delete_any_item_kategori","force_delete_item_kategori","force_delete_any_item_kategori","restore_item_kategori","restore_any_item_kategori","view_satuan_dasar","view_any_satuan_dasar","create_satuan_dasar","update_satuan_dasar","delete_satuan_dasar","delete_any_satuan_dasar","force_delete_satuan_dasar","force_delete_any_satuan_dasar","restore_satuan_dasar","restore_any_satuan_dasar","view_akun","view_any_akun","create_akun","update_akun","delete_akun","delete_any_akun","force_delete_akun","force_delete_any_akun","restore_akun","restore_any_akun","view_item","view_any_item","create_item","update_item","delete_item","delete_any_item","force_delete_item","force_delete_any_item","restore_item","restore_any_item","view_tbbm","view_any_tbbm","create_tbbm","update_tbbm","delete_tbbm","delete_any_tbbm","force_delete_tbbm","force_delete_any_tbbm","restore_tbbm","restore_any_tbbm","view_pelanggan","view_any_pelanggan","create_pelanggan","update_pelanggan","delete_pelanggan","delete_any_pelanggan","force_delete_pelanggan","force_delete_any_pelanggan","restore_pelanggan","restore_any_pelanggan","view_alamat_pelanggan","view_any_alamat_pelanggan","create_alamat_pelanggan","update_alamat_pelanggan","delete_alamat_pelanggan","delete_any_alamat_pelanggan","force_delete_alamat_pelanggan","force_delete_any_alamat_pelanggan","restore_alamat_pelanggan","restore_any_alamat_pelanggan","view_supplier","view_any_supplier","create_supplier","update_supplier","delete_supplier","delete_any_supplier","force_delete_supplier","force_delete_any_supplier","restore_supplier","restore_any_supplier","view_kendaraan","view_any_kendaraan","create_kendaraan","update_kendaraan","delete_kendaraan","delete_any_kendaraan","force_delete_kendaraan","force_delete_any_kendaraan","restore_kendaraan","restore_any_kendaraan","view_transaksi_penjualan","view_any_transaksi_penjualan","create_transaksi_penjualan","update_transaksi_penjualan","delete_transaksi_penjualan","delete_any_transaksi_penjualan","force_delete_transaksi_penjualan","force_delete_any_transaksi_penjualan","restore_transaksi_penjualan","restore_any_transaksi_penjualan","view_penjualan_detail","view_any_penjualan_detail","create_penjualan_detail","update_penjualan_detail","delete_penjualan_detail","delete_any_penjualan_detail","force_delete_penjualan_detail","force_delete_any_penjualan_detail","restore_penjualan_detail","restore_any_penjualan_detail","view_delivery_order","view_any_delivery_order","create_delivery_order","update_delivery_order","delete_delivery_order","delete_any_delivery_order","force_delete_delivery_order","force_delete_any_delivery_order","restore_delivery_order","restore_any_delivery_order","view_pengiriman_driver","view_any_pengiriman_driver","create_pengiriman_driver","update_pengiriman_driver","delete_pengiriman_driver","delete_any_pengiriman_driver","force_delete_pengiriman_driver","force_delete_any_pengiriman_driver","restore_pengiriman_driver","restore_any_pengiriman_driver","view_uang_jalan","view_any_uang_jalan","create_uang_jalan","update_uang_jalan","delete_uang_jalan","delete_any_uang_jalan","force_delete_uang_jalan","force_delete_any_uang_jalan","restore_uang_jalan","restore_any_uang_jalan","view_expense_request","view_any_expense_request","create_expense_request","update_expense_request","delete_expense_request","delete_any_expense_request","force_delete_expense_request","force_delete_any_expense_request","restore_expense_request","restore_any_expense_request","view_surat","view_any_surat","create_surat","update_surat","delete_surat","delete_any_surat","force_delete_surat","force_delete_any_surat","restore_surat","restore_any_surat"]},{"name":"manager","guard_name":"web","permissions":["view_item","view_any_item","create_item","update_item","delete_item","delete_any_item","force_delete_item","force_delete_any_item","restore_item","restore_any_item","view_tbbm","view_any_tbbm","create_tbbm","update_tbbm","delete_tbbm","delete_any_tbbm","force_delete_tbbm","force_delete_any_tbbm","restore_tbbm","restore_any_tbbm","view_pelanggan","view_any_pelanggan","create_pelanggan","update_pelanggan","delete_pelanggan","delete_any_pelanggan","force_delete_pelanggan","force_delete_any_pelanggan","restore_pelanggan","restore_any_pelanggan","view_alamat_pelanggan","view_any_alamat_pelanggan","create_alamat_pelanggan","update_alamat_pelanggan","delete_alamat_pelanggan","delete_any_alamat_pelanggan","force_delete_alamat_pelanggan","force_delete_any_alamat_pelanggan","restore_alamat_pelanggan","restore_any_alamat_pelanggan","view_supplier","view_any_supplier","create_supplier","update_supplier","delete_supplier","delete_any_supplier","force_delete_supplier","force_delete_any_supplier","restore_supplier","restore_any_supplier","view_kendaraan","view_any_kendaraan","create_kendaraan","update_kendaraan","delete_kendaraan","delete_any_kendaraan","force_delete_kendaraan","force_delete_any_kendaraan","restore_kendaraan","restore_any_kendaraan","view_transaksi_penjualan","view_any_transaksi_penjualan","create_transaksi_penjualan","update_transaksi_penjualan","delete_transaksi_penjualan","delete_any_transaksi_penjualan","force_delete_transaksi_penjualan","force_delete_any_transaksi_penjualan","restore_transaksi_penjualan","restore_any_transaksi_penjualan","view_penjualan_detail","view_any_penjualan_detail","create_penjualan_detail","update_penjualan_detail","delete_penjualan_detail","delete_any_penjualan_detail","force_delete_penjualan_detail","force_delete_any_penjualan_detail","restore_penjualan_detail","restore_any_penjualan_detail","view_delivery_order","view_any_delivery_order","create_delivery_order","update_delivery_order","delete_delivery_order","delete_any_delivery_order","force_delete_delivery_order","force_delete_any_delivery_order","restore_delivery_order","restore_any_delivery_order","view_pengiriman_driver","view_any_pengiriman_driver","create_pengiriman_driver","update_pengiriman_driver","delete_pengiriman_driver","delete_any_pengiriman_driver","force_delete_pengiriman_driver","force_delete_any_pengiriman_driver","restore_pengiriman_driver","restore_any_pengiriman_driver","view_uang_jalan","view_any_uang_jalan","create_uang_jalan","update_uang_jalan","delete_uang_jalan","delete_any_uang_jalan","force_delete_uang_jalan","force_delete_any_uang_jalan","restore_uang_jalan","restore_any_uang_jalan","view_expense_request","view_any_expense_request","create_expense_request","update_expense_request","delete_expense_request","delete_any_expense_request","force_delete_expense_request","force_delete_any_expense_request","restore_expense_request","restore_any_expense_request","view_surat","view_any_surat","create_surat","update_surat","delete_surat","delete_any_surat","force_delete_surat","force_delete_any_surat","restore_surat","restore_any_surat"]},{"name":"staff","guard_name":"web","permissions":["view_user","view_any_user","create_user","update_user","view_role","view_any_role","create_role","update_role","view_province","view_any_province","create_province","update_province","view_regency","view_any_regency","create_regency","update_regency","view_district","view_any_district","create_district","update_district","view_subdistrict","view_any_subdistrict","create_subdistrict","update_subdistrict","view_entitas_tipe","view_any_entitas_tipe","create_entitas_tipe","update_entitas_tipe","view_jabatan","view_any_jabatan","create_jabatan","update_jabatan","view_divisi","view_any_divisi","create_divisi","update_divisi","view_item_kategori","view_any_item_kategori","create_item_kategori","update_item_kategori","view_satuan_dasar","view_any_satuan_dasar","create_satuan_dasar","update_satuan_dasar","view_akun","view_any_akun","create_akun","update_akun","view_item","view_any_item","create_item","update_item","view_tbbm","view_any_tbbm","create_tbbm","update_tbbm","view_pelanggan","view_any_pelanggan","create_pelanggan","update_pelanggan","view_alamat_pelanggan","view_any_alamat_pelanggan","create_alamat_pelanggan","update_alamat_pelanggan","view_supplier","view_any_supplier","create_supplier","update_supplier","view_kendaraan","view_any_kendaraan","create_kendaraan","update_kendaraan","view_transaksi_penjualan","view_any_transaksi_penjualan","create_transaksi_penjualan","update_transaksi_penjualan","view_penjualan_detail","view_any_penjualan_detail","create_penjualan_detail","update_penjualan_detail","view_delivery_order","view_any_delivery_order","create_delivery_order","update_delivery_order","view_pengiriman_driver","view_any_pengiriman_driver","create_pengiriman_driver","update_pengiriman_driver","view_uang_jalan","view_any_uang_jalan","create_uang_jalan","update_uang_jalan","view_expense_request","view_any_expense_request","create_expense_request","update_expense_request","view_surat","view_any_surat","create_surat","update_surat"]},{"name":"driver","guard_name":"web","permissions":["view_delivery_order","view_any_delivery_order","update_delivery_order","view_pengiriman_driver","view_any_pengiriman_driver","create_pengiriman_driver","update_pengiriman_driver","view_uang_jalan","view_any_uang_jalan"]}]';
        $directPermissions = '{"16":{"name":"force_delete_role","guard_name":"web"},"17":{"name":"force_delete_any_role","guard_name":"web"}}';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}
