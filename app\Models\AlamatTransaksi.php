<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AlamatTransaksi extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'alamat_transaksi';

    protected $fillable = [
        'id_transaksi_penjualan',
        'alamat',
        'location',
        'keterangan',
        'urutan',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'urutan' => 'integer',
        'location' => 'array',
    ];

    /**
     * Relationships
     */
    public function transaksiPenjualan(): BelongsTo
    {
        return $this->belongsTo(TransaksiPenjualan::class, 'id_transaksi_penjualan');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scopes
     */
    public function scopeByTransaksi($query, int $transaksiId)
    {
        return $query->where('id_transaksi_penjualan', $transaksiId);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('urutan')->orderBy('created_at');
    }

    /**
     * Static methods
     */
    public static function getNextUrutan(int $transaksiId): int
    {
        return self::where('id_transaksi_penjualan', $transaksiId)->max('urutan') + 1;
    }

    /**
     * Accessors
     */
    public function getFormattedAlamatAttribute(): string
    {
        $alamat = $this->alamat;
        if ($this->keterangan) {
            $alamat .= ' (' . $this->keterangan . ')';
        }
        return $alamat;
    }

    /**
     * Check if address has coordinates
     */
    public function hasCoordinates(): bool
    {
        return !is_null($this->location) && is_array($this->location) &&
            isset($this->location['lat']) && isset($this->location['lng']);
    }

    /**
     * Boot method for auto-setting user fields
     */
    protected static function booted(): void
    {
        static::creating(function (AlamatTransaksi $alamat) {
            if (auth()->check()) {
                $alamat->created_by = auth()->id();
            }

            // Auto-set urutan if not provided
            if (empty($alamat->urutan)) {
                $alamat->urutan = self::getNextUrutan($alamat->id_transaksi_penjualan);
            }
        });

        static::updating(function (AlamatTransaksi $alamat) {
            if (auth()->check()) {
                $alamat->updated_by = auth()->id();
            }
        });
    }
}
