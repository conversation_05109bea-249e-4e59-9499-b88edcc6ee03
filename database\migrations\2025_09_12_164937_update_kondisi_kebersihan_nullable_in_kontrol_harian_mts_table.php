<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kontrol_harian_m_t_s', function (Blueprint $table) {
            // Make kondisi_kebersihan column nullable
            $table->enum('kondisi_kebersihan', ['bersih', 'kurang_bersih'])->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kontrol_harian_m_t_s', function (Blueprint $table) {
            // Make kondisi_kebersihan column not nullable again
            $table->enum('kondisi_kebersihan', ['bersih', 'kurang_bersih'])->nullable(false)->change();
        });
    }
};
