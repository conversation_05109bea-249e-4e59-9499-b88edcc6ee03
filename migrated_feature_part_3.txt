# AKUNTANSI MODULE MIGRATION - PART 3: FILAMENT RESOURCES
# This file contains all Filament Resources for the Accounting Module

# ============================================================================
# FILE: app/Filament/Resources/AkunResource.php
# ============================================================================
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AkunResource\Pages;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AkunResource extends Resource
{
    protected static ?string $model = Akun::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Chart of Accounts';

    protected static ?string $modelLabel = 'Akun';

    protected static ?string $pluralModelLabel = 'Chart of Accounts';

    protected static ?string $navigationGroup = 'Akuntansi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kode_akun')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_akun')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('kategori_akun')
                    ->required()
                    ->options([
                        'Aset' => 'Aset',
                        'Kewajiban' => 'Kewajiban',
                        'Ekuitas' => 'Ekuitas',
                        'Pendapatan' => 'Pendapatan',
                        'Beban' => 'Beban',
                    ])
                    ->native(false),
                Forms\Components\Select::make('tipe_akun')
                    ->required()
                    ->options([
                        'Debit' => 'Debit',
                        'Kredit' => 'Kredit',
                    ])
                    ->native(false),
                Forms\Components\TextInput::make('saldo_awal')
                    ->numeric()
                    ->prefix('Rp')
                    ->step(0.01)
                    ->default(0),
                Forms\Components\Hidden::make('created_by')
                    ->default(auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_akun')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('nama_akun')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('kategori_akun')
                    ->colors([
                        'primary' => 'Aset',
                        'danger' => 'Kewajiban',
                        'success' => 'Ekuitas',
                        'warning' => 'Pendapatan',
                        'secondary' => 'Beban',
                    ]),
                Tables\Columns\BadgeColumn::make('tipe_akun')
                    ->colors([
                        'success' => 'Debit',
                        'danger' => 'Kredit',
                    ]),
                Tables\Columns\TextColumn::make('saldo_awal')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('kategori_akun')
                    ->options([
                        'Aset' => 'Aset',
                        'Kewajiban' => 'Kewajiban',
                        'Ekuitas' => 'Ekuitas',
                        'Pendapatan' => 'Pendapatan',
                        'Beban' => 'Beban',
                    ]),
                Tables\Filters\SelectFilter::make('tipe_akun')
                    ->options([
                        'Debit' => 'Debit',
                        'Kredit' => 'Kredit',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAkuns::route('/'),
            'create' => Pages\CreateAkun::route('/create'),
            'edit' => Pages\EditAkun::route('/{record}/edit'),
        ];
    }
}

# ============================================================================
# FILE: app/Filament/Resources/AkunResource/Pages/ListAkuns.php
# ============================================================================
<?php

namespace App\Filament\Resources\AkunResource\Pages;

use App\Filament\Resources\AkunResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAkuns extends ListRecords
{
    protected static string $resource = AkunResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

# ============================================================================
# FILE: app/Filament/Resources/AkunResource/Pages/CreateAkun.php
# ============================================================================
<?php

namespace App\Filament\Resources\AkunResource\Pages;

use App\Filament\Resources\AkunResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAkun extends CreateRecord
{
    protected static string $resource = AkunResource::class;
}

# ============================================================================
# FILE: app/Filament/Resources/AkunResource/Pages/EditAkun.php
# ============================================================================
<?php

namespace App\Filament\Resources\AkunResource\Pages;

use App\Filament\Resources\AkunResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAkun extends EditRecord
{
    protected static string $resource = AkunResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}

# ============================================================================
# FILE: app/Filament/Resources/JournalResource.php
# ============================================================================
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JournalResource\Pages;
use App\Models\Journal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class JournalResource extends Resource
{
    protected static ?string $model = Journal::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationLabel = 'Jurnal';

    protected static ?string $modelLabel = 'Jurnal';

    protected static ?string $pluralModelLabel = 'Jurnal';

    protected static ?string $navigationGroup = 'Akuntansi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('journal_number')
                    ->label('Nomor Jurnal')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\DatePicker::make('transaction_date')
                    ->label('Tanggal Transaksi')
                    ->required()
                    ->default(now()),
                Forms\Components\TextInput::make('reference_number')
                    ->label('Nomor Referensi')
                    ->maxLength(255),
                Forms\Components\Select::make('source_type')
                    ->label('Tipe Sumber')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                    ])
                    ->native(false),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'Draft' => 'Draft',
                        'Posted' => 'Posted',
                        'Cancelled' => 'Cancelled',
                        'Error' => 'Error',
                    ])
                    ->default('Draft')
                    ->native(false),
                Forms\Components\Repeater::make('journalEntries')
                    ->relationship()
                    ->schema([
                        Forms\Components\Select::make('account_id')
                            ->label('Akun')
                            ->relationship('account', 'nama_akun')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->columnSpan(2),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->columnSpan(2),
                        Forms\Components\TextInput::make('debit')
                            ->label('Debit')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('credit')
                            ->label('Credit')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Urutan')
                            ->numeric()
                            ->default(0),
                    ])
                    ->columns(3)
                    ->defaultItems(2)
                    ->addActionLabel('Tambah Entri Jurnal')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('journal_number')
                    ->label('Nomor Jurnal')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Referensi')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source_type')
                    ->label('Tipe')
                    ->badge(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'secondary' => 'Draft',
                        'success' => 'Posted',
                        'danger' => 'Cancelled',
                        'warning' => 'Error',
                    ]),
                Tables\Columns\TextColumn::make('total_debit')
                    ->label('Total Debit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->journalEntries->sum('debit')),
                Tables\Columns\TextColumn::make('total_credit')
                    ->label('Total Credit')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->journalEntries->sum('credit')),
                Tables\Columns\IconColumn::make('is_balanced')
                    ->label('Seimbang')
                    ->boolean()
                    ->getStateUsing(fn($record) => $record->isBalanced()),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'Draft' => 'Draft',
                        'Posted' => 'Posted',
                        'Cancelled' => 'Cancelled',
                        'Error' => 'Error',
                    ]),
                Tables\Filters\SelectFilter::make('source_type')
                    ->options([
                        'Sale' => 'Penjualan',
                        'Purchase' => 'Pembelian',
                        'Payment' => 'Pembayaran',
                        'Receipt' => 'Penerimaan',
                        'ManualAdjust' => 'Penyesuaian Manual',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('post')
                    ->label('Post Jurnal')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'Draft' && $record->isBalanced())
                    ->requiresConfirmation()
                    ->modalHeading('Post Jurnal')
                    ->modalDescription('Apakah Anda yakin ingin memposting jurnal ini? Jurnal yang sudah diposting tidak dapat diubah.')
                    ->action(function ($record) {
                        if (!$record->isBalanced()) {
                            Notification::make()
                                ->title('Jurnal Tidak Seimbang')
                                ->body('Jurnal tidak dapat diposting karena total debit tidak sama dengan total credit.')
                                ->danger()
                                ->send();
                            return;
                        }

                        $record->update(['status' => 'Posted']);

                        Notification::make()
                            ->title('Jurnal Berhasil Diposting')
                            ->body('Jurnal ' . $record->journal_number . ' telah berhasil diposting.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('cancel')
                    ->label('Batalkan')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn($record) => $record->status === 'Posted')
                    ->requiresConfirmation()
                    ->modalHeading('Batalkan Jurnal')
                    ->modalDescription('Apakah Anda yakin ingin membatalkan jurnal ini?')
                    ->action(function ($record) {
                        $record->update(['status' => 'Cancelled']);

                        Notification::make()
                            ->title('Jurnal Dibatalkan')
                            ->body('Jurnal ' . $record->journal_number . ' telah dibatalkan.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'Draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn($record) => $record->status === 'Draft'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJournals::route('/'),
            'create' => Pages\CreateJournal::route('/create'),
            'view' => Pages\ViewJournal::route('/{record}'),
            'edit' => Pages\EditJournal::route('/{record}/edit'),
        ];
    }
}
