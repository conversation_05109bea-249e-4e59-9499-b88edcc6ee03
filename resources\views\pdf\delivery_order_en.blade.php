<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Order - {{ $record->kode }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            margin: 20px;
        }

        .header {
            width: 100%;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .header-content {
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 25%;
            vertical-align: top;
        }

        .company-info {
            display: table-cell;
            width: 35%;
            vertical-align: top;
            text-align: center;
            padding: 0 10px;
        }

        .iso-section {
            display: table-cell;
            width: 25%;
            vertical-align: middle;
            text-align: center;
            padding: 0 10px;
        }

        .recipient-info {
            display: table-cell;
            width: 25%;
            vertical-align: top;
            text-align: right;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-tagline {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .company-contact {
            font-size: 8px;
            margin-top: 5px;
        }

        .recipient-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
            min-height: 60px;
        }

        .recipient-label {
            font-size: 9px;
            margin-bottom: 5px;
        }

        .logo-placeholder {
            border: 1px solid #000;
            width: 80px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            text-align: center;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
        }

        .do-info {
            display: table;
            width: 100%;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .do-number {
            display: table-cell;
            width: 50%;
            text-align: left;
        }

        .do-date {
            display: table-cell;
            width: 50%;
            text-align: right;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .main-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }

        .main-table td {
            min-height: 25px;
        }

        .info-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
            font-size: 10px;
        }

        .info-left,
        .info-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }

        .info-table {
            width: 100%;
        }

        .info-table td {
            padding: 2px 5px;
            border: none;
        }

        .signature-section {
            display: table;
            width: 100%;
            margin-top: 30px;
            font-size: 10px;
        }

        .signature-box {
            display: table-cell;
            width: 25%;
            text-align: center;
            vertical-align: top;
            padding: 0 5px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .signature-space {
            height: 50px;
            border-bottom: 1px solid #000;
            margin: 10px 0;
        }

        .signature-name {
            margin-top: 5px;
        }

        .notes-section {
            margin-top: 20px;
            font-size: 10px;
        }

        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notes-content {
            border: 1px solid #000;
            padding: 10px;
            min-height: 40px;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body {
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                @if (isset($logoBase64) && !empty($logoBase64))
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo" width="80" height="60">
                @else
                    <div class="logo-placeholder">
                        COMPANY<br>LOGO
                    </div>
                @endif
            </div>
            <div class="company-info">
                <div class="company-name">LINTAS RIAU PRIMA</div>
                <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                <div class="company-tagline">Fuel Agent - Fuel Transportation - Bunker Service</div>
                <div class="company-contact">
                    @if ($record->letterSetting)
                        {{ $record->letterSetting->phone_number ?? '0761-22369' }} -
                        {{ $record->letterSetting->email ?? '<EMAIL>' }}<br>
                        {{ $record->letterSetting->website ?? 'www.lintasriauprima.com' }}
                    @else
                        0761-22369 - <EMAIL><br>
                        www.lintasriauprima.com
                    @endif
                </div>
            </div>

            <!-- ISO Certifications -->
            <div class="iso-section">
                @php
                    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
                    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
                        ->whereIn('name', $isoNamesToDisplay)
                        ->get();
                @endphp

                @foreach ($isoCertifications as $cert)
                    @php
                        $logoPath = public_path('storage/' . $cert->logo_path);
                    @endphp
                    @if (file_exists($logoPath))
                        <img src="data:image/jpeg;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                            alt="{{ $cert->name }}" style="height: 40px; margin: 0 5px; display: inline-block;">
                    @endif
                @endforeach
            </div>
            <div class="recipient-info">
                <div class="recipient-box">
                    <div class="recipient-label">To:</div>
                    <div style="font-weight: bold; font-size: 11px;">
                        {{ strtoupper($record->transaksi->pelanggan->nama ?? 'PT. ANUGERAH PRAMUDITA UTAMA') }}
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        GOODS DELIVERY RECEIPT / DELIVERY ORDER (DO)
    </div>

    <!-- DO Information -->
    <div class="do-info">
        <div class="do-number">
            <strong>DO Number : {{ $record->kode ?? 'N/A' }}</strong>
        </div>
        <div class="do-date">
            <strong>LRP-Form-Ops-04/Rev 03/
                {{ $record->transaksi && $record->transaksi->created_at ? $record->transaksi->created_at->format('d M Y') : now()->format('d M Y') }}</strong>
        </div>
    </div>

    <!-- Items Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 40%;">GOODS TYPE</th>
                <th style="width: 15%;">QUANTITY</th>
                <th style="width: 25%;">SEAL NO</th>
                <th style="width: 15%;">RECEIVER/PIC</th>
                <th style="width: 15%;">SIGNATURE</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalVolume = 0;
                $itemNumber = 1;
            @endphp

            @if ($record->details && $record->details->count() > 0)
                @foreach ($record->details as $detail)
                    @php
                        $totalVolume += $detail->volume_delivered ?? 0;
                    @endphp
                    <tr>
                        <td>{{ $itemNumber++ }}</td>
                        <td style="text-align: left; padding-left: 8px;">
                            {{ $detail->item_name ?? ($detail->item->name ?? 'N/A') }}
                        </td>
                        <td>{{ number_format($detail->volume_delivered ?? 0, 0, ',', '.') }} L</td>
                        <td>
                            @if ($record->seals && $record->seals->count() > 0)
                                {{ $record->seals->pluck('nomor_segel')->implode(', ') }}
                            @else
                                {{ $record->no_segel ?? '-' }}
                            @endif
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                @endforeach
            @else
                @if ($record->transaksi && $record->transaksi->penjualanDetails)
                    @foreach ($record->transaksi->penjualanDetails as $detail)
                        @php
                            $volume = $detail->volume_item ?? 0;
                            $totalVolume += $volume;
                        @endphp
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td style="text-align: left; padding-left: 8px;">
                                {{ $detail->item->name ?? 'N/A' }}
                            </td>
                            <td>{{ number_format($volume, 0, ',', '.') }} L</td>
                            <td>
                                @if ($record->seals && $record->seals->count() > 0)
                                    {{ $record->seals->pluck('nomor_segel')->implode(', ') }}
                                @else
                                    {{ $record->no_segel ?? '-' }}
                                @endif
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td>1</td>
                        <td style="text-align: left; padding-left: 8px;">
                            Fuel Product
                        </td>
                        <td>{{ number_format($record->volume_do ?? 0, 0, ',', '.') }} L</td>
                        <td>
                            @if ($record->seals && $record->seals->count() > 0)
                                {{ $record->seals->pluck('nomor_segel')->implode(', ') }}
                            @else
                                {{ $record->no_segel ?? '-' }}
                            @endif
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                @endif
            @endif

            <!-- Empty rows for manual filling -->
            @for ($i = $itemNumber; $i <= 5; $i++)
                <tr>
                    <td>{{ $i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            @endfor

            <!-- Total row -->
            <tr style="font-weight: bold;">
                <td colspan="2" style="text-align: center;">TOTAL</td>
                <td>{{ number_format($totalVolume, 0, ',', '.') }} L</td>
                <td colspan="3"></td>
            </tr>
        </tbody>
    </table>



    <!-- Notes Section -->
    <div class="notes-section">
        <div class="notes-title">Notes:</div>
        <div class="notes-content">
            • Check the quality and quantity of goods before unloading, complaints after unloading will not be
            served<br>
            • Check all top and bottom seals must be in good condition before unloading
        </div>
    </div>

    <!-- Signature Section -->
    <div style="margin-top: 30px;">
        <!-- Top row with 4 columns to match signature boxes -->
         <div style="display: table; width: 100%; margin-bottom: 10px; font-size: 10px;">
            <div style="display: table-cell; width: 25%; text-align: center;">
                ......., /......................... 20...<br>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center;">
                {{ $record->letterSetting?->city ?? 'Pekanbaru' }}, /......................... 20...<br>
            </div>
        </div>
        <div style="display: table; width: 100%; margin-bottom: 10px; font-size: 10px;">
            <div style="display: table-cell; width: 25%; text-align: center;">
                <strong>Receiver,</strong><br>
                <span style="font-size: 9px;">Name & Signature</span>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center;">
                <strong>Security (if any),</strong><br>
                <span style="font-size: 9px;">Name & Signature</span>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center;">
                <strong>Sender,</strong><br>
                <span style="font-size: 9px;">Name & Signature</span>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center;">
                <strong>Shipper,</strong><br>
                <span style="font-size: 9px;">Name & Signature</span>
            </div>
        </div>

        <!-- Bottom row with 4 columns -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>( {{ $record->user?->name ?? '...........................' }} )</strong><br>
                    {{ $record->kendaraan?->no_pol_kendaraan ?? '.............................' }}<br>
                    Name & Signature
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-name">
                    <strong>(...........................)</strong><br>
                    Name & Signature<br>
                    or Company Stamp
                </div>
            </div>
        </div>
    </div>


</body>

</html>
