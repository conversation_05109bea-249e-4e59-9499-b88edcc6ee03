{{--
    Invoice Preview Template - Matches PDF Layout Exactly
    Location: resources/views/invoice/invoice-preview.blade.php
--}}

@php
    // Get ISO certifications
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)->get();

    // Check if this is a service type transaction
    $isServiceType = false;
    if ($record->transaksiPenjualan) {
        $isServiceType = $record->transaksiPenjualan->tipe === 'jasa';
    }

    // Calculate totals (same logic as PDF)
    $finalTotalPenjualan = $record->subtotal ?? ($record->total_amount ?? 0);
    $includePpn = $record->include_ppn ?? true;
    $includeOperasional = !$isServiceType && ($record->include_operasional_kerja ?? false);
    $includePbbkb = !$isServiceType && ($record->include_pbbkb ?? false);

    $finalTotalPajak = $includePpn ? $record->total_pajak ?? $finalTotalPenjualan * 0.11 : 0;
    $finalBiayaOperasional = $includeOperasional ? $record->biaya_operasional_kerja ?? 0 : 0;
    $finalBiayaPbbkb = $includePbbkb ? $record->biaya_pbbkb ?? 0 : 0;
    $finalBiayaOngkosAngkut = !$isServiceType ? $record->biaya_ongkos_angkut ?? 0 : 0;

    $finalTotalInvoice =
        $record->total_invoice ?? $finalTotalPenjualan + $finalTotalPajak + $finalBiayaOperasional + $finalBiayaPbbkb;

    // Fallback for demo purposes
    if ($finalTotalInvoice <= 0) {
        $finalTotalPenjualan = 100000000;
        $finalTotalPajak = $includePpn ? 11000000 : 0;
        $finalTotalInvoice = $finalTotalPenjualan + $finalTotalPajak + $finalBiayaOperasional + $finalBiayaPbbkb;
    }

    // Get logo as base64 (same as PDF)
    $logoPath = public_path('images/lrp.png');
    $logoBase64 = '';
    if (\Illuminate\Support\Facades\File::exists($logoPath)) {
        $logoBase64 = base64_encode(\Illuminate\Support\Facades\File::get($logoPath));
    }
@endphp

<style>
    .invoice-preview {
        font-family: Arial, sans-serif;
        font-size: 11px;
        line-height: 1.3;
        color: #000;
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 15px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        position: relative;
    }

    .company-section {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        flex: 1;
    }

    .company-logo {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 10px;
        text-align: center;
        line-height: 1.1;
        flex-shrink: 0;
        overflow: hidden;
    }

    .company-logo img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .company-logo-fallback {
        background: linear-gradient(135deg, #1e40af, #3b82f6);
        color: white;
    }

    .company-info {
        flex: 1;
        margin-left: 10px;
    }

    .company-name {
        font-size: 16px;
        font-weight: bold;
        color: #1e40af;
        margin-bottom: 3px;
    }

    .company-tagline {
        font-size: 10px;
        color: #000;
        font-weight: bold;
        margin-bottom: 2px;
    }

    .company-services {
        font-size: 9px;
        color: #666;
        margin-bottom: 8px;
    }

    .header-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 120px;
        height: 80px;
        background-color: #1e40af;
    }

    .invoice-title-section {
        text-align: center;
        margin: 15px 0;
    }

    .invoice-title {
        font-size: 24px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
        text-decoration: underline;
    }

    .invoice-number {
        font-size: 11px;
        color: #000;
    }

    .customer-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .customer-left,
    .customer-right {
        width: 48%;
    }

    .detail-row {
        display: flex;
        margin-bottom: 3px;
        font-size: 10px;
    }

    .detail-label {
        width: 140px;
        color: #000;
        flex-shrink: 0;
    }

    .detail-colon {
        width: 15px;
        text-align: center;
        flex-shrink: 0;
    }

    .detail-value {
        flex: 1;
        color: #000;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
        font-size: 10px;
    }

    .items-table th {
        background-color: #fff;
        border: 1px solid #000;
        padding: 6px 4px;
        text-align: center;
        font-weight: bold;
        color: #000;
        font-size: 10px;
    }

    .items-table td {
        border: 1px solid #000;
        padding: 6px 4px;
        text-align: center;
        font-size: 10px;
        color: #000;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .totals-row {
        background-color: #f8f9fa;
    }

    .totals-row td {
        font-weight: bold;
    }

    /* Terbilang Section */
    .terbilang-section {
        margin: 15px 0;
        font-size: 11px;
        border: 1px solid #000;
        padding: 8px;
    }

    /* Payment Notes */
    .payment-notes {
        margin: 15px 0;
        font-size: 10px;
        line-height: 1.5;
    }

    /* Signature Section */
    .signature-section {
        display: flex;
        justify-content: flex-end;
        margin-top: 40px;
    }

    .signature-box {
        text-align: center;
        width: 200px;
    }

    .signature-location {
        margin-bottom: 10px;
        font-size: 11px;
    }

    .signature-space {
        height: 80px;
        margin: 20px 0;
    }

    .signature-name {
        font-weight: bold;
        font-size: 11px;
        margin-bottom: 3px;
    }

    .signature-title {
        font-size: 10px;
    }

    .footer {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 9px;
        border-top: 1px solid #000;
        padding-top: 10px;
    }

    .footer-left {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .footer-logo {
        width: 40px;
        height: 40px;
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
    }

    .footer-logo img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .footer-company {
        font-weight: bold;
    }

    .footer-contact {
        text-align: right;
    }

    /* New Footer Section */
    .footer {
        margin-top: 30px;
        border-top: 1px solid #ddd;
        padding-top: 15px;
        font-size: 10px;
    }

    .footer table {
        width: 100%;
        border-collapse: collapse;
    }

    .footer td {
        vertical-align: top;
        padding: 5px;
    }

    .footer .center {
        text-align: center;
    }

    .footer .right {
        text-align: right;
    }

    .footer p {
        margin: 2px 0;
        line-height: 1.3;
    }
</style>

<div class="invoice-preview">
    <!-- Header Section -->
    <div class="header">
        <div class="company-section">
            <div class="company-logo {{ empty($logoBase64) ? 'company-logo-fallback' : '' }}">
                @if (!empty($logoBase64))
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo">
                @else
                    LINTAS<br>RIAU<br>PRIMA
                @endif
            </div>
            <div class="company-info">
                <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Service</div>
            </div>
        </div>
        <div class="header-right"></div>
    </div>

    <!-- Invoice Title -->
    <div class="invoice-title-section">
        <div class="invoice-title">INVOICE</div>
        <div class="invoice-number">{{ $record->nomor_invoice }}</div>
    </div>

    <!-- Customer Details -->
    <div class="customer-details">
        <div style="margin-bottom: 15px;">
            <div class="detail-row">
                <span class="detail-label">Nama Pelanggan</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Alamat Pelanggan</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamatUtama?->alamat ?? 'N/A') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">No Surat Pengantar Pengiriman Pertamina</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $record->transaksiPenjualan?->kode ?? 'nomor so' }}</span>
            </div>
            @php
                $deliveryOrders = $record->transaksiPenjualan?->deliveryOrders ?? collect();
                $doNumbers = $deliveryOrders->pluck('kode')->filter()->implode(', ');
            @endphp
            <div class="detail-row">
                <span class="detail-label">No Tanda Bukti</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $doNumbers ?: '15090, 15091' }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Pengiriman Barang</span>
                <span class="detail-colon">:</span>
                <span class="detail-value"></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">No PO</span>
                <span class="detail-colon">:</span>
                <span class="detail-value">{{ $record->transaksiPenjualan?->nomor_po ?? '' }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Tanggal PO</span>
                <span class="detail-colon">:</span>
                <span
                    class="detail-value">{{ $record->transaksiPenjualan?->tanggal ? $record->transaksiPenjualan->tanggal->format('Y-m-d') : '' }}</span>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">No</th>
                <th style="width: 35%;">Perincian</th>
                <th style="width: 15%;">Harga Satuan</th>
                <th style="width: 15%;">Volume</th>
                <th style="width: 15%;">PPN</th>
                <th style="width: 15%;">Jumlah</th>
            </tr>
        </thead>
        <tbody>
            @php
                $itemNumber = 1;
                $invoiceItems = $record->invoiceItems ?? collect();
                $hasInvoiceItems = $invoiceItems->isNotEmpty();

                // Calculate totals from invoice items
                $totalPenjualan = 0;
                $totalPajak = 0;
            @endphp

            @if ($hasInvoiceItems)
                @foreach ($invoiceItems as $item)
                    {{-- Main item row --}}
                    <tr>
                        <td>{{ $itemNumber++ }}</td>
                        <td class="text-left">
                            {{ $item->item_name ?? ($item->item?->name ?? 'Item tidak ditemukan') }}<br>
                            <small
                                style="color: #6b7280;">{{ $item->item_description ?? ($item->item?->description ?? '') }}</small>
                        </td>
                        <td class="text-right">Rp {{ number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                        <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                            {{ $item->unit ?? 'Liter' }}</td>
                        <td class="text-right">Rp {{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                        <td class="text-right">Rp
                            {{ number_format(($item->unit_price ?? 0) * ($item->quantity ?? 0), 0, ',', '.') }}</td>
                    </tr>
                    @php
                        $totalPenjualan += ($item->unit_price ?? 0) * ($item->quantity ?? 0);
                    @endphp

                    {{-- Operational cost row (if included) --}}
                    @if (($item->operasional_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">Biaya Operasional dikali
                                {{ number_format($item->quantity ?? 0, 2, ',', '.') }} liter</td>
                            <td class="text-right">Rp
                                {{ number_format(($item->operasional_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->operasional_amount ?? 0, 0, ',', '.') }}
                            </td>
                        </tr>
                        @php
                            $totalPenjualan += $item->operasional_amount ?? 0;
                        @endphp
                    @endif

                    {{-- PBBKB cost row (if included) --}}
                    @if (($item->pbbkb_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">PBBKB</td>
                            <td class="text-right">Rp
                                {{ number_format(($item->pbbkb_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                            </td>
                            <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->pbbkb_amount ?? 0, 0, ',', '.') }}</td>
                        </tr>
                        @php
                            $totalPenjualan += $item->pbbkb_amount ?? 0;
                        @endphp
                    @endif

                    {{-- PPN row --}}
                    @if (($item->ppn_amount ?? 0) > 0)
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">PPN</td>
                            <td class="text-right"></td>
                            <td class="text-right"></td>
                            <td class="text-right"></td>
                            <td class="text-right">Rp {{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                        </tr>
                        @php
                            $totalPajak += $item->ppn_amount ?? 0;
                        @endphp
                    @endif
                @endforeach
            @elseif ($hasDetails)
                @foreach ($details as $detail)
                    <tr>
                        <td>{{ $itemNumber++ }}</td>
                        <td class="text-left">
                            {{ $detail->item?->nama ?? ($detail->item?->name ?? 'Item tidak ditemukan') }}<br>
                            <small
                                style="color: #6b7280;">{{ $detail->item?->deskripsi ?? ($detail->item?->description ?? '') }}</small>
                        </td>
                        <td class="text-right">Rp {{ number_format($detail->harga_jual ?? 0, 0, ',', '.') }}</td>
                        <td class="text-right">{{ number_format($detail->volume_do ?? 0, 2, ',', '.') }}
                            {{ $detail->item?->satuan?->nama ?? 'Unit' }}
                        </td>
                        <td class="text-right">
                            @if ($includePpn)
                                Rp
                                {{ number_format(($detail->harga_jual ?? 0) * ($detail->volume_do ?? 0) * 0.11, 0, ',', '.') }}
                            @else
                                -
                            @endif
                        </td>
                        <td class="text-right">Rp
                            {{ number_format(($detail->harga_jual ?? 0) * ($detail->volume_do ?? 0) * ($includePpn ? 1.11 : 1), 0, ',', '.') }}
                        </td>
                    </tr>
                @endforeach
            @else
                @php
                    // Reset item number for fallback display
                    $itemNumber = 1;
                @endphp

                <!-- Main BBM Item -->
                <tr>
                    <td>{{ $itemNumber++ }}.</td>
                    <td class="text-left">
                        BBM BIOSOLAR INDUSTRI {{ number_format($totalVolume, 0, ',', '.') }} liter<br>
                        wilayah Kab. Siak Polongan, Polongan
                    </td>
                    <td class="text-right">
                        @php
                            $hargaSatuan = 0;
                            if ($hasDetails) {
                                $hargaSatuan = $details->first()->harga_jual ?? 10000;
                            } else {
                                $hargaSatuan =
                                    $subtotalAmount > 0 && $totalVolume > 0 ? $subtotalAmount / $totalVolume : 10000;
                            }
                        @endphp
                        Rp. {{ number_format($hargaSatuan, 0, ',', '.') }}
                    </td>
                    <td class="text-right">{{ number_format($totalVolume, 0, ',', '.') }} Liter</td>
                    <td class="text-right">
                        @if ($includePpn)
                            Rp. {{ number_format($subtotalAmount * 0.11, 0, ',', '.') }}
                        @else
                            --
                        @endif
                    </td>
                    <td class="text-right">Rp. {{ number_format($subtotalAmount, 0, ',', '.') }}</td>
                </tr>

                <!-- Operational Costs (if enabled) -->
                @if ($includeOperasional && $finalBiayaOperasional > 0)
                    <tr>
                        <td>{{ $itemNumber++ }}.</td>
                        <td class="text-left">
                            Operasional Kerja<br>
                            - Polongan
                        </td>
                        <td class="text-right">
                            @php
                                $hargaOperasional =
                                    $finalBiayaOperasional > 0 && $operationalVolume > 0
                                        ? $finalBiayaOperasional / $operationalVolume
                                        : 968;
                            @endphp
                            {{ number_format($hargaOperasional, 0, ',', '.') }}
                        </td>
                        <td class="text-right">
                            {{ number_format($operationalVolume, 0, ',', '.') }} liter
                        </td>
                        <td class="text-right">--</td>
                        <td class="text-right">
                            Rp. {{ number_format($finalBiayaOperasional, 0, ',', '.') }}
                        </td>
                    </tr>
                @endif

                <!-- PPN Row -->
                @if ($includePpn)
                    <tr>
                        <td>{{ $itemNumber++ }}.</td>
                        <td class="text-left">PPN</td>
                        <td class="text-right"></td>
                        <td class="text-right"></td>
                        <td class="text-right"></td>
                        <td class="text-right">Rp. {{ number_format($finalTotalPajak, 0, ',', '.') }}</td>
                    </tr>
                @endif

                <!-- PBBKB Row (if enabled) -->
                @if ($includePbbkb && $finalBiayaPbbkb > 0)
                    <tr>
                        <td>{{ $itemNumber++ }}.</td>
                        <td class="text-left">
                            PBBKB BBM Solar Industri Pertamina<br>
                            {{ number_format($finalBiayaPbbkb / 2545, 1, ',', '.') }} x
                            {{ number_format($totalVolume, 0, ',', '.') }} Liter
                        </td>
                        <td class="text-right"></td>
                        <td class="text-right"></td>
                        <td class="text-right"></td>
                        <td class="text-right">Rp. {{ number_format($finalBiayaPbbkb, 0, ',', '.') }}</td>
                    </tr>
                @endif
            @endif

            <!-- Totals within table -->
            <tr class="totals-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Penjualan</td>
                <td class="text-right" style="font-weight: bold;">Rp.
                    {{ number_format($totalPenjualan, 0, ',', '.') }}</td>
            </tr>

            <tr class="totals-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Pajak</td>
                <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($totalPajak, 0, ',', '.') }}
                </td>
            </tr>

            <tr class="final-total-row">
                <td colspan="5" style="text-align: right; font-weight: bold;">Total Invoice Termasuk Pajak</td>
                <td class="text-right" style="font-weight: bold;">Rp.
                    {{ number_format($totalPenjualan + $totalPajak, 0, ',', '.') }}</td>
            </tr>
        </tbody>
    </table>

    <!-- Terbilang Section -->
    <div class="terbilang-section">
        <strong>Terbilang :</strong>
        "{{ $record->catatan ?? 'Dua Juta Empat Ratus Enam Puluh Ribu rupiah' }}"
    </div>

    <!-- Payment Notes -->
    <div class="payment-notes">
        1. Payment transfer to account<br>
        2. After Payment please Call or Email transfer from to 0761-22369 or <EMAIL>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-location">Pekanbaru,
                {{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d F Y') : now()->format('d F Y') }}
            </div>
            <div class="signature-space" style="height: 60px;"></div>
            <div class="signature-name"><strong>Agustiawan Syahputra</strong></div>
            <div class="signature-title">Direktur Utama</div>
        </div>
    </div>

    {{-- Footer Section --}}
    <div class="footer">
        <table>
            <tr>
                <td style="width: 33%;" class="iso-logos">
                    @php
                        $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
                        $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
                            ->whereIn('name', $isoNamesToDisplay)
                            ->get();
                    @endphp

                    @foreach ($isoCertifications as $cert)
                        @php
                            $logoPath = public_path('storage/' . $cert->logo_path);
                        @endphp
                        @if (file_exists($logoPath))
                            <img src="data:image/jpeg;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                                alt="{{ $cert->name }}" style="height: 40px; margin-right: 10px;">
                        @endif
                    @endforeach
                </td>
                <td style="width: 34%;" class="center">
                    <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                    @if ($record->letterSetting)
                        <p>{{ $record->letterSetting->address }}</p>
                        <p>{{ $record->letterSetting->city }}, {{ $record->letterSetting->province }}.
                            {{ $record->letterSetting->postal_code }}</p>
                    @else
                        <p>Jl. Mesjid Al Furqon No. 26</p>
                        <p>Pekanbaru, Riau. 28144</p>
                    @endif
                </td>
                <td style="width: 33%;" class="right">
                    @if ($record->letterSetting)
                        <p>Tel: {{ $record->letterSetting->phone_number }}</p>
                        <p>Email: {{ $record->letterSetting->email }}</p>
                        <p>Web: {{ $record->letterSetting->website }}</p>
                    @else
                        <p>Tel: 0761-22369</p>
                        <p>Email: <EMAIL></p>
                        <p>Web: www.lintasriauprima.com</p>
                    @endif
                </td>
            </tr>
        </table>
    </div>
</div>
