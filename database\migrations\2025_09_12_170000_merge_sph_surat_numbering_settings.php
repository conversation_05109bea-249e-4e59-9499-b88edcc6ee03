<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\NumberingSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Merge SPH and Surat numbering settings to use the same sequence
        
        // Get existing settings
        $sphSetting = NumberingSetting::where('type', 'sph')->first();
        $suratSetting = NumberingSetting::where('type', 'surat')->first();
        
        if ($sphSetting && $suratSetting) {
            // Use the higher sequence number to continue from
            $maxSequence = max($sphSetting->last_sequence, $suratSetting->last_sequence);
            
            // Update SPH setting to have the higher sequence
            $sphSetting->update([
                'last_sequence' => $maxSequence,
                'format' => '{SEQUENCE}/{PREFIX}/{MONTH_ROMAN}/{YEAR}', // Use consistent format
            ]);
            
            // Delete the separate surat setting
            $suratSetting->delete();
        } elseif ($suratSetting && !$sphSetting) {
            // If only surat setting exists, convert it to sph
            $suratSetting->update([
                'type' => 'sph',
                'prefix' => 'SPH',
            ]);
        } elseif (!$sphSetting && !$suratSetting) {
            // Create default SPH setting if neither exists
            NumberingSetting::create([
                'type' => 'sph',
                'prefix' => 'SPH',
                'suffix' => null,
                'sequence_digits' => 4,
                'format' => '{SEQUENCE}/{PREFIX}/{MONTH_ROMAN}/{YEAR}',
                'reset_frequency' => 'yearly',
                'last_sequence' => 0,
                'last_reset_date' => now()->toDateString(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate separate surat setting if needed
        $sphSetting = NumberingSetting::where('type', 'sph')->first();
        
        if ($sphSetting) {
            NumberingSetting::create([
                'type' => 'surat',
                'prefix' => 'SURAT',
                'suffix' => null,
                'sequence_digits' => 4,
                'format' => '{SEQUENCE}/{PREFIX}/{MONTH_ROMAN}/{YEAR}',
                'reset_frequency' => 'yearly',
                'last_sequence' => $sphSetting->last_sequence,
                'last_reset_date' => $sphSetting->last_reset_date,
            ]);
        }
    }
};
