<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kontrol_harian_m_t_s', function (Blueprint $table) {
            $table->id();
            $table->date('tanggal_kontrol');
            $table->string('plat_kendaraan');
            $table->string('awak_mobil_tangki');
            $table->string('petugas_sekuriti');
            $table->string('tujuan_mt');
            $table->time('jam_keberangkatan')->nullable();
            $table->time('jam_pulang')->nullable();
            $table->string('jenis_muatan'); // Solar, Pertalite, dll

            // Pre Trip Inspection MT
            $table->boolean('perlengkapan_bongkar_muatan')->default(false);
            $table->boolean('ban_serap_ada')->default(false);
            $table->boolean('lampu_rotari_menyala')->default(false);
            $table->boolean('apar_ada')->default(false);
            $table->enum('kondisi_kebersihan', ['bersih', 'kurang_bersih']);
            $table->boolean('surat_kendaraan_dibawa')->default(false);

            // AMT Inspection
            $table->boolean('awak_mt_sehat')->default(false);
            $table->boolean('awak_mt_safety')->default(false);
            $table->boolean('kelengkapan_surat_awak_ready')->default(false);

            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['tanggal_kontrol', 'plat_kendaraan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kontrol_harian_m_t_s');
    }
};
