<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\NeracaService;
use Illuminate\Support\Facades\Auth;

class BalanceSheet extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationLabel = 'Neraca';

    protected static ?string $title = 'Laporan Neraca';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?int $navigationSort = 32;

    protected static string $view = 'filament.pages.balance-sheet';

    public ?array $data = [];
    public $start_date = null;
    public $end_date = null;
    public $period_preset = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->can('page_BalanceSheet') ?? false;
    }

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
            'period_preset' => 'current_month',
        ]);

        $this->start_date = now()->startOfMonth()->format('Y-m-d');
        $this->end_date = now()->endOfMonth()->format('Y-m-d');
        $this->period_preset = 'current_month';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Periode Laporan')
                    ->schema([
                        Forms\Components\Select::make('period_preset')
                            ->label('Periode Preset')
                            ->options([
                                'current_month' => 'Bulan Ini',
                                'last_month' => 'Bulan Lalu',
                                'current_year' => 'Tahun Ini',
                                'last_year' => 'Tahun Lalu',
                                'custom' => 'Custom',
                            ])
                            ->default('current_month')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->period_preset = $state;
                                $this->updateDatesByPreset($state);

                                // Update form data
                                $this->form->fill([
                                    'period_preset' => $this->period_preset,
                                    'start_date' => $this->start_date,
                                    'end_date' => $this->end_date,
                                ]);
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->start_date = $state;
                            })
                            ->visible(fn($get) => $get('period_preset') === 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('Tanggal Akhir')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->end_date = $state;
                            })
                            ->visible(fn($get) => $get('period_preset') === 'custom'),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function updateDatesByPreset($preset)
    {
        switch ($preset) {
            case 'current_month':
                $this->start_date = now()->startOfMonth()->format('Y-m-d');
                $this->end_date = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_month':
                $this->start_date = now()->subMonth()->startOfMonth()->format('Y-m-d');
                $this->end_date = now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'current_year':
                $this->start_date = now()->startOfYear()->format('Y-m-d');
                $this->end_date = now()->endOfYear()->format('Y-m-d');
                break;
            case 'last_year':
                $this->start_date = now()->subYear()->startOfYear()->format('Y-m-d');
                $this->end_date = now()->subYear()->endOfYear()->format('Y-m-d');
                break;
            case 'custom':
                // Don't change dates for custom
                break;
        }
    }

    public function getBalanceSheetData(): array
    {
        if (!$this->start_date || !$this->end_date) {
            return [
                'aktiva' => [
                    'lancar' => ['items' => [], 'total' => 0],
                    'tetap' => ['items' => [], 'total' => 0],
                    'total_lancar' => 0,
                    'total_tetap' => 0,
                    'total' => 0
                ],
                'pasiva' => [
                    'hutang' => ['items' => [], 'total' => 0],
                    'modal' => ['items' => [], 'total' => 0],
                    'total_hutang' => 0,
                    'total_modal' => 0,
                    'total' => 0
                ],
                'is_balanced' => false,
                'periode' => [
                    'start' => '',
                    'end' => '',
                    'title' => ''
                ]
            ];
        }

        $neracaService = new NeracaService();
        return $neracaService->generateReport($this->start_date, $this->end_date);
    }

    /**
     * Format currency for display
     */
    public function formatCurrency($amount)
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}
