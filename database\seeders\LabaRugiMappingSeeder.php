<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LabaRugiMapping;

class LabaRugiMappingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing mappings
        LabaRugiMapping::truncate();

        $mappings = [
            // PENDAPATAN USAHA
            ['kode_akun' => '400', 'nama_akun' => 'Penjualan BBM', 'kategori_laba_rugi' => 'Pendapatan Penjualan BBM', 'urutan' => 1],
            ['kode_akun' => '401', 'nama_akun' => 'OAT Penjualan BBM', 'kategori_laba_rugi' => 'Pendapatan Penjualan BBM', 'urutan' => 2],
            ['kode_akun' => '402', 'nama_akun' => 'PBBKB', 'kategori_laba_rugi' => 'Pendapatan Penjualan BBM', 'urutan' => 3],
            ['kode_akun' => '403', 'nama_akun' => 'Pendapatan Jasa Angkut', 'kategori_laba_rugi' => 'Pendapatan Penjualan BBM', 'urutan' => 4],

            // HPP
            ['kode_akun' => '500', 'nama_akun' => 'Persediaan Awal', 'kategori_laba_rugi' => 'Persediaan', 'urutan' => 10],
            ['kode_akun' => '501', 'nama_akun' => 'Harga Dasar BBM', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 11],
            ['kode_akun' => '502', 'nama_akun' => 'Rounding', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 12],
            ['kode_akun' => '503', 'nama_akun' => 'Disc Pembeliaan', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 13],
            ['kode_akun' => '504', 'nama_akun' => 'Persediaan Akhir', 'kategori_laba_rugi' => 'Persediaan', 'urutan' => 14],
            ['kode_akun' => '505', 'nama_akun' => 'PBBKB', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 15],
            ['kode_akun' => '506', 'nama_akun' => 'HPP Jasa Angkut APMS', 'kategori_laba_rugi' => 'HPP Jasa Angkut', 'urutan' => 16],
            ['kode_akun' => '507', 'nama_akun' => 'HPP BBM', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 17],

            // BEBAN UMUM / ADM / OPS
            ['kode_akun' => '600', 'nama_akun' => 'Gaji', 'kategori_laba_rugi' => 'Gaji Karyawan', 'urutan' => 20],
            ['kode_akun' => '601', 'nama_akun' => 'Upah/Honor', 'kategori_laba_rugi' => 'Gaji Karyawan', 'urutan' => 21],
            ['kode_akun' => '602', 'nama_akun' => 'THR', 'kategori_laba_rugi' => 'Gaji Karyawan', 'urutan' => 22],
            ['kode_akun' => '603', 'nama_akun' => 'Beban Ekspedisi', 'kategori_laba_rugi' => 'Ekspedisi', 'urutan' => 23],
            ['kode_akun' => '604.1', 'nama_akun' => 'ATK Kantor', 'kategori_laba_rugi' => 'ATK', 'urutan' => 24],
            ['kode_akun' => '604.2', 'nama_akun' => 'ATK Materai', 'kategori_laba_rugi' => 'ATK', 'urutan' => 25],
            ['kode_akun' => '605', 'nama_akun' => 'Beban Kesehatan', 'kategori_laba_rugi' => 'Beban kesehatan', 'urutan' => 26],
            ['kode_akun' => '606', 'nama_akun' => 'Beban Listrik', 'kategori_laba_rugi' => 'Beban Listrik', 'urutan' => 27],
            ['kode_akun' => '607', 'nama_akun' => 'Beban Telepon', 'kategori_laba_rugi' => 'Beban Utilitas', 'urutan' => 28],
            ['kode_akun' => '608.1', 'nama_akun' => 'Beban Maintanance Bangunan Kantor', 'kategori_laba_rugi' => 'Beban Maintanance Bangunan Kantor', 'urutan' => 29],
            ['kode_akun' => '608.2', 'nama_akun' => 'Beban Maintanance Bangunan pool', 'kategori_laba_rugi' => 'Beban Maintanance Bangunan pool', 'urutan' => 30],
            ['kode_akun' => '608.3', 'nama_akun' => 'Beban Maintanance Kendaraan Service', 'kategori_laba_rugi' => 'Beban Maintanance Kendaraan Service', 'urutan' => 31],
            ['kode_akun' => '608.4', 'nama_akun' => 'Beban Maintanance Kendaraan Spare Part', 'kategori_laba_rugi' => 'Beban Maintanance Kendaraan Spare Part', 'urutan' => 32],
            ['kode_akun' => '608.5', 'nama_akun' => 'Beban Maintanance Kendaraan Ban', 'kategori_laba_rugi' => 'Beban Maintanance Kendaraan Ban', 'urutan' => 33],
            ['kode_akun' => '609', 'nama_akun' => 'Beban Maintanance Inventaris', 'kategori_laba_rugi' => 'Beban Lain -lain', 'urutan' => 34],
            ['kode_akun' => '610', 'nama_akun' => 'Perjalanan Dinas', 'kategori_laba_rugi' => 'Beban Perjalanan Dinas', 'urutan' => 35],
            ['kode_akun' => '611', 'nama_akun' => 'Fee', 'kategori_laba_rugi' => 'Beban Lain -lain', 'urutan' => 36],
            ['kode_akun' => '612', 'nama_akun' => 'BBM/Tol Operasional', 'kategori_laba_rugi' => 'BBM mobil operasional kantor', 'urutan' => 37],
            ['kode_akun' => '613', 'nama_akun' => 'Beban Jasa Transport', 'kategori_laba_rugi' => 'HPP Jasa Angkut', 'urutan' => 38],
            ['kode_akun' => '614', 'nama_akun' => 'Konsumsi', 'kategori_laba_rugi' => 'Beban Lain -lain', 'urutan' => 39],
            ['kode_akun' => '615', 'nama_akun' => 'Beban Marketing', 'kategori_laba_rugi' => 'Beban Lain-lain', 'urutan' => 40],
            ['kode_akun' => '616', 'nama_akun' => 'Beban Digital Marketing', 'kategori_laba_rugi' => 'Beban lain - lain', 'urutan' => 41],
            ['kode_akun' => '617', 'nama_akun' => 'Beban Operasional', 'kategori_laba_rugi' => 'Beban Operasional', 'urutan' => 42],
            ['kode_akun' => '618', 'nama_akun' => 'Sumbangan Kelembagaan', 'kategori_laba_rugi' => 'Beban lain -lain', 'urutan' => 43],
            ['kode_akun' => '619', 'nama_akun' => 'Lisensi', 'kategori_laba_rugi' => 'Beban pengurusan izin/lisensi', 'urutan' => 44],
            ['kode_akun' => '620', 'nama_akun' => 'Entertaint', 'kategori_laba_rugi' => 'Lain-lain', 'urutan' => 45],
            ['kode_akun' => '621', 'nama_akun' => 'Beban Pralatan kantor', 'kategori_laba_rugi' => 'Beban Pralatan kantor', 'urutan' => 46],
            ['kode_akun' => '622', 'nama_akun' => 'Zakat', 'kategori_laba_rugi' => 'Beban lain -lain', 'urutan' => 47],
            ['kode_akun' => '623.1', 'nama_akun' => 'Beban Sewa Kendaraan', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 48],
            ['kode_akun' => '623.2', 'nama_akun' => 'Beban Sewa Kapal', 'kategori_laba_rugi' => 'HPP BBM', 'urutan' => 49],
            ['kode_akun' => '624', 'nama_akun' => 'Jasa Konsultasi', 'kategori_laba_rugi' => 'Beban pengurusan izin/lisensi', 'urutan' => 50],
            ['kode_akun' => '699', 'nama_akun' => 'Beban Lain-lain', 'kategori_laba_rugi' => 'Beban lain -lain', 'urutan' => 51],

            // PENDAPATAN DAN BEBAN LUAR USAHA
            ['kode_akun' => '700', 'nama_akun' => 'Pendapatan Jasa Giro', 'kategori_laba_rugi' => 'Pendapatan Jasa Giro', 'urutan' => 60],
            ['kode_akun' => '701', 'nama_akun' => 'Pendapatan Bunga Bank', 'kategori_laba_rugi' => 'Pendapatan Bunga Bank', 'urutan' => 61],
            ['kode_akun' => '799', 'nama_akun' => 'Pendapatan Lain Lain', 'kategori_laba_rugi' => 'Pendapatan Lain Lain', 'urutan' => 62],
            ['kode_akun' => '800', 'nama_akun' => 'Adm Bank', 'kategori_laba_rugi' => 'Adm Bank', 'urutan' => 63],
            ['kode_akun' => '801', 'nama_akun' => 'Pajak Bank', 'kategori_laba_rugi' => 'Pajak Bank', 'urutan' => 64],
            ['kode_akun' => '802', 'nama_akun' => 'Bunga Bank', 'kategori_laba_rugi' => 'Bunga Bank', 'urutan' => 65],

            // PAJAK
            ['kode_akun' => '900', 'nama_akun' => 'PBB', 'kategori_laba_rugi' => 'PBB', 'urutan' => 70],
            ['kode_akun' => '901', 'nama_akun' => 'PPN', 'kategori_laba_rugi' => 'PPN', 'urutan' => 71],
            ['kode_akun' => '902', 'nama_akun' => 'Pajak Pasal 21', 'kategori_laba_rugi' => 'Pajak Pasal 21', 'urutan' => 72],
            ['kode_akun' => '903', 'nama_akun' => 'Pajak Pasal 22', 'kategori_laba_rugi' => 'Pajak Pasal 22', 'urutan' => 73],
            ['kode_akun' => '904', 'nama_akun' => 'Pajak Pasal 23', 'kategori_laba_rugi' => 'Pajak Pasal 23', 'urutan' => 74],
            ['kode_akun' => '905', 'nama_akun' => 'Pajak Pasal 25', 'kategori_laba_rugi' => 'Pajak Pasal 25', 'urutan' => 75],
            ['kode_akun' => '906', 'nama_akun' => 'Pajak Pasal 29', 'kategori_laba_rugi' => 'Pajak Pasal 29', 'urutan' => 76],
            ['kode_akun' => '999', 'nama_akun' => 'Pajak Lainnnya', 'kategori_laba_rugi' => 'Pajak Lainnnya', 'urutan' => 77],
        ];

        foreach ($mappings as $mapping) {
            LabaRugiMapping::create($mapping);
        }

        $this->command->info('✅ Laba Rugi Mapping seeded successfully!');
        $this->command->info('📊 Total mappings: ' . count($mappings));
        $this->command->info('');
        $this->command->info('Categories created:');
        
        $categories = collect($mappings)->groupBy('kategori_laba_rugi');
        foreach ($categories as $category => $items) {
            $this->command->info('   - ' . $category . ': ' . $items->count() . ' accounts');
        }
    }
}
