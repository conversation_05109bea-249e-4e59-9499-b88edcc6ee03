<?php

namespace App\Filament\Resources\TransaksiPenjualanResource\Pages;

// --- Model & Resource ---
use App\Filament\Resources\TransaksiPenjualanResource;
use App\Models\TransaksiPenjualan;
use App\Models\NotificationSetting;

// --- Services ---
use App\Services\TransaksiPenjualanService;
use App\Services\MessageService;

// --- Core Filament Classes ---
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;

// --- Komponen untuk Menampilkan Data (Infolist) ---
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;

use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Joaopaulolndev\FilamentPdfViewer\Infolists\Components\PdfViewerEntry;

// --- Komponen untuk Form di dalam Action ---
use Filament\Forms\Get;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\HtmlEntry;

class ViewTransaksiPenjualan extends ViewRecord
{
    protected static string $resource = TransaksiPenjualanResource::class;

    /**
     * Check if current user is authorized to approve transactions
     */
    private function isCurrentUserAuthorizedApprover(): bool
    {
        $currentUserId = Auth::id();
        return NotificationSetting::where('event_name', 'penjualan_baru')
            ->where('user_id', $currentUserId)
            ->where('is_active', true)
            ->exists();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(function () {
                    // Only allow edit if not approved yet
                    return $this->record->status !== 'approved';
                })
                ->tooltip(function () {
                    if ($this->record->status === 'approved') {
                        return 'Transaksi yang sudah disetujui tidak dapat diedit';
                    }
                    return 'Edit transaksi penjualan';
                }),

            Actions\Action::make('create_delivery_order')
                ->label('Buat Delivery Order')
                ->icon('heroicon-o-truck')
                ->color('success')
                ->url(fn() => route('filament.admin.resources.delivery-orders.create', [
                    'id_transaksi' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if transaction is approved
                    return $this->record->status === 'approved';
                })
                ->tooltip('Buat delivery order untuk transaksi ini'),

            Actions\Action::make('create_invoice')
                ->label('Buat Invoice')
                ->icon('heroicon-o-document-plus')
                ->color('primary')
                ->url(fn() => route('filament.admin.resources.invoices.create', [
                    'id_transaksi' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if transaction is approved and no invoice exists yet
                    return $this->record->status === 'approved' && !$this->record->invoices()->exists();
                })
                ->tooltip('Buat invoice untuk transaksi ini'),

            Actions\Action::make('view_invoices')
                ->label('Lihat Invoice')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn() => route('filament.admin.resources.invoices.index', [
                    'tableFilters[transaksi_penjualan][value]' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if invoices exist
                    return $this->record->invoices()->exists();
                })
                ->tooltip('Lihat invoice yang sudah dibuat'),

            // Action untuk menampilkan status rejected
            Actions\Action::make('rejected_status')
                ->label('Transaksi Ditolak')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->disabled()
                ->visible(function () {
                    // Show if transaction is rejected
                    return $this->record->status === 'rejected';
                })
                ->tooltip(function () {
                    // Get the latest rejection note
                    $latestApproval = $this->record->approvals()
                        ->where('status', 'rejected')
                        ->latest()
                        ->first();

                    $note = $latestApproval?->note ?? 'Tidak ada catatan';
                    return "Transaksi ini ditolak. Alasan: {$note}";
                }),

            // Action untuk menampilkan status needs revision
            Actions\Action::make('needs_revision_status')
                ->label('Perlu Perbaikan')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->disabled()
                ->visible(function () {
                    // Show if transaction needs revision
                    return $this->record->status === 'needs_revision';
                })
                ->tooltip(function () {
                    // Get the latest revision note
                    $latestApproval = $this->record->approvals()
                        ->where('status', 'reject_with_perbaikan')
                        ->latest()
                        ->first();

                    $note = $latestApproval?->note ?? 'Tidak ada catatan';
                    return "Transaksi ini perlu diperbaiki. Catatan: {$note}";
                }),
            // Action untuk menampilkan status "Belum Ada Approval" (untuk authorized approver)
            Actions\Action::make('no_approval_yet')
                ->label('Belum Ada Approval')
                ->icon('heroicon-o-exclamation-circle')
                ->color('info')
                ->disabled()
                ->visible(function () {
                    // Show if pending approval, no approvals exist yet, and user is authorized approver
                    return $this->record->status === 'pending_approval'
                        && !$this->record->approvals()->exists()
                        && $this->isCurrentUserAuthorizedApprover();
                })
                ->tooltip('Transaksi ini belum diproses approval. Klik tombol "Proses Approval" untuk memulai.'),

            // Action untuk menampilkan status "Tidak Berhak Approval" (untuk non-authorized user)
            Actions\Action::make('not_authorized_approver')
                ->label('Tidak Berhak Approval')
                ->icon('heroicon-o-shield-exclamation')
                ->color('gray')
                ->disabled()
                ->visible(function () {
                    // Show if pending approval and user is NOT authorized approver
                    return $this->record->status === 'pending_approval' && !$this->isCurrentUserAuthorizedApprover();
                })
                ->tooltip('Anda tidak memiliki hak untuk memproses approval transaksi ini. Hanya user yang terdaftar sebagai approver yang dapat melakukan approval.'),

            // approval action
            Actions\Action::make('approval')
                ->label('Proses Approval')
                ->color('primary')
                ->icon('heroicon-o-check-badge')
                ->visible(function () {
                    // Only show if transaction is pending approval AND current user is authorized approver
                    return $this->record->status === 'pending_approval' && $this->isCurrentUserAuthorizedApprover();
                })
                ->form([
                    Fieldset::make('Informasi Pembuat Transaksi')
                        ->schema([
                            Placeholder::make('pembuat_nama')
                                ->label('Nama Salesperson')
                                ->content(fn(TransaksiPenjualan $record): string => $record->createdBy?->name ?? 'N/A'),

                            Placeholder::make('pembuat_hp')
                                ->label('No. Handphone')
                                ->content(fn(TransaksiPenjualan $record): string => $record->createdBy?->hp ?? 'Tidak ada nomor'),
                        ])
                        ->columns(2),
                    Radio::make('status')
                        ->label('Status Approval')
                        ->options([
                            'approved' => 'Approved (Disetujui)',
                            'reject_with_perbaikan' => 'Reject with Revision (Tolak dengan Perbaikan)',
                            'rejected' => 'Rejected (Ditolak Final)',
                        ])
                        ->required()
                        ->live(),
                    Textarea::make('note')
                        ->label('Catatan / Alasan')
                        // Required only if status is a type of rejection
                        ->required(fn(Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan']))
                        // Visible only if status is a type of rejection
                        ->visible(fn(Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan'])),
                ])
                // 2. Service class and variable name updated here
                ->action(function (TransaksiPenjualan $record, array $data, TransaksiPenjualanService $penjualanService) {
                    // The action is simple: just call the service.
                    $penjualanService->processApproval(
                        $record,
                        \Illuminate\Support\Facades\Auth::user(),
                        $data['status'],
                        // 3. IMPORTANT: Use null coalescing operator to prevent errors when 'note' is not present.
                        $data['note'] ?? null
                    );

                    Notification::make()
                        ->title('Proses approval berhasil disimpan')
                        ->success()
                        ->send();
                }),
            // Optional: Only show this button if the record needs approval
            // ->visible(fn (TransaksiPenjualan $record) => $record->status === 'pending_approval'),

            // Actions\Action::make('reject')
            //     ->label('Tolak SO'),
            Actions\Action::make('view_timeline')
                ->label('Lihat Timeline')
                ->icon('heroicon-o-clock')
                ->color('success')
                ->url(fn(TransaksiPenjualan $record): string => "/admin/sales-order-timeline-detail?record={$record->id}")
                ->openUrlInNewTab(false),

        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Transaksi Penjualan')
                    ->schema([
                        TextEntry::make('tipe')
                            ->icon('heroicon-o-document-text')
                            ->color('primary')
                            ->weight('bold')
                            ->label('Tipe Transaksi'),
                        TextEntry::make('kode')
                            ->icon('heroicon-o-document-text')
                            ->color('primary')
                            ->weight('bold')
                            ->label('Nomor SO'),
                        PdfViewerEntry::make('dokumen_so')
                            ->label('Dokumen SO')
                            ->fileUrl(function ($record) {
                                $media = $record->getFirstMedia('dokumen_so');
                                return $media ? $media->getUrl() : null;
                            })
                            ->minHeight('400px')
                            ->visible(fn($record) => $record->getMedia('dokumen_so')->count() > 0)
                            ->columnSpanFull(),

                        TextEntry::make('nomor_po')
                            ->label('Nomor PO')
                            ->icon('heroicon-o-document-text'),

                        PdfViewerEntry::make('dokumen_po')
                            ->label('Dokumen PO')
                            ->fileUrl(function ($record) {
                                $media = $record->getFirstMedia('dokumen_po');
                                return $media ? $media->getUrl() : null;
                            })
                            ->minHeight('400px')
                            ->visible(fn($record) => $record->getMedia('dokumen_po')->count() > 0)
                            ->columnSpanFull(),

                        TextEntry::make('nomor_sph')
                            ->label('Nomor SPH')
                            ->icon('heroicon-o-document-text'),

                        PdfViewerEntry::make('dokumen_sph')
                            ->label('Dokumen SPH')
                            ->fileUrl(function ($record) {
                                $media = $record->getFirstMedia('dokumen_sph');
                                return $media ? $media->getUrl() : null;
                            })
                            ->minHeight('400px')
                            ->visible(fn($record) => $record->getMedia('dokumen_sph')->count() > 0)
                            ->columnSpanFull(),
                        TextEntry::make('pelanggan.nama')
                            ->label('Pelanggan')
                            ->icon('heroicon-o-building-office'),
                        TextEntry::make('alamatPelanggan.alamat')
                            ->label('Alamat Invoice')
                            ->icon('heroicon-o-map-pin'),

                        TextEntry::make('top_pembayaran')
                            ->label('Termin Pembayaran')
                            ->formatStateUsing(fn($state) => $state ? "{$state} hari" : 'Tunai')
                            ->badge()
                            ->color(fn($state) => $state > 30 ? 'warning' : 'success'),

                        TextEntry::make('letterSetting.name')
                            ->label('Format Surat')
                            ->placeholder('Tidak Ada')
                            ->badge()
                            ->color('info'),

                        TextEntry::make('letterSetting.locale')
                            ->label('Mata Uang')
                            ->placeholder('Tidak Ada')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'id' => 'Rupiah (IDR)',
                                'en' => 'Dollar (USD)',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'id' => 'success',
                                'en' => 'warning',
                                default => 'gray'
                            }),

                        // nama dan lokasi tbbm
                        TextEntry::make('tbbm.nama')
                            ->label('Nama TBBM')
                            ->placeholder('Belum Ditentukan'),
                        TextEntry::make('tbbm.alamat')
                            ->label('Alamat TBBM')
                            ->placeholder('Belum Ditentukan'),
                    ])
                    ->columns(2),

                // Status Approval Section
                Section::make('Status Approval')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status Transaksi')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'pending_approval' => 'Menunggu Approval',
                                'approved' => 'Disetujui',
                                'rejected' => 'Ditolak',
                                'needs_revision' => 'Perlu Perbaikan',
                                default => ucfirst(str_replace('_', ' ', $state)),
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'pending_approval' => 'warning',
                                'approved' => 'success',
                                'rejected' => 'danger',
                                'needs_revision' => 'info',
                                default => 'gray'
                            })
                            ->icon(fn($state) => match ($state) {
                                'pending_approval' => 'heroicon-o-clock',
                                'approved' => 'heroicon-o-check-circle',
                                'rejected' => 'heroicon-o-x-circle',
                                'needs_revision' => 'heroicon-o-exclamation-triangle',
                                default => 'heroicon-o-question-mark-circle'
                            })
                            ->size('lg')
                            ->weight('bold'),

                        TextEntry::make('latest_approval_note')
                            ->label('Catatan Terakhir')
                            ->getStateUsing(function (TransaksiPenjualan $record) {
                                $latestApproval = $record->approvals()->latest()->first();
                                return $latestApproval?->note ?? 'Tidak ada catatan';
                            })
                            ->visible(function (TransaksiPenjualan $record) {
                                return in_array($record->status, ['rejected', 'needs_revision']);
                            })
                            ->color('danger')
                            ->icon('heroicon-o-chat-bubble-left-ellipsis'),

                        TextEntry::make('approval_info')
                            ->label('Informasi')
                            ->getStateUsing(function (TransaksiPenjualan $record) {
                                // Check if there are any approvals
                                $hasApprovals = $record->approvals()->exists();
                                $isAuthorizedApprover = $this->isCurrentUserAuthorizedApprover();

                                if (!$hasApprovals && $record->status === 'pending_approval') {
                                    if ($isAuthorizedApprover) {
                                        return '⚠️ Transaksi ini belum diproses approval. Silakan klik tombol "Proses Approval" di atas untuk memulai proses persetujuan.';
                                    } else {
                                        return '⏳ Transaksi ini belum diproses approval. Hanya user yang terdaftar sebagai approver yang dapat memproses approval ini.';
                                    }
                                }

                                return match ($record->status) {
                                    'pending_approval' => $isAuthorizedApprover
                                        ? 'Transaksi menunggu persetujuan dari Anda sebagai approver. Tombol "Buat DO" dan "Buat Invoice" akan muncul setelah disetujui.'
                                        : 'Transaksi menunggu persetujuan dari approver. Tombol "Buat DO" dan "Buat Invoice" akan muncul setelah disetujui.',
                                    'approved' => '✅ Transaksi telah disetujui. Anda dapat membuat Delivery Order dan Invoice. Transaksi yang sudah disetujui tidak dapat diedit lagi.',
                                    'rejected' => '❌ Transaksi ditolak. Silakan hubungi manager untuk informasi lebih lanjut.',
                                    'needs_revision' => '🔄 Transaksi perlu diperbaikan. Silakan edit transaksi sesuai catatan dan ajukan ulang.',
                                    default => 'Status tidak dikenal.'
                                };
                            })
                            ->color(fn(TransaksiPenjualan $record) => match ($record->status) {
                                'pending_approval' => $record->approvals()->exists() ? 'warning' : 'info',
                                'approved' => 'success',
                                'rejected' => 'danger',
                                'needs_revision' => 'info',
                                default => 'gray'
                            })
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->collapsible(),

                // informasi detail repeater transaksi per item, use repeatableentry
                Section::make('Detail Transaksi')
                    ->schema([
                        RepeatableEntry::make('penjualanDetails')
                            ->label('Item Penjualan')
                            ->schema([
                                TextEntry::make('item.name')
                                    ->label('Item/Produk'),
                                TextEntry::make('volume_item')
                                    ->label('Volume')
                                    ->numeric(decimalPlaces: 2)
                                    ->suffix(' Liter'),
                                TextEntry::make('harga_jual')
                                    ->label('Harga Jual')
                                    ->formatStateUsing(function ($state) {
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    }),

                                TextEntry::make('harga_dasar')
                                    ->label('Harga Dasar')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada'),

                                TextEntry::make('discount_agent_skp')
                                    ->label('Discount Agent SKP')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada'),

                                TextEntry::make('ppn')
                                    ->label('PPN')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada'),

                                TextEntry::make('pbbkb')
                                    ->label('PBBKB')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada'),

                                TextEntry::make('pph_22')
                                    ->label('PPh 22')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        return $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada'),

                                TextEntry::make('rounding')
                                    ->label('Rounding')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Tidak Ada';
                                        $parentRecord = $this->getRecord();
                                        $currency = 'IDR';
                                        if ($parentRecord->letterSetting && $parentRecord->letterSetting->locale === 'en') {
                                            $currency = 'USD';
                                        }
                                        $prefix = $state >= 0 ? '+' : '';
                                        return $prefix . $currency . ' ' . number_format($state, 0, ',', '.');
                                    })
                                    ->placeholder('Tidak Ada')
                                    ->badge()
                                    ->color(fn($state) => $state > 0 ? 'success' : ($state < 0 ? 'danger' : 'gray')),

                                TextEntry::make('alamat_pengiriman')
                                    ->label('Alamat Pengiriman')
                                    ->formatStateUsing(function ($state, $record) {
                                        // Jika alamat_pengiriman adalah 'manual', tampilkan alamat_pengiriman_manual
                                        if ($state === 'manual' && !empty($record['alamat_pengiriman_manual'])) {
                                            return $record['alamat_pengiriman_manual'];
                                        }
                                        // Jika bukan manual, tampilkan alamat_pengiriman
                                        return $state ?: 'Tidak ada alamat';
                                    })
                                    ->placeholder('Tidak ada alamat')
                                    ->columnSpanFull(),

                                // TextEntry::make('location')
                                //     ->label('Koordinat GPS')
                                //     ->getStateUsing(function ($record) {
                                //         if (is_array($record) && isset($record['location'])) {
                                //             $location = $record['location'];
                                //             if (is_array($location) && isset($location['lat']) && isset($location['lng'])) {
                                //                 return $location['lat'] . ', ' . $location['lng'];
                                //             }
                                //         }
                                //         return null;
                                //     })
                                //     ->placeholder('Tidak ada koordinat')
                                //     ->copyable()
                                //     ->visible(fn($record) => is_array($record) && !empty($record['location'])),

                                TextEntry::make('keterangan_lokasi')
                                    ->label('Keterangan Lokasi')
                                    ->placeholder('Tidak ada keterangan')
                                    ->columnSpanFull()
                                    ->visible(fn($record) => is_array($record) && !empty($record['keterangan_lokasi'])),
                            ])
                            ->columns(3),

                    ])
                    ->columns(1),

                // Approval History Section
                Section::make('Riwayat Approval')
                    ->description(function (TransaksiPenjualan $record) {
                        $latestApproval = $record->approvals->first();
                        if (!$latestApproval) {
                            return 'Belum ada tindakan approval';
                        }

                        $status = $latestApproval->status;
                        $user = $latestApproval->creator->name ?? 'Unknown';

                        $color = match ($status) {
                            'approved' => '#10b981',
                            'rejected' => '#ef4444',
                            'reject_with_perbaikan' => '#f59e0b',
                            default => '#6b7280',
                        };

                        $statusLabel = match ($status) {
                            'approved' => 'Approved',
                            'rejected' => 'Rejected',
                            'reject_with_perbaikan' => 'Needs Revision',
                            default => ucfirst(str_replace('_', ' ', $status)),
                        };

                        return new HtmlString("<span style='color: {$color};'>●</span> {$statusLabel} oleh {$user}");
                    })
                    ->schema([
                        RepeatableEntry::make('approvals')
                            ->label('')
                            ->schema([
                                TextEntry::make('creator.name')
                                    ->label('Ditinjau Oleh')
                                    ->icon('heroicon-s-user'),
                                TextEntry::make('status')
                                    ->label('Tindakan')
                                    ->badge()
                                    ->formatStateUsing(fn($state) => match ($state) {
                                        'approved' => 'Approved',
                                        'rejected' => 'Rejected',
                                        'reject_with_perbaikan' => 'Needs Revision',
                                        default => ucfirst(str_replace('_', ' ', $state)),
                                    })
                                    ->color(fn($state) => match ($state) {
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        'reject_with_perbaikan' => 'warning',
                                        default => 'gray'
                                    }),
                                TextEntry::make('note')
                                    ->label('Catatan/Alasan')
                                    ->placeholder('Tidak ada catatan'),
                                TextEntry::make('created_at')
                                    ->label('Waktu')
                                    ->dateTime('d M Y, H:i'),
                            ])
                            ->columns(4)
                    ])
                    ->collapsible()
                    ->visible(fn(TransaksiPenjualan $record) => $record->approvals->isNotEmpty()),
            ]);
    }

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         // approval action
    //         Actions\Action::make('approval')
    //             ->label('Proses Approval')
    //             ->color('primary')
    //             ->icon('heroicon-o-check-badge')
    //             ->form([
    //                 Fieldset::make('Informasi Pembuat Transaksi')
    //                     ->schema([
    //                         Placeholder::make('pembuat_nama')
    //                             ->label('Nama Salesperson')
    //                             ->content(fn(TransaksiPenjualan $record): string => $record->createdBy?->name ?? 'N/A'),

    //                         Placeholder::make('pembuat_hp')
    //                             ->label('No. Handphone')
    //                             ->content(fn(TransaksiPenjualan $record): string => $record->createdBy?->hp ?? 'Tidak ada nomor'),
    //                     ])
    //                     ->columns(2),
    //                 Radio::make('status')
    //                     ->label('Status Approval')
    //                     ->options([
    //                         'approved' => 'Approved (Disetujui)',
    //                         'reject_with_perbaikan' => 'Reject with Revision (Tolak dengan Perbaikan)',
    //                         'rejected' => 'Rejected (Ditolak Final)',
    //                     ])
    //                     ->required()
    //                     ->live(),
    //                 Textarea::make('note')
    //                     ->label('Catatan / Alasan')
    //                     // Required only if status is a type of rejection
    //                     ->required(fn(Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan']))
    //                     // Visible only if status is a type of rejection
    //                     ->visible(fn(Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan'])),
    //             ])
    //             // 2. Service class and variable name updated here
    //             ->action(function (TransaksiPenjualan $record, array $data, TransaksiPenjualanService $penjualanService) {
    //                 // The action is simple: just call the service.
    //                 $penjualanService->processApproval(
    //                     $record,
    //                     \Illuminate\Support\Facades\Auth::user(),
    //                     $data['status'],
    //                     // 3. IMPORTANT: Use null coalescing operator to prevent errors when 'note' is not present.
    //                     $data['note'] ?? null
    //                 );

    //                 Notification::make()
    //                     ->title('Proses approval berhasil disimpan')
    //                     ->success()
    //                     ->send();
    //             }),
    //         // Optional: Only show this button if the record needs approval
    //         // ->visible(fn (TransaksiPenjualan $record) => $record->status === 'pending_approval'),

    //         // Actions\Action::make('reject')
    //         //     ->label('Tolak SO'),
    //         Actions\Action::make('view_timeline')
    //             ->label('Lihat Timeline')
    //             ->icon('heroicon-o-clock')
    //             ->color('success')
    //             ->url(fn(TransaksiPenjualan $record): string => "/admin/sales-order-timeline-detail?record={$record->id}")
    //             ->openUrlInNewTab(false),
    //         Actions\Action::make('create_do')
    //             ->label('Buat DO')
    //             ->icon('heroicon-o-truck')
    //             ->url(fn(TransaksiPenjualan $record): string => route('filament.admin.resources.delivery-orders.create', ['id_transaksi' => $record->id]))
    //             ->visible(fn(TransaksiPenjualan $record): bool => !$record->deliveryOrder)
    //             ->openUrlInNewTab(false),
    //         // lihat do kalau ada
    //         Actions\Action::make('view_do')
    //             ->label('Lihat DO')
    //             ->icon('heroicon-o-document-text')
    //             ->color('primary')
    //             ->url(fn(TransaksiPenjualan $record): string => $record->deliveryOrderUrl)
    //             ->visible(fn(TransaksiPenjualan $record): bool => $record->deliveryOrderUrl !== null)
    //             ->openUrlInNewTab(false),



    //         Actions\EditAction::make()
    //             ->color('gray'),
    //     ];
    // }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load the penjualan details for viewing
        $record = $this->getRecord();
        $data['penjualanDetails'] = $record->penjualanDetails->map(function ($detail) {
            return [
                'id_item' => $detail->id_item,
                'volume_item' => $detail->volume_item,
                'harga_jual' => $detail->harga_jual,
                'harga_dasar' => $detail->harga_dasar,
                'discount_agent_skp' => $detail->discount_agent_skp,
                'ppn' => $detail->ppn,
                'pbbkb' => $detail->pbbkb,
                'pph_22' => $detail->pph_22,
                'rounding' => $detail->rounding,
                'location' => $detail->location,
                'alamat_pengiriman' => $detail->alamat_pengiriman,
                'alamat_pengiriman_manual' => $detail->alamat_pengiriman_manual,
                'keterangan_lokasi' => $detail->keterangan_lokasi,
                'item_info' => $detail->item ? $detail->item->kode . ' - ' . $detail->item->name : '',
                'satuan_info' => $detail->item?->satuan?->nama ?? '',
            ];
        })->toArray();

        return $data;
    }

    public function getTitle(): string
    {
        return 'Detail Transaksi';
    }

    public function getSubheading(): ?string
    {
        return 'Lihat detail transaksi penjualan';
    }

    public function getBreadcrumbs(): array
    {
        return [
            '/admin' => 'Home',
            '/admin/transaksi-penjualans' => 'Transaksi Penjualan',
            '' => 'Detail',
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            //
        ];
    }
}
