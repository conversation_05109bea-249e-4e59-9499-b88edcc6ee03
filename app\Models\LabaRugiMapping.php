<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LabaRugiMapping extends Model
{
    protected $table = 'laba_rugi_mappings';
    
    protected $fillable = [
        'kode_akun',
        'nama_akun',
        'kategori_laba_rugi',
        'urutan',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get the account (Akun) that this mapping belongs to
     */
    public function akun()
    {
        return $this->belongsTo(Akun::class, 'kode_akun', 'kode_akun');
    }

    /**
     * Get all mappings grouped by category
     */
    public static function getMappingsByCategory()
    {
        return self::where('is_active', true)
            ->orderBy('urutan')
            ->get()
            ->groupBy('kategori_laba_rugi');
    }

    /**
     * Get revenue accounts (Pendapatan)
     */
    public static function getRevenueAccounts()
    {
        return self::where('kategori_laba_rugi', 'LIKE', 'Pendapatan%')
            ->where('is_active', true)
            ->orderBy('urutan')
            ->get();
    }

    /**
     * Get HPP accounts
     */
    public static function getHPPAccounts()
    {
        return self::whereIn('kategori_laba_rugi', [
            'HPP BBM',
            'HPP Jasa Angkut',
            'Persediaan'
        ])
        ->where('is_active', true)
        ->orderBy('urutan')
        ->get();
    }

    /**
     * Get expense accounts (Beban)
     */
    public static function getExpenseAccounts()
    {
        return self::where('kategori_laba_rugi', 'LIKE', 'Beban%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'Gaji%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'ATK%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'Ekspedisi%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'BBM%')
            ->where('is_active', true)
            ->orderBy('urutan')
            ->get();
    }

    /**
     * Get other income accounts
     */
    public static function getOtherIncomeAccounts()
    {
        return self::whereIn('kategori_laba_rugi', [
            'Pendapatan Jasa Giro',
            'Pendapatan Bunga Bank',
            'Pendapatan Lain Lain'
        ])
        ->where('is_active', true)
        ->orderBy('urutan')
        ->get();
    }

    /**
     * Get other expense accounts
     */
    public static function getOtherExpenseAccounts()
    {
        return self::whereIn('kategori_laba_rugi', [
            'Adm Bank',
            'Pajak Bank',
            'Bunga Bank',
            'Lain Lain'
        ])
        ->where('is_active', true)
        ->orderBy('urutan')
        ->get();
    }

    /**
     * Get tax accounts
     */
    public static function getTaxAccounts()
    {
        return self::where('kategori_laba_rugi', 'LIKE', 'Pajak%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'PBB%')
            ->orWhere('kategori_laba_rugi', 'LIKE', 'PPN%')
            ->where('is_active', true)
            ->orderBy('urutan')
            ->get();
    }
}
