/* Dashboard Theme CSS for Light/Dark Mode Compatibility */

/* Base Dashboard Styles */
.dashboard-container {
    @apply space-y-6;
}

/* Filter Section */
.dashboard-filter {
    @apply bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6;
    background-color: #ffffff !important;
}

.dark .dashboard-filter {
    background-color: #1f2937 !important;
    border-color: #4b5563 !important;
}

.dashboard-filter h3 {
    @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

/* KPI Cards */
.kpi-card {
    @apply bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg;
    background-color: #ffffff !important;
}

.dark .kpi-card {
    background-color: #1f2937 !important;
    border-color: #4b5563 !important;
}

.kpi-card-gradient {
    @apply overflow-hidden shadow-lg rounded-lg border-2 border-gray-200 dark:border-gray-600;
}

.kpi-card-content {
    @apply p-5;
}

.kpi-card-icon {
    @apply flex-shrink-0;
}

.kpi-card-body {
    @apply ml-5 w-0 flex-1;
}

.kpi-card-title {
    @apply text-sm font-medium text-gray-600 dark:text-gray-300 truncate;
}

.kpi-card-value {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.kpi-card-subtitle {
    @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Gradient Card Titles */
.kpi-card-gradient .kpi-card-title {
    @apply text-sm font-medium truncate;
}

.kpi-card-gradient .kpi-card-value {
    @apply text-2xl font-bold text-white;
}

.kpi-card-gradient .kpi-card-subtitle {
    @apply text-sm;
}

/* Chart Containers */
.chart-container {
    @apply bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6;
    background-color: #ffffff !important;
}

.dark .chart-container {
    background-color: #1f2937 !important;
    border-color: #4b5563 !important;
}

.chart-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.chart-canvas {
    @apply h-64;
}

.chart-canvas-large {
    @apply h-80;
}

/* Table Styles */
.dashboard-table {
    @apply bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg;
    background-color: #ffffff !important;
}

.dark .dashboard-table {
    background-color: #1f2937 !important;
    border-color: #4b5563 !important;
}

.dashboard-table-header {
    @apply p-6 border-b border-gray-200 dark:border-gray-700;
}

.dashboard-table-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.dashboard-table-content {
    @apply overflow-x-auto;
}

.dashboard-table table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.dashboard-table thead {
    @apply bg-gray-50 dark:bg-gray-700;
}

.dashboard-table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
}

.dashboard-table tbody {
    @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700;
}

.dashboard-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300;
}

.dashboard-table .table-cell-primary {
    @apply text-sm font-medium text-gray-900 dark:text-white;
}

/* Icon Colors for Light/Dark Mode */
.icon-blue {
    @apply text-blue-500 dark:text-blue-400;
}

.icon-green {
    @apply text-green-500 dark:text-green-400;
}

.icon-yellow {
    @apply text-yellow-500 dark:text-yellow-400;
}

.icon-red {
    @apply text-red-500 dark:text-red-400;
}

.icon-purple {
    @apply text-purple-500 dark:text-purple-400;
}

.icon-indigo {
    @apply text-indigo-500 dark:text-indigo-400;
}

.icon-teal {
    @apply text-teal-500 dark:text-teal-400;
}

.icon-orange {
    @apply text-orange-500 dark:text-orange-400;
}

/* Badge Styles */
.badge-corporate {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.badge-individual {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200;
}

.badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.badge-danger {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Grid Layouts */
.grid-kpi-main {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.grid-kpi-secondary {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.grid-charts {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

/* Responsive Text */
.text-responsive-sm {
    @apply text-xs sm:text-sm;
}

.text-responsive-base {
    @apply text-sm sm:text-base;
}

.text-responsive-lg {
    @apply text-base sm:text-lg;
}

/* Loading States */
.loading-skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.loading-card {
    @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-6;
}

/* Chart.js Dark Mode Compatibility */
.chart-dark canvas {
    filter: brightness(0.9) contrast(1.1);
}

/* Custom Scrollbar for Dark Mode */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
}

/* Print Styles */
@media print {
    .dashboard-container {
        @apply space-y-4;
    }

    .kpi-card-gradient {
        @apply bg-gray-100 text-gray-900;
    }

    .chart-container {
        @apply border border-gray-300;
    }

    .dashboard-table {
        @apply border border-gray-300;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .kpi-card {
        @apply border-2 border-gray-900 dark:border-gray-100;
    }

    .chart-container {
        @apply border-2 border-gray-900 dark:border-gray-100;
    }

    .dashboard-table {
        @apply border-2 border-gray-900 dark:border-gray-100;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .loading-skeleton {
        @apply animate-none;
    }

    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
