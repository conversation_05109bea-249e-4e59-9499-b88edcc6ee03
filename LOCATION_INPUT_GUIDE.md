# Location Input System for Transaction Details

## Overview

Sistem input lokasi telah ditambahkan pada setiap detail transaksi penjualan. Setiap item dalam transaksi sekarang dapat memiliki lokasi pengiriman yang spesifik dengan koordinat GPS, al<PERSON><PERSON> leng<PERSON>, dan keterangan tambahan.

## Features Added

### 1. Database Structure

#### New Fields in `penjualan_detail` Table
- **location** (JSON): Koordinat GPS dalam format `{"lat": -6.2088, "lng": 106.8456}`
- **alamat_pengiriman** (TEXT): Alamat lengkap pengiriman
- **keterangan_lokasi** (TEXT): Keterangan tambahan tentang lokasi

### 2. Model Enhancements

#### PenjualanDetail Model Updates
```php
// Helper methods
public function hasLocation(): bool
public function getLatitude(): ?float
public function getLongitude(): ?float
public function getCoordinatesString(): ?string
public function getGoogleMapsUrl(): ?string
```

#### Location Data Structure
```json
{
    "lat": -6.2088,
    "lng": 106.8456
}
```

### 3. User Interface

#### TransaksiPenjualanResource Form
- **Leaflet Map Picker**: Interactive map untuk memilih koordinat
- **Alamat Pengiriman**: Textarea untuk alamat lengkap
- **Keterangan Lokasi**: Textarea untuk keterangan tambahan
- **Collapsible Section**: Section lokasi dapat di-collapse untuk menghemat ruang

#### Form Features
- **Draggable Marker**: User dapat drag marker di peta
- **Clickable Map**: Klik di peta untuk set lokasi
- **My Location Button**: Tombol untuk menggunakan lokasi saat ini
- **Default Location**: Default ke koordinat LRP Pekanbaru
- **Responsive Height**: Map height 300px

#### Table Display
- **Location Icon Column**: Menampilkan status lokasi per transaksi
- **Tooltip Information**: Menampilkan jumlah item yang memiliki lokasi
- **Color Coding**: 
  - Green (success): Ada item dengan lokasi
  - Gray: Tidak ada item dengan lokasi

### 4. View Pages

#### ViewTransaksiPenjualan
- **Informasi Lokasi Section**: Collapsible section untuk setiap item
- **Coordinates Display**: Koordinat yang dapat di-copy dan link ke Google Maps
- **Address Display**: Alamat pengiriman lengkap
- **Notes Display**: Keterangan lokasi tambahan
- **Conditional Visibility**: Hanya tampil jika ada data lokasi

#### Display Features
- **Copyable Coordinates**: Koordinat dapat di-copy dengan satu klik
- **Google Maps Link**: Klik koordinat untuk buka di Google Maps
- **Responsive Layout**: Adaptif untuk mobile dan desktop

### 5. Location Data Types

#### Complete Location
```php
[
    'location' => ['lat' => -6.2088, 'lng' => 106.8456],
    'alamat_pengiriman' => 'Jl. Sudirman No. 123, Jakarta Pusat',
    'keterangan_lokasi' => 'Gedung perkantoran, lantai 5'
]
```

#### Address Only
```php
[
    'location' => null,
    'alamat_pengiriman' => 'Jl. Veteran No. 369, Palembang',
    'keterangan_lokasi' => 'Alamat saja tanpa koordinat GPS'
]
```

#### Coordinates Only
```php
[
    'location' => ['lat' => -2.5489, 'lng' => 118.0149],
    'alamat_pengiriman' => null,
    'keterangan_lokasi' => 'Hanya koordinat GPS'
]
```

### 6. File Structure

```
app/
├── Models/
│   └── PenjualanDetail.php (UPDATED)
├── Filament/Resources/
│   ├── TransaksiPenjualanResource.php (UPDATED)
│   └── TransaksiPenjualanResource/Pages/
│       └── ViewTransaksiPenjualan.php (UPDATED)

database/
├── migrations/
│   └── 2025_07_28_000006_add_location_fields_to_penjualan_detail_table.php (NEW)
└── seeders/
    └── PenjualanDetailLocationSeeder.php (NEW)
```

### 7. Usage Examples

#### Creating Transaction with Locations
1. Buka TransaksiPenjualan form
2. Tambah item di repeater
3. Expand "Lokasi Pengiriman" section
4. Isi alamat pengiriman
5. Klik di peta atau drag marker untuk set koordinat
6. Tambah keterangan jika perlu
7. Save transaksi

#### Viewing Location Information
- **Table View**: Icon menunjukkan status lokasi
- **Detail View**: Section lokasi untuk setiap item
- **Coordinates**: Klik untuk buka di Google Maps

### 8. Benefits

#### For Sales Team
- **Specific Delivery Locations**: Setiap item dapat memiliki lokasi berbeda
- **GPS Accuracy**: Koordinat presisi untuk navigasi
- **Flexible Input**: Bisa alamat saja, GPS saja, atau keduanya

#### For Drivers
- **Clear Directions**: Alamat lengkap dan koordinat GPS
- **Navigation Ready**: Link langsung ke Google Maps
- **Additional Notes**: Keterangan untuk memudahkan pencarian

#### For Operations
- **Route Planning**: Data lokasi untuk optimasi rute
- **Delivery Tracking**: Monitoring pengiriman per lokasi
- **Customer Service**: Informasi lengkap untuk follow-up

### 9. Technical Implementation

#### Map Integration
```php
\Afsakar\LeafletMapPicker\LeafletMapPicker::make('location')
    ->label('Lokasi di Peta')
    ->defaultLocation([0.5394419, 101.451907]) // LRP default
    ->defaultZoom(15)
    ->draggable(true)
    ->clickable(true)
    ->myLocationButtonLabel('Lokasi Saya')
    ->tileProvider('openstreetmap')
    ->height('300px')
```

#### Location Validation
- **JSON Format**: Location stored as JSON array
- **Coordinate Range**: Latitude -90 to 90, Longitude -180 to 180
- **Optional Fields**: All location fields are optional
- **Flexible Input**: Support partial location data

### 10. Testing Data

#### Sample Locations
- **Jakarta**: Jl. Sudirman (business district)
- **Surabaya**: Jl. Raya Darmo (SPBU Shell)
- **Yogyakarta**: Jl. Malioboro (tourist area)
- **Bandung**: Jl. Asia Afrika (government area)
- **Denpasar**: Jl. Gajah Mada (city center)
- **Medan**: Jl. Sisingamangaraja (business area)
- **Makassar**: Jl. Jenderal Sudirman (port area)
- **Pekanbaru**: Jl. Jenderal Sudirman (LRP area)

#### Test Cases
- Complete location data (GPS + address + notes)
- Address only (no GPS coordinates)
- GPS only (no address)
- No location data (all fields empty)

## Migration Notes

- **Safe Migration**: New fields are nullable
- **Backward Compatible**: Existing data not affected
- **Performance**: JSON field indexed for queries
- **Storage Efficient**: Compact JSON format for coordinates

## Future Enhancements

1. **Bulk Location Import**: Import locations from CSV/Excel
2. **Location Templates**: Save frequently used locations
3. **Route Optimization**: Calculate optimal delivery routes
4. **Geofencing**: Alerts when entering/leaving delivery areas
5. **Location History**: Track location changes over time
6. **Mobile App Integration**: Better mobile location input
7. **Address Validation**: Validate addresses against postal service
8. **Distance Calculation**: Calculate distances between locations
