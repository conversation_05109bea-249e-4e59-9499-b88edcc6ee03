<?php

namespace App\Filament\Resources\LaporanHarianResource\Pages;

use App\Filament\Resources\LaporanHarianResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;

class ViewLaporanHarian extends ViewRecord
{
    protected static string $resource = LaporanHarianResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Laporan')
                    ->schema([
                        TextEntry::make('tanggal_laporan')
                            ->label('Tanggal Laporan')
                            ->date('d F Y'),

                        TextEntry::make('createdBy.name')
                            ->label('Dibuat Oleh'),

                        TextEntry::make('created_at')
                            ->label('Dibuat Pada')
                            ->dateTime('d F Y H:i'),
                    ])
                    ->columns(3),

                Section::make('Foto Mobil Tangki dengan Muatan')
                    ->description('Dokumentasi foto mobil tangki yang ada muatan')
                    ->schema([
                        SpatieMediaLibraryImageEntry::make('foto_mobil_tangki')
                            ->label('')
                            ->collection('foto_mobil_tangki')
                            ->conversion('thumb')
                            ->size(200)
                            ->square(false)
                            ->limit(10)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Foto Laporan Tamu Keluar Masuk')
                    ->description('Dokumentasi foto laporan tamu keluar masuk')
                    ->schema([
                        SpatieMediaLibraryImageEntry::make('foto_laporan_tamu')
                            ->label('')
                            ->collection('foto_laporan_tamu')
                            ->conversion('thumb')
                            ->size(200)
                            ->square(false)
                            ->limit(10)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->getMedia('foto_laporan_tamu')->count() > 0)
                    ->collapsible(),

                Section::make('Catatan')
                    ->schema([
                        TextEntry::make('catatan')
                            ->label('Catatan')
                            ->placeholder('Tidak ada catatan')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => !empty($record->catatan))
                    ->collapsible(),
            ]);
    }
}
