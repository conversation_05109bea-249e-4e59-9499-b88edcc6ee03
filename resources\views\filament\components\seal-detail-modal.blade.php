<div class="space-y-6">
    <!-- Informasi Segel -->
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nomor <PERSON>
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white font-semibold">
                {{ $record->nomor_segel }}
            </p>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <PERSON><PERSON>
            </label>
            <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                @switch($record->jenis_segel)
                    @case('atas')
                        bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300
                        @break
                    @case('bawah')
                        bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300
                        @break
                    @case('samping')
                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300
                        @break
                    @default
                        bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300
                @endswitch
            ">
                {{ \App\Models\DeliveryOrderSeal::JENIS_SEGEL[$record->jenis_segel] ?? $record->jenis_segel }}
            </span>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Urutan
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ $record->urutan }}
            </p>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Dibuat Oleh
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ $record->createdBy->name ?? 'System' }}
            </p>
        </div>
    </div>

    @if($record->keterangan)
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Keterangan
            </label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ $record->keterangan }}
            </p>
        </div>
    @endif

    <!-- Foto Segel -->
    @if($fotoUrl)
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Foto Segel
            </label>
            <div class="flex justify-center">
                <img 
                    src="{{ $fotoUrl }}" 
                    alt="Foto Segel {{ $record->nomor_segel }}"
                    class="max-w-full h-auto rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
                    style="max-height: 400px;"
                >
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Tidak ada foto segel
            </p>
        </div>
    @endif

    <!-- Informasi Tambahan -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
        <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400">
            <div>
                <span class="font-medium">Dibuat:</span>
                {{ $record->created_at->format('d/m/Y H:i') }}
            </div>
            @if($record->updated_at != $record->created_at)
                <div>
                    <span class="font-medium">Diupdate:</span>
                    {{ $record->updated_at->format('d/m/Y H:i') }}
                </div>
            @endif
        </div>
    </div>
</div>
