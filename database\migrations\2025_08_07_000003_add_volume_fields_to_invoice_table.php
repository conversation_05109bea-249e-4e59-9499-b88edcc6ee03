<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->decimal('operasional_volume', 15, 2)->default(0)->after('biaya_operasional_kerja')
                ->comment('Volume untuk perhitungan operasional (dari invoice items)');
            $table->decimal('pbbkb_volume', 15, 2)->default(0)->after('biaya_pbbkb')
                ->comment('Volume untuk perhitungan PBBKB (dari invoice items)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            $table->dropColumn(['operasional_volume', 'pbbkb_volume']);
        });
    }
};
