<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AsetDepreciation extends Model
{
    protected $fillable = [
        'aset_id',
        'depreciation_date',
        'amount',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'depreciation_date' => 'date',
    ];

    public function aset()
    {
        return $this->belongsTo(Aset::class, 'aset_id');
    }

    public function getFormattedDepreciationDateAttribute(): string
    {
        return $this->depreciation_date->format('d-m-Y');
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2, ',', '.');
    }
}
