<?php

namespace App\Filament\Resources\ExpenseRequestResource\Pages;

use App\Filament\Resources\ExpenseRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExpenseRequest extends EditRecord
{
    protected static string $resource = ExpenseRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Find category_id based on category code
        if (isset($data['category'])) {
            $expenseCategory = \App\Models\ExpenseCategory::where('code', $data['category'])->first();
            if ($expenseCategory) {
                $data['category_id'] = $expenseCategory->id;
            }
        }

        return $data;
    }
}
