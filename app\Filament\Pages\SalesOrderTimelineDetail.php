<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use App\Models\PengirimanDriver;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class SalesOrderTimelineDetail extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $title = 'Detail Timeline Pesanan Penjualan';
    protected static string $view = 'filament.pages.sales-order-timeline-detail';
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $slug = 'sales-order-timeline-detail';

    public ?TransaksiPenjualan $record = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->can('page_SalesOrderTimelineDetail') ?? false;
    }

    public function mount(): void
    {
        // Get record ID from query parameter
        $recordId = request()->get('record');

        if (!$recordId) {
            abort(404, 'Sales Order ID not provided');
        }

        try {
            $this->record = TransaksiPenjualan::with([
                'pelanggan',
                'penjualanDetails.item.kategori',
                'penjualanDetails.item.satuan',
                'tbbm',
                'createdBy',
                'fakturPajak.createdBy',
                'invoices.payments.paymentMethod',
                'invoices.payments.createdBy',
                'receipts.createdBy',
                'taxInvoices.createdBy',
                'approvals.creator'
            ])->findOrFail($recordId);
        } catch (ModelNotFoundException) {
            abort(404, 'Sales Order not found');
        }
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public function getTitle(): string
    {
        return $this->record ? "Timeline for SO: {$this->record->kode}" : 'Detail Timeline Pesanan Penjualan';
    }

    public function getBreadcrumbs(): array
    {
        return [
            '/admin/sales-order-timeline' => 'Sales Order Timeline',
            '' => $this->record ? "SO: {$this->record->kode}" : 'Detail Timeline',
        ];
    }

    public function getDeliveryOrders()
    {
        if (!$this->record) {
            return collect();
        }

        return DeliveryOrder::where('id_transaksi', $this->record->id)
            ->with([
                'user',
                'kendaraan',
                'pengirimanDriver.createdBy',
                'pengirimanDriver.approvedBy',
                'uangJalan.user',
                'uangJalan.createdBy',
                'uangJalan.approvedBy'
            ])
            ->orderBy('created_at')
            ->get();
    }

    public function getTimelineEvents()
    {
        if (!$this->record) {
            return collect();
        }

        $events = collect();

        // Sales Order Created Event
        $events->push([
            'type' => 'sales_order_created',
            'title' => 'Sales Order Dibuat',
            'link' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $this->record->id]),
            'description' => 'Sales order dibuat dan dikonfirmasi',
            'timestamp' => $this->record->created_at,
            'icon' => 'heroicon-o-document-plus',
            'color' => 'blue',
            'data' => [
                'Nomor SO' => $this->record->kode,
                'Pelanggan' => $this->record->pelanggan->nama ?? 'N/A',
                'Jenis BBM' => $this->record->penjualanDetails->pluck('item.name')->unique()->join(', '),
                'Volume' => number_format($this->record->penjualanDetails->sum('volume_item'), 2) . ' Liter',
                'TBBM' => $this->record->tbbm->nama ?? 'N/A',
                'Dibuat Oleh' => $this->record->createdBy->name ?? 'System',
                'Status' => $this->record->status ?? 'N/A',
                'Nomor PO' => $this->record->nomor_po ?? 'N/A',
                'TOP Pembayaran' => $this->record->top_pembayaran ?? 'N/A',
            ]
        ]);

        // Sales Order Approval Events
        if ($this->record->approvals && $this->record->approvals->count() > 0) {
            foreach ($this->record->approvals as $approval) {
                $events->push([
                    'type' => 'sales_order_approval',
                    'title' => 'Persetujuan Sales Order',
                    'link' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $this->record->id]),
                    'description' => 'Sales order ' . ($approval->status === 'approved' ? 'disetujui' : ($approval->status === 'rejected' ? 'ditolak' : 'menunggu persetujuan')),
                    'timestamp' => $approval->created_at,
                    'icon' => $approval->status === 'approved' ? 'heroicon-o-check-circle' : ($approval->status === 'rejected' ? 'heroicon-o-x-circle' : 'heroicon-o-clock'),
                    'color' => $approval->status === 'approved' ? 'green' : ($approval->status === 'rejected' ? 'red' : 'yellow'),
                    'data' => [
                        'Status' => ucfirst($approval->status),
                        'Catatan' => $approval->note ?? 'Tidak ada catatan',
                        'Diproses Oleh' => $approval->creator->name ?? 'System',
                    ]
                ]);
            }
        }

        // Faktur Pajak Events
        if ($this->record->fakturPajak) {
            $faktur = $this->record->fakturPajak;
            $events->push([
                'type' => 'tax_invoice_created',
                'title' => 'Faktur Pajak Dibuat',
                'link' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $this->record->id]),
                'description' => 'Faktur pajak telah dibuat untuk transaksi ini',
                'timestamp' => $faktur->created_at,
                'icon' => 'heroicon-o-document-text',
                'color' => 'purple',
                'data' => [
                    'Nomor Faktur' => $faktur->nomor_faktur,
                    'Tanggal Faktur' => $faktur->tanggal_faktur ? $faktur->tanggal_faktur->format('d M Y') : 'N/A',
                    'NPWP Pelanggan' => $faktur->npwp_pelanggan ?? 'N/A',
                    'Total DPP' => 'IDR ' . number_format($faktur->total_dpp ?? 0),
                    'Total PPN' => 'IDR ' . number_format($faktur->total_ppn ?? 0),
                    'Status' => $faktur->status ?? 'N/A',
                    'Dibuat Oleh' => $faktur->createdBy->name ?? 'System',
                ]
            ]);
        }

        // Invoice Events
        if ($this->record->invoices && $this->record->invoices->count() > 0) {
            foreach ($this->record->invoices as $invoice) {
                $events->push([
                    'type' => 'invoice_created',
                    'title' => 'Invoice Dibuat',
                    'link' => route('filament.admin.resources.invoices.view', ['record' => $invoice->id]),
                    'description' => 'Invoice telah dibuat untuk transaksi ini',
                    'timestamp' => $invoice->created_at,
                    'icon' => 'heroicon-o-document-currency-dollar',
                    'color' => 'indigo',
                    'data' => [
                        'Nomor Invoice' => $invoice->nomor_invoice,
                        'Tanggal Invoice' => $invoice->tanggal_invoice ? $invoice->tanggal_invoice->format('d M Y') : 'N/A',
                        'Tanggal Jatuh Tempo' => $invoice->tanggal_jatuh_tempo ? $invoice->tanggal_jatuh_tempo->format('d M Y') : 'N/A',
                        'Subtotal' => 'IDR ' . number_format($invoice->subtotal ?? 0),
                        'Total Invoice' => 'IDR ' . number_format($invoice->total_invoice ?? 0),
                        'Total Terbayar' => 'IDR ' . number_format($invoice->total_terbayar ?? 0),
                        'Sisa Tagihan' => 'IDR ' . number_format($invoice->sisa_tagihan ?? 0),
                        'Status' => $invoice->status ?? 'N/A',
                    ]
                ]);

                // Invoice Payment Events
                if ($invoice->payments && $invoice->payments->count() > 0) {
                    foreach ($invoice->payments as $payment) {
                        $events->push([
                            'type' => 'invoice_payment',
                            'title' => 'Pembayaran Invoice',
                            'link' => route('filament.admin.resources.invoices.view', ['record' => $invoice->id]),
                            'description' => 'Pembayaran diterima untuk invoice ini',
                            'timestamp' => $payment->payment_date ?? $payment->created_at,
                            'icon' => 'heroicon-o-banknotes',
                            'color' => 'emerald',
                            'data' => [
                                'Jumlah Pembayaran' => 'IDR ' . number_format($payment->amount ?? 0),
                                'Metode Pembayaran' => $payment->paymentMethod->name ?? 'N/A',
                                'Nomor Referensi' => $payment->reference_number ?? 'N/A',
                                'Status' => ucfirst($payment->status ?? 'pending'),
                                'Catatan' => $payment->notes ?? 'Tidak ada catatan',
                                'Diproses Oleh' => $payment->createdBy->name ?? 'System',
                            ]
                        ]);
                    }
                }
            }
        }

        // Tax Invoice Events
        if ($this->record->taxInvoices && $this->record->taxInvoices->count() > 0) {
            foreach ($this->record->taxInvoices as $taxInvoice) {
                $events->push([
                    'type' => 'tax_invoice_standalone',
                    'title' => 'Tax Invoice Dibuat',
                    'link' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $this->record->id]),
                    'description' => 'Tax invoice telah dibuat untuk transaksi ini',
                    'timestamp' => $taxInvoice->created_at,
                    'icon' => 'heroicon-o-receipt-tax',
                    'color' => 'violet',
                    'data' => [
                        'Nomor Tax Invoice' => $taxInvoice->nomor_tax_invoice,
                        'Tanggal' => $taxInvoice->tanggal_tax_invoice ? $taxInvoice->tanggal_tax_invoice->format('d M Y') : 'N/A',
                        'DPP' => 'IDR ' . number_format($taxInvoice->dasar_pengenaan_pajak ?? 0),
                        'Tarif Pajak' => ($taxInvoice->tarif_pajak ?? 0) . '%',
                        'PPN' => 'IDR ' . number_format($taxInvoice->pajak_pertambahan_nilai ?? 0),
                        'Total' => 'IDR ' . number_format($taxInvoice->total_tax_invoice ?? 0),
                        'Status' => $taxInvoice->status ?? 'N/A',
                    ]
                ]);
            }
        }

        // Receipt Events
        if ($this->record->receipts && $this->record->receipts->count() > 0) {
            foreach ($this->record->receipts as $receipt) {
                $events->push([
                    'type' => 'receipt_created',
                    'title' => 'Kwitansi Dibuat',
                    'link' => route('filament.admin.resources.transaksi-penjualans.view', ['record' => $this->record->id]),
                    'description' => 'Kwitansi pembayaran telah dibuat',
                    'timestamp' => $receipt->created_at,
                    'icon' => 'heroicon-o-document-check',
                    'color' => 'teal',
                    'data' => [
                        'Nomor Kwitansi' => $receipt->nomor_receipt,
                        'Tanggal Kwitansi' => $receipt->tanggal_receipt ? $receipt->tanggal_receipt->format('d M Y') : 'N/A',
                        'Tanggal Pembayaran' => $receipt->tanggal_pembayaran ? $receipt->tanggal_pembayaran->format('d M Y') : 'N/A',
                        'Metode Pembayaran' => $receipt->metode_pembayaran ?? 'N/A',
                        'Jumlah Pembayaran' => 'IDR ' . number_format($receipt->jumlah_pembayaran ?? 0),
                        'Total Diterima' => 'IDR ' . number_format($receipt->total_diterima ?? 0),
                        'Status' => $receipt->status ?? 'N/A',
                    ]
                ]);
            }
        }

        // Delivery Orders Events
        $deliveryOrders = $this->getDeliveryOrders();
        foreach ($deliveryOrders as $do) {
            // Delivery Order Created
            $events->push([
                'type' => 'delivery_order_created',
                'title' => 'Delivery Order Dibuat',
                'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                'description' => 'Delivery order dibuat untuk pesanan penjualan ini',
                'timestamp' => $do->created_at,
                'icon' => 'heroicon-o-truck',
                'color' => 'indigo',
                'data' => [
                    'Nomor DO' => $do->kode,
                    'Tanggal Pengiriman' => $do->tanggal_delivery ? $do->tanggal_delivery->format('d M Y') : 'Not scheduled',
                    'Kendaraan' => $do->kendaraan->nomor_polisi ?? 'Not assigned',
                    'Sopir' => $do->user->name ?? 'Not assigned',
                    'Nomor Segel' => $do->no_segel ?? 'Not set',
                    'Status' => $do->status_muat ?? 'pending',
                ]
            ]);

            // Loading Events
            if ($do->waktu_muat) {
                $events->push([
                    'type' => 'loading_started',
                    'title' => 'Muat Dimulai',
                    'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                    'description' => 'Proses muat dimulai',
                    'timestamp' => $do->waktu_muat,
                    'icon' => 'heroicon-o-arrow-down-on-square',
                    'color' => 'yellow',
                    'data' => [
                        'do_number' => $do->kode,
                        'vehicle' => $do->kendaraan->nomor_polisi ?? 'N/A',
                    ]
                ]);
            }

            if ($do->waktu_selesai_muat) {
                $events->push([
                    'type' => 'loading_completed',
                    'title' => 'Muat Selesai',
                    'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                    'description' => 'Proses muat selesai',
                    'timestamp' => $do->waktu_selesai_muat,
                    'icon' => 'heroicon-o-check-circle',
                    'color' => 'green',
                    'data' => [
                        'do_number' => $do->kode,
                        'vehicle' => $do->kendaraan->nomor_polisi ?? 'N/A',
                    ]
                ]);
            }

            // Driver Allowance Events
            if ($do->uangJalan) {
                $allowance = $do->uangJalan;

                $events->push([
                    'type' => 'allowance_created',
                    'title' => 'Uang Jalan Dibuat',
                    'link' => route('filament.admin.resources.uang-jalans.view', ['record' => $allowance->id]),
                    'description' => 'Uang jalan dibuat untuk driver',
                    'timestamp' => $allowance->created_at,
                    'icon' => 'heroicon-o-banknotes',
                    'color' => 'purple',
                    'data' => [
                        'Total Nominal' => 'IDR ' . number_format($allowance->nominal ?? 0),
                        'Uang Depot' => 'IDR ' . number_format($allowance->uang_depot ?? 0),
                        'Uang Jalan' => 'IDR ' . number_format($allowance->uang_jalan_amount ?? 0),
                        'Uang Bongkar' => 'IDR ' . number_format($allowance->uang_bongkar ?? 0),
                        'Uang BBM' => 'IDR ' . number_format($allowance->uang_bbm ?? 0),
                        'Uang Tol' => 'IDR ' . number_format($allowance->uang_tol ?? 0),
                        'Driver' => $allowance->user->name ?? 'N/A',
                        'Status Kirim' => $allowance->status_kirim ?? 'pending',
                        'Status Terima' => $allowance->status_terima ?? 'pending',
                        'Dibuat Oleh' => $allowance->createdBy->name ?? 'System',
                    ]
                ]);

                // Allowance Approval Events
                if ($allowance->approval_status && $allowance->approved_at) {
                    $events->push([
                        'type' => 'allowance_approval',
                        'title' => 'Persetujuan Uang Jalan',
                        'link' => route('filament.admin.resources.uang-jalans.view', ['record' => $allowance->id]),
                        'description' => 'Uang jalan ' . ($allowance->approval_status === 'approved' ? 'disetujui' : 'ditolak'),
                        'timestamp' => $allowance->approved_at,
                        'icon' => $allowance->approval_status === 'approved' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle',
                        'color' => $allowance->approval_status === 'approved' ? 'green' : 'red',
                        'data' => [
                            'Status' => ucfirst($allowance->approval_status),
                            'Nominal' => 'IDR ' . number_format($allowance->nominal ?? 0),
                            'Disetujui Oleh' => $allowance->approvedBy->name ?? 'N/A',
                            'Catatan' => $allowance->approval_notes ?? 'Tidak ada catatan',
                        ]
                    ]);
                }

                // Allowance Receipt Event
                if ($do->allowance_receipt_time) {
                    $events->push([
                        'type' => 'allowance_received',
                        'title' => 'Uang Jalan Diterima',
                        'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                        'description' => 'Driver telah menerima uang jalan',
                        'timestamp' => $do->allowance_receipt_time,
                        'icon' => 'heroicon-o-hand-raised',
                        'color' => 'emerald',
                        'data' => [
                            'Nominal' => 'IDR ' . number_format($allowance->nominal ?? 0),
                            'Driver' => $allowance->user->name ?? 'N/A',
                            'Status' => $do->allowance_receipt_status ? 'Diterima' : 'Belum Diterima',
                        ]
                    ]);
                }
            }

            // DO Handover Events
            if ($do->do_handover_time) {
                $events->push([
                    'type' => 'do_handover',
                    'title' => 'Serah Terima DO',
                    'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                    'description' => 'Delivery Order diserahterimakan',
                    'timestamp' => $do->do_handover_time,
                    'icon' => 'heroicon-o-hand-raised',
                    'color' => 'cyan',
                    'data' => [
                        'Nomor DO' => $do->kode,
                        'Status' => $do->do_handover_status ? 'Diserahkan' : 'Belum Diserahkan',
                        'Kendaraan' => $do->kendaraan->nomor_polisi ?? 'N/A',
                        'Driver' => $do->user->name ?? 'N/A',
                    ]
                ]);
            }

            // Invoice Confirmation Events
            if ($do->invoice_confirmation_time) {
                $events->push([
                    'type' => 'invoice_confirmation',
                    'title' => 'Konfirmasi Invoice',
                    'link' => route('filament.admin.resources.delivery-orders.view', ['record' => $do->id]),
                    'description' => 'Invoice telah dikonfirmasi',
                    'timestamp' => $do->invoice_confirmation_time,
                    'icon' => 'heroicon-o-document-check',
                    'color' => 'blue',
                    'data' => [
                        'Nomor DO' => $do->kode,
                        'Status Konfirmasi' => $do->invoice_confirmation_status ? 'Dikonfirmasi' : 'Belum Dikonfirmasi',
                        'Nomor Invoice' => $do->invoice_number ?? 'N/A',
                        'Nomor Tax Invoice' => $do->tax_invoice_number ?? 'N/A',
                    ]
                ]);
            }

            // Delivery Events
            if ($do->pengirimanDriver) {
                $delivery = $do->pengirimanDriver;

                if ($delivery->waktu_mulai) {
                    $events->push([
                        'type' => 'delivery_departed',
                        'title' => 'Pengiriman Berangkat',
                        'link' => route('filament.admin.resources.pengiriman-drivers.view', ['record' => $delivery->id]),
                        'description' => 'Kendaraan berangkat untuk pengiriman',
                        'timestamp' => $delivery->waktu_mulai,
                        'icon' => 'heroicon-o-arrow-right',
                        'color' => 'orange',
                        'data' => [
                            'Nomor DO' => $do->kode,
                            'Kendaraan' => $do->kendaraan->nomor_polisi ?? 'N/A',
                            'Driver' => $do->user->name ?? 'N/A',
                            'Totalisator Awal' => $delivery->totalisator_awal ? number_format($delivery->totalisator_awal, 2) . ' L' : 'N/A',
                            'Dibuat Oleh' => $delivery->createdBy->name ?? 'System',
                        ]
                    ]);
                }

                if ($delivery->waktu_tiba) {
                    $events->push([
                        'type' => 'delivery_arrived',
                        'title' => 'Pengiriman Tiba',
                        'link' => route('filament.admin.resources.pengiriman-drivers.view', ['record' => $delivery->id]),
                        'description' => 'Kendaraan tiba di lokasi tujuan',
                        'timestamp' => $delivery->waktu_tiba,
                        'icon' => 'heroicon-o-map-pin',
                        'color' => 'teal',
                        'data' => [
                            'Nomor DO' => $do->kode,
                            'Totalisator Tiba' => $delivery->totalisator_tiba ? number_format($delivery->totalisator_tiba, 2) . ' L' : 'N/A',
                            'Volume Terkirim' => $delivery->totalisator_tiba && $delivery->totalisator_awal ?
                                number_format($delivery->totalisator_tiba - $delivery->totalisator_awal, 2) . ' L' : 'N/A',
                        ]
                    ]);
                }

                if ($delivery->waktu_pool_arrival) {
                    $events->push([
                        'type' => 'delivery_completed',
                        'title' => 'Kembali ke Pool',
                        'link' => route('filament.admin.resources.pengiriman-drivers.view', ['record' => $delivery->id]),
                        'description' => 'Kendaraan kembali ke pool',
                        'timestamp' => $delivery->waktu_pool_arrival,
                        'icon' => 'heroicon-o-check-badge',
                        'color' => 'emerald',
                        'data' => [
                            'Nomor DO' => $do->kode,
                            'Totalisator Akhir' => $delivery->totalisator_pool_return ? number_format($delivery->totalisator_pool_return, 2) . ' L' : 'N/A',
                            'Total Jarak' => $delivery->totalisator_pool_return && $delivery->totalisator_awal ?
                                number_format($delivery->totalisator_pool_return - $delivery->totalisator_awal, 2) . ' Km' : 'N/A',
                        ]
                    ]);
                }

                // Delivery Approval Events
                if ($delivery->approval_status && $delivery->approved_at) {
                    $events->push([
                        'type' => 'delivery_approval',
                        'title' => 'Persetujuan Pengiriman',
                        'link' => route('filament.admin.resources.pengiriman-drivers.view', ['record' => $delivery->id]),
                        'description' => 'Laporan pengiriman ' . ($delivery->approval_status === 'approved' ? 'disetujui' : 'ditolak'),
                        'timestamp' => $delivery->approved_at,
                        'icon' => $delivery->approval_status === 'approved' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle',
                        'color' => $delivery->approval_status === 'approved' ? 'green' : 'red',
                        'data' => [
                            'Status' => ucfirst($delivery->approval_status),
                            'Nomor DO' => $do->kode,
                            'Disetujui Oleh' => $delivery->approvedBy->name ?? 'N/A',
                            'Catatan' => $delivery->approval_notes ?? 'Tidak ada catatan',
                        ]
                    ]);
                }
            }
        }

        // Sort events by timestamp in ascending order (oldest first)
        return $events->sortBy('timestamp');
    }
}
