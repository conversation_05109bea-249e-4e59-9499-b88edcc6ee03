<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NeracaMapping extends Model
{
    use HasFactory;

    protected $table = 'neraca_mappings';

    protected $fillable = [
        'kode_akun',
        'nama_akun',
        'kategori_neraca',
        'urutan'
    ];

    /**
     * Get mappings grouped by category
     */
    public static function getMappingsByCategory()
    {
        return self::orderBy('urutan')
            ->orderBy('kode_akun')
            ->get()
            ->groupBy('kategori_neraca');
    }

    /**
     * Get account relationship
     */
    public function akun()
    {
        return $this->belongsTo(Akun::class, 'kode_akun', 'kode_akun');
    }
}
