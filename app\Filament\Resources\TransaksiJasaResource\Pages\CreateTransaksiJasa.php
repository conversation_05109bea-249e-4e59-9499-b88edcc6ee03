<?php

namespace App\Filament\Resources\TransaksiJasaResource\Pages;

use App\Filament\Resources\TransaksiJasaResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTransaksiJasa extends CreateRecord
{
    protected static string $resource = TransaksiJasaResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure tipe is always 'jasa'
        $data['tipe'] = 'jasa';
        
        // Set created_by
        $data['created_by'] = auth()->id();

        return $data;
    }

    public function getTitle(): string
    {
        return 'Buat Transaksi Jasa';
    }
}
