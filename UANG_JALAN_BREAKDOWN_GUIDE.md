# Uang Jalan Breakdown System

## Overview

Sistem uang jalan telah diupdate dari single input menjadi breakdown detail dengan 7 komponen terpisah. Total allowance amount akan dihitung otomatis dari semua komponen breakdown.

## Komponen Breakdown Uang Jalan

### 1. Uang Depot
- **Field**: `uang_depot`
- **Deskripsi**: Biaya depot untuk loading BBM
- **Keterangan**: `keterangan_depot`

### 2. Uang Jalan
- **Field**: `uang_jalan_amount`
- **Deskripsi**: Uang jalan perjalanan
- **Keterangan**: `keterangan_jalan`

### 3. Uang <PERSON>
- **Field**: `uang_bongkar`
- **Deskripsi**: Biaya bongkar di lokasi tujuan
- **Keterangan**: `keterangan_bongkar`

### 4. Uang Pas
- **Field**: `uang_pas`
- **Deskripsi**: Uang pas tol dan parkir
- **Keterangan**: `keterangan_pas`

### 5. Uang Lembur
- **Field**: `uang_lembur`
- **Deskripsi**: <PERSON>mbur karena kondisi khusus
- **Keterangan**: `keterangan_lembur`

### 6. Uang BBM
- **Field**: `uang_bbm`
- **Deskripsi**: BBM untuk perjalanan
- **Keterangan**: `keterangan_bbm`

### 7. Uang Tol
- **Field**: `uang_tol`
- **Deskripsi**: Biaya tol perjalanan
- **Keterangan**: `keterangan_tol`

## Database Changes

### Migration: `add_breakdown_fields_to_uang_jalan_table`
```sql
-- Breakdown fields
uang_depot DECIMAL(15,2) DEFAULT 0
uang_jalan_amount DECIMAL(15,2) DEFAULT 0
uang_bongkar DECIMAL(15,2) DEFAULT 0
uang_pas DECIMAL(15,2) DEFAULT 0
uang_lembur DECIMAL(15,2) DEFAULT 0
uang_bbm DECIMAL(15,2) DEFAULT 0
uang_tol DECIMAL(15,2) DEFAULT 0

-- Keterangan fields
keterangan_depot TEXT NULL
keterangan_jalan TEXT NULL
keterangan_bongkar TEXT NULL
keterangan_pas TEXT NULL
keterangan_lembur TEXT NULL
keterangan_bbm TEXT NULL
keterangan_tol TEXT NULL
```

### Model Updates
```php
// Auto-calculate total when saving
protected static function booted()
{
    static::saving(function ($uangJalan) {
        $uangJalan->nominal = $uangJalan->calculateTotal();
    });
}

// Helper methods
public function calculateTotal(): float
public function getBreakdownArray(): array
```

## User Interface

### 1. UangJalanResource Form
- **Total Amount**: Read-only field yang dihitung otomatis
- **Breakdown Section**: Collapsible section dengan grid 2 kolom
- **Live Calculation**: Total update otomatis saat input berubah
- **Keterangan Fields**: Textarea untuk setiap komponen

### 2. Table Display
- **Total Amount**: Kolom utama menampilkan total
- **Breakdown Summary**: Kolom tambahan menampilkan ringkasan komponen yang > 0

### 3. ViewDeliveryOrder
- **Informasi Uang Jalan**: Section existing dengan total
- **Breakdown Uang Jalan**: Section baru menampilkan detail breakdown

### 4. Driver Delivery Detail
- **Rincian Uang Jalan**: Card-based layout dengan icons
- **Responsive Grid**: 1-4 kolom tergantung screen size
- **Color-coded Icons**: Setiap komponen memiliki warna berbeda
- **Total Display**: Prominent total di bagian bawah

## Features

### Auto-Calculation
```php
// Real-time calculation in form
->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
    $total = ($get('uang_depot') ?? 0) +
             ($get('uang_jalan_amount') ?? 0) +
             ($get('uang_bongkar') ?? 0) +
             ($get('uang_pas') ?? 0) +
             ($get('uang_lembur') ?? 0) +
             ($get('uang_bbm') ?? 0) +
             ($get('uang_tol') ?? 0);
    $set('nominal', $total);
})
```

### Smart Display
- **Table**: Hanya menampilkan komponen yang > 0
- **Driver View**: Card layout dengan icons dan keterangan
- **Responsive**: Adaptif untuk mobile dan desktop

### Validation
- **Numeric**: Semua amount fields harus numeric
- **Non-negative**: Tidak boleh nilai negatif
- **Auto-save**: Total tersimpan otomatis

## Testing Data

### Test Case 1: Full Breakdown
```php
'uang_depot' => 200000,
'uang_jalan_amount' => 300000,
'uang_bongkar' => 150000,
'uang_pas' => 50000,
'uang_lembur' => 100000,
'uang_bbm' => 250000,
'uang_tol' => 75000,
// Total: Rp 1,125,000
```

### Test Case 2: Partial Breakdown
```php
'uang_depot' => 150000,
'uang_jalan_amount' => 400000,
'uang_pas' => 30000,
'uang_bbm' => 200000,
'uang_tol' => 60000,
// Total: Rp 840,000
```

### Test Case 3: Selected Components
```php
'uang_jalan_amount' => 500000,
'uang_bongkar' => 100000,
'uang_lembur' => 200000,
// Total: Rp 800,000
```

## File Structure

```
app/
├── Models/
│   └── UangJalan.php (UPDATED)
├── Filament/Resources/
│   ├── UangJalanResource.php (UPDATED)
│   └── DeliveryOrderResource/Pages/
│       └── ViewDeliveryOrder.php (UPDATED)

database/
├── migrations/
│   └── 2025_07_28_000005_add_breakdown_fields_to_uang_jalan_table.php (NEW)
└── seeders/
    └── UangJalanBreakdownSeeder.php (NEW)

resources/views/filament/pages/
└── driver-delivery-detail.blade.php (UPDATED)
```

## Benefits

1. **Transparency**: Detail breakdown untuk setiap komponen
2. **Flexibility**: Dapat mengatur komponen sesuai kebutuhan
3. **Accuracy**: Perhitungan otomatis menghindari error manual
4. **Audit Trail**: Keterangan untuk setiap komponen
5. **User Experience**: Interface yang intuitif dan responsive

## Usage Examples

### Creating Breakdown
1. Buka UangJalan form
2. Isi komponen yang diperlukan
3. Total akan dihitung otomatis
4. Tambahkan keterangan jika perlu
5. Save

### Viewing Breakdown
- **Admin**: Lihat di ViewDeliveryOrder atau UangJalan detail
- **Driver**: Lihat di Driver Delivery Detail page
- **Table**: Summary di list UangJalan

## Migration Notes

- **Backward Compatible**: Field `nominal` tetap ada dan berfungsi
- **Auto-calculation**: Total dihitung otomatis dari breakdown
- **Default Values**: Semua breakdown fields default 0
- **Safe Migration**: Tidak ada data loss
