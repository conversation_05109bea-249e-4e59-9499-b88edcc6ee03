<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class JadwalMasal extends Model
{
    use HasFactory;

    protected $table = 'jadwal_masal';

    protected $fillable = [
        'nama_jadwal',
        'tanggal_mulai',
        'tanggal_selesai',
        'shift_id',
        'entitas_id',
        'created_by',
        'keterangan',
        'generated_at',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'generated_at' => 'datetime',
    ];

    /**
     * Get the shift associated with this bulk schedule
     */
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who created this bulk schedule
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the entitas/entity for this bulk schedule
     */
    public function entitas()
    {
        return $this->belongsTo(Entitas::class, 'entitas_id');
    }

    /**
     * Get the employees associated with this bulk schedule
     */
    public function karyawan()
    {
        return $this->belongsToMany(User::class, 'jadwal_masal_karyawan', 'jadwal_masal_id', 'karyawan_id')
            ->withTimestamps();
    }

    /**
     * Get the generated schedules from this bulk schedule
     * Returns a collection of Schedule models that were generated from this jadwal masal
     */
    public function getGeneratedSchedules()
    {
        // Get karyawan IDs that are assigned to this jadwal masal
        $karyawanIds = $this->karyawan()->pluck('users.id');

        if ($karyawanIds->isEmpty()) {
            return \App\Models\Schedule::whereRaw('1 = 0'); // Return empty query
        }

        // Build query with base criteria
        $query = \App\Models\Schedule::whereIn('karyawan_id', $karyawanIds)
            ->whereBetween('tanggal_jadwal', [$this->tanggal_mulai, $this->tanggal_selesai])
            ->where('is_approved', true);

        // Add shift_id condition only if it's not null
        if ($this->shift_id) {
            $query->where('shift_id', $this->shift_id);
        }

        // Add entitas_id condition - be more flexible here
        if ($this->entitas_id) {
            $query->where(function ($q) {
                $q->where('entitas_id', $this->entitas_id)
                    ->orWhereNull('entitas_id'); // Include schedules without entitas for backward compatibility
            });
        }

        return $query;
    }

    /**
     * Generate individual schedules from this bulk schedule
     */
    public function generateSchedules()
    {
        // Check if already generated
        if ($this->generated_at) {
            throw new \Exception('Jadwal masal ini sudah pernah di-generate pada ' . $this->generated_at->format('d M Y H:i'));
        }

        // Validasi awal
        $karyawanIds = $this->karyawan()->pluck('users.id');
        if ($karyawanIds->isEmpty()) {
            throw new \Exception('Tidak ada karyawan yang dipilih untuk jadwal masal ini.');
        }

        $shift = $this->shift;
        if (!$shift) {
            throw new \Exception('Shift tidak ditemukan untuk jadwal masal ini.');
        }

        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $entitasId = $this->entitas_id;
        $createdBy = $this->created_by;

        $generatedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $errors = [];

        Log::info("Starting jadwal masal generation", [
            'jadwal_masal_id' => $this->id,
            'nama_jadwal' => $this->nama_jadwal,
            'total_karyawan' => $karyawanIds->count(),
            'total_days' => $startDate->diffInDays($endDate) + 1,
            'shift_id' => $shift->id,
            'entitas_id' => $entitasId
        ]);

        // Loop through each day in the date range
        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $dateString = $currentDate->format('Y-m-d');

            // Loop through each employee
            foreach ($karyawanIds as $karyawanId) {
                try {
                    // Check if a schedule already exists for this employee on this date
                    $existingSchedule = \App\Models\Schedule::where('karyawan_id', $karyawanId)
                        ->where('tanggal_jadwal', $dateString)
                        ->first();

                    $scheduleData = [
                        'karyawan_id' => $karyawanId,
                        'shift_id' => $shift->id,
                        'entitas_id' => $entitasId,
                        'supervisor_id' => $createdBy,
                        'tanggal_jadwal' => $dateString,
                        'waktu_masuk' => $shift->waktu_mulai,
                        'waktu_keluar' => $shift->waktu_selesai,
                        'is_approved' => true, // Auto-approve jadwal dari jadwal masal
                    ];

                    // If no schedule exists, create one
                    if (!$existingSchedule) {
                        \App\Models\Schedule::create($scheduleData);
                        $generatedCount++;

                        Log::debug("Created new schedule", [
                            'karyawan_id' => $karyawanId,
                            'tanggal_jadwal' => $dateString
                        ]);
                    } else {
                        // Update existing schedule
                        $existingSchedule->update([
                            'entitas_id' => $entitasId,
                            'shift_id' => $shift->id,
                            'supervisor_id' => $createdBy,
                            'waktu_masuk' => $shift->waktu_mulai,
                            'waktu_keluar' => $shift->waktu_selesai,
                            'is_approved' => true,
                        ]);
                        $generatedCount++;

                        Log::debug("Updated existing schedule", [
                            'schedule_id' => $existingSchedule->id,
                            'karyawan_id' => $karyawanId,
                            'tanggal_jadwal' => $dateString
                        ]);
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorKey = "karyawan_{$karyawanId}_date_{$dateString}";
                    $errors[$errorKey] = $e->getMessage();

                    Log::error("Failed to create/update schedule", [
                        'jadwal_masal_id' => $this->id,
                        'karyawan_id' => $karyawanId,
                        'tanggal_jadwal' => $dateString,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    // Continue with next employee instead of stopping
                    continue;
                }
            }

            // Move to the next day
            $currentDate->addDay();
        }

        // Mark as generated only if no critical errors
        if ($errorCount === 0 || $generatedCount > 0) {
            $this->update(['generated_at' => now()]);
        }

        $result = [
            'generated' => $generatedCount,
            'skipped' => $skippedCount,
            'errors' => $errorCount,
            'total_days' => $startDate->diffInDays($endDate) + 1,
            'total_employees' => $karyawanIds->count(),
            'expected_total' => $karyawanIds->count() * ($startDate->diffInDays($endDate) + 1)
        ];

        if ($errorCount > 0) {
            $result['error_details'] = $errors;
        }

        Log::info("Completed jadwal masal generation", array_merge($result, [
            'jadwal_masal_id' => $this->id,
            'nama_jadwal' => $this->nama_jadwal
        ]));

        return $result;
    }

    /**
     * Check if this bulk schedule has been generated
     */
    public function isGenerated(): bool
    {
        return !is_null($this->generated_at);
    }

    /**
     * Debug method untuk analisis jadwal masal generation
     */
    public function debugGenerationStatus(): array
    {
        $karyawanIds = $this->karyawan()->pluck('users.id');
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $shift = $this->shift;

        $debug = [
            'jadwal_masal_info' => [
                'id' => $this->id,
                'nama_jadwal' => $this->nama_jadwal,
                'tanggal_mulai' => $this->tanggal_mulai->format('Y-m-d'),
                'tanggal_selesai' => $this->tanggal_selesai->format('Y-m-d'),
                'shift_id' => $this->shift_id,
                'entitas_id' => $this->entitas_id,
                'generated_at' => $this->generated_at?->format('Y-m-d H:i:s'),
            ],
            'validation_checks' => [
                'has_karyawan' => !$karyawanIds->isEmpty(),
                'karyawan_count' => $karyawanIds->count(),
                'has_shift' => !is_null($shift),
                'shift_valid' => $shift && $shift->waktu_mulai && $shift->waktu_selesai,
                'date_range_valid' => $startDate->lte($endDate),
                'total_expected_days' => $startDate->diffInDays($endDate) + 1,
            ],
            'karyawan_assigned' => $karyawanIds->toArray(),
            'shift_info' => $shift ? [
                'id' => $shift->id,
                'nama_shift' => $shift->nama_shift,
                'waktu_mulai' => $shift->waktu_mulai,
                'waktu_selesai' => $shift->waktu_selesai,
            ] : null,
        ];

        // Check existing schedules
        if (!$karyawanIds->isEmpty()) {
            $existingSchedules = \App\Models\Schedule::whereIn('karyawan_id', $karyawanIds)
                ->whereBetween('tanggal_jadwal', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->get();

            $debug['existing_schedules'] = [
                'total_found' => $existingSchedules->count(),
                'expected_total' => $karyawanIds->count() * ($startDate->diffInDays($endDate) + 1),
                'completion_percentage' => $karyawanIds->count() > 0 ?
                    round(($existingSchedules->count() / ($karyawanIds->count() * ($startDate->diffInDays($endDate) + 1))) * 100, 2) : 0,
                'by_date' => $existingSchedules->groupBy('tanggal_jadwal')->map->count()->toArray(),
                'by_karyawan' => $existingSchedules->groupBy('karyawan_id')->map->count()->toArray(),
            ];

            // Check for missing schedules
            $missingSchedules = [];
            $currentDate = $startDate->copy();
            while ($currentDate->lte($endDate)) {
                $dateString = $currentDate->format('Y-m-d');
                foreach ($karyawanIds as $karyawanId) {
                    $exists = $existingSchedules->where('karyawan_id', $karyawanId)
                        ->where('tanggal_jadwal', $dateString)
                        ->isNotEmpty();

                    if (!$exists) {
                        $missingSchedules[] = [
                            'karyawan_id' => $karyawanId,
                            'tanggal_jadwal' => $dateString
                        ];
                    }
                }
                $currentDate->addDay();
            }

            $debug['missing_schedules'] = [
                'count' => count($missingSchedules),
                'details' => array_slice($missingSchedules, 0, 10), // Show first 10 missing
                'has_more' => count($missingSchedules) > 10
            ];
        }

        return $debug;
    }

    /**
     * Get generation status text with missing schedules info
     */
    public function getGenerationStatusText(): string
    {
        if ($this->isGenerated()) {
            try {
                $crosscheckData = $this->getCrosscheckData();
                $missingTotal = $crosscheckData['summary']['missing_total'];

                if ($missingTotal > 0) {
                    return 'Generate selesai - ' . number_format($missingTotal) . ' jadwal gagal';
                } else {
                    return 'Generate selesai - Semua jadwal berhasil';
                }
            } catch (\Exception) {
                return 'Sudah di-generate pada ' . $this->generated_at->format('d M Y H:i');
            }
        }

        return 'Belum di-generate';
    }

    /**
     * Get missing schedules count
     */
    public function getMissingSchedulesCount(): int
    {
        if (!$this->isGenerated()) {
            return 0;
        }

        try {
            $crosscheckData = $this->getCrosscheckData();
            return $crosscheckData['summary']['missing_total'];
        } catch (\Exception) {
            return 0;
        }
    }

    /**
     * Generate single missing schedule for specific employee and date
     */
    public function generateSingleSchedule(int $karyawanId, string $tanggalJadwal)
    {
        // Must be already generated
        if (!$this->generated_at) {
            throw new \Exception('Jadwal masal ini belum pernah di-generate.');
        }

        // Validate date is within range
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $targetDate = Carbon::parse($tanggalJadwal);

        if ($targetDate->lt($startDate) || $targetDate->gt($endDate)) {
            throw new \Exception('Tanggal jadwal tidak dalam rentang jadwal masal ini.');
        }

        // Validate karyawan is assigned to this jadwal masal
        $karyawanIds = $this->karyawan()->pluck('users.id');
        if (!$karyawanIds->contains($karyawanId)) {
            throw new \Exception('Karyawan tidak terdaftar dalam jadwal masal ini.');
        }

        // Check if schedule already exists
        $existingSchedule = \App\Models\Schedule::where('karyawan_id', $karyawanId)
            ->where('tanggal_jadwal', $tanggalJadwal)
            ->where('shift_id', $this->shift_id)
            ->where('is_approved', true)
            ->first();

        if ($existingSchedule) {
            throw new \Exception('Jadwal untuk karyawan ini pada tanggal tersebut sudah ada.');
        }

        $shift = $this->shift;
        if (!$shift) {
            throw new \Exception('Shift tidak ditemukan untuk jadwal masal ini.');
        }

        // Create the schedule
        $schedule = \App\Models\Schedule::create([
            'karyawan_id' => $karyawanId,
            'entitas_id' => $this->entitas_id,
            'shift_id' => $this->shift_id,
            'supervisor_id' => Auth::id(),
            'tanggal_jadwal' => $tanggalJadwal,
            'waktu_masuk' => $shift->waktu_mulai,
            'waktu_keluar' => $shift->waktu_selesai,
            'status' => 'scheduled',
            'is_approved' => true,
        ]);

        Log::info("Created single missing schedule", [
            'jadwal_masal_id' => $this->id,
            'karyawan_id' => $karyawanId,
            'tanggal_jadwal' => $tanggalJadwal,
            'schedule_id' => $schedule->id
        ]);

        return $schedule;
    }

    /**
     * Duplicate this bulk schedule with new date range
     */
    public function duplicate(string $tanggalMulai, string $tanggalSelesai): JadwalMasal
    {
        // Create new jadwal masal with same attributes but new dates
        $duplicatedJadwal = static::create([
            'nama_jadwal' => $this->nama_jadwal . ' (Copy)',
            'tanggal_mulai' => $tanggalMulai,
            'tanggal_selesai' => $tanggalSelesai,
            'shift_id' => $this->shift_id,
            'entitas_id' => $this->entitas_id,
            'created_by' => Auth::id() ?? $this->created_by,
            'keterangan' => $this->keterangan,
            'generated_at' => null, // Reset generation status
        ]);

        // Copy all assigned employees
        $karyawanIds = $this->karyawan()->pluck('users.id');
        if ($karyawanIds->isNotEmpty()) {
            $duplicatedJadwal->karyawan()->attach($karyawanIds);
        }

        return $duplicatedJadwal;
    }



    /**
     * Get crosscheck data between expected and actual generated schedules
     */
    public function getCrosscheckData(): array
    {
        $karyawanIds = $this->karyawan()->pluck('users.id');
        $totalKaryawan = $karyawanIds->count();

        // Calculate expected schedules
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $expectedTotal = $totalKaryawan * $totalDays;

        // Get actual generated schedules
        $actualSchedules = $this->getGeneratedSchedules()->get();
        $actualTotal = $actualSchedules->count();

        // Calculate per-employee breakdown
        $employeeBreakdown = [];
        foreach ($karyawanIds as $karyawanId) {
            $karyawan = \App\Models\Karyawan::find($karyawanId);
            $employeeSchedules = $actualSchedules->where('karyawan_id', $karyawanId);

            $employeeBreakdown[] = [
                'karyawan_id' => $karyawanId,
                'nama_karyawan' => $karyawan->nama_lengkap ?? 'Unknown',
                'nip' => $karyawan->nip ?? '-',
                'expected_schedules' => $totalDays,
                'actual_schedules' => $employeeSchedules->count(),
                'missing_schedules' => $totalDays - $employeeSchedules->count(),
                'is_complete' => $employeeSchedules->count() === $totalDays,
                'missing_dates' => $this->getMissingDatesForEmployee($employeeSchedules)
            ];
        }

        // Calculate per-date breakdown
        $dateBreakdown = [];

        // Group actual schedules by date first for better performance
        $schedulesByDate = $actualSchedules->groupBy(function ($schedule) {
            $scheduleDate = $schedule->tanggal_jadwal;

            // Handle if tanggal_jadwal is already a Carbon instance
            if ($scheduleDate instanceof Carbon) {
                return $scheduleDate->format('Y-m-d');
            }

            // Handle if tanggal_jadwal is a string
            return Carbon::parse($scheduleDate)->format('Y-m-d');
        });

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $targetDate = $currentDate->format('Y-m-d');
            $dateSchedules = $schedulesByDate->get($targetDate, collect([]));

            $dateBreakdown[] = [
                'tanggal' => $targetDate,
                'tanggal_formatted' => $currentDate->format('d M Y'),
                'day_name' => $currentDate->format('l'),
                'expected_schedules' => $totalKaryawan,
                'actual_schedules' => $dateSchedules->count(),
                'missing_schedules' => $totalKaryawan - $dateSchedules->count(),
                'is_complete' => $dateSchedules->count() === $totalKaryawan,
                'missing_employees' => $this->getMissingEmployeesForDate($dateSchedules, $karyawanIds),
                'existing_employees' => $dateSchedules->pluck('karyawan_id')->toArray() // Debug info
            ];

            $currentDate->addDay();
        }

        return [
            'summary' => [
                'total_karyawan' => $totalKaryawan,
                'total_days' => $totalDays,
                'expected_total' => $expectedTotal,
                'actual_total' => $actualTotal,
                'missing_total' => $expectedTotal - $actualTotal,
                'completion_percentage' => $expectedTotal > 0 ? round(($actualTotal / $expectedTotal) * 100, 2) : 0,
                'is_complete' => $actualTotal === $expectedTotal,
                'generated_at' => $this->generated_at,
            ],
            'employee_breakdown' => $employeeBreakdown,
            'date_breakdown' => $dateBreakdown,
        ];
    }

    /**
     * Get missing dates for a specific employee
     */
    private function getMissingDatesForEmployee($employeeSchedules): array
    {
        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);
        $existingDates = $employeeSchedules->pluck('tanggal_jadwal')->map(function ($date) {
            return Carbon::parse($date)->format('Y-m-d');
        })->toArray();

        $missingDates = [];
        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            if (!in_array($currentDate->format('Y-m-d'), $existingDates)) {
                $missingDates[] = [
                    'tanggal' => $currentDate->format('Y-m-d'),
                    'tanggal_formatted' => $currentDate->format('d M Y'),
                    'day_name' => $currentDate->format('l')
                ];
            }
            $currentDate->addDay();
        }

        return $missingDates;
    }

    /**
     * Get missing employees for a specific date
     */
    private function getMissingEmployeesForDate($dateSchedules, $karyawanIds): array
    {
        // Get employee IDs that have schedules on this date
        $existingEmployeeIds = collect($dateSchedules)->pluck('karyawan_id')->toArray();

        // Find missing employee IDs
        $missingEmployeeIds = $karyawanIds->diff($existingEmployeeIds);

        $missingEmployees = [];
        foreach ($missingEmployeeIds as $karyawanId) {
            $karyawan = \App\Models\Karyawan::find($karyawanId);
            if ($karyawan) {
                $missingEmployees[] = [
                    'karyawan_id' => $karyawanId,
                    'nama_karyawan' => $karyawan->nama_lengkap,
                    'nip' => $karyawan->nip ?? '-'
                ];
            }
        }

        return $missingEmployees;
    }

    /**
     * Debug method to check crosscheck data
     */
    public function debugCrosscheckData(): array
    {
        $karyawanIds = $this->karyawan()->pluck('users.id');
        $actualSchedules = $this->getGeneratedSchedules()->get();

        $debug = [
            'jadwal_masal_info' => [
                'id' => $this->id,
                'nama_jadwal' => $this->nama_jadwal,
                'tanggal_mulai' => $this->tanggal_mulai->format('Y-m-d'),
                'tanggal_selesai' => $this->tanggal_selesai->format('Y-m-d'),
                'shift_id' => $this->shift_id,
                'entitas_id' => $this->entitas_id,
            ],
            'karyawan_assigned' => $karyawanIds->toArray(),
            'total_schedules_found' => $actualSchedules->count(),
            'schedules_by_date' => [],
            'schedules_by_employee' => [],
        ];

        // Group by date
        foreach ($actualSchedules as $schedule) {
            $date = Carbon::parse($schedule->tanggal_jadwal)->format('Y-m-d');
            if (!isset($debug['schedules_by_date'][$date])) {
                $debug['schedules_by_date'][$date] = [];
            }
            $debug['schedules_by_date'][$date][] = [
                'karyawan_id' => $schedule->karyawan_id,
                'nama_karyawan' => $schedule->karyawan->nama_lengkap ?? 'Unknown',
                'shift_id' => $schedule->shift_id,
                'entitas_id' => $schedule->entitas_id,
            ];
        }

        // Group by employee
        foreach ($actualSchedules as $schedule) {
            $karyawanId = $schedule->karyawan_id;
            if (!isset($debug['schedules_by_employee'][$karyawanId])) {
                $debug['schedules_by_employee'][$karyawanId] = [
                    'nama_karyawan' => $schedule->karyawan->nama_lengkap ?? 'Unknown',
                    'schedules' => []
                ];
            }
            $debug['schedules_by_employee'][$karyawanId]['schedules'][] = [
                'tanggal_jadwal' => Carbon::parse($schedule->tanggal_jadwal)->format('Y-m-d'),
                'shift_id' => $schedule->shift_id,
                'entitas_id' => $schedule->entitas_id,
            ];
        }

        return $debug;
    }
}
