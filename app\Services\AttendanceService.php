<?php

namespace App\Services;

use App\Models\Absensi;
use App\Models\Schedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AttendanceService
{
    /**
     * Determine attendance status based on schedule and time
     */
    public static function determineAttendanceStatus($karyawanId, $datetime = null, $periode = 1): string
    {
        if (!$datetime) {
            $datetime = Carbon::now();
        }

        try {
            // Get schedule for the employee on this date
            $schedule = Schedule::with(['shift', 'karyawan', 'entitas'])
                ->where('karyawan_id', $karyawanId)
                ->whereDate('tanggal_jadwal', $datetime->toDateString())
                ->first();

            if (!$schedule || !$schedule->shift) {
                return 'hadir'; // Default status if no schedule
            }

            $shift = $schedule->shift;
            $actualTime = Carbon::parse($datetime);

            // Handle split shift
            if ($shift->isSplitShift()) {
                $periods = $shift->getWorkPeriods();

                foreach ($periods as $period) {
                    if ($period['periode'] == $periode) {
                        $shiftStart = Carbon::parse($period['waktu_mulai']);
                        $toleranceMinutes = config('app.toleransi_sesudah_absen_masuk');

                        return $actualTime->greaterThan($shiftStart->addMinutes($toleranceMinutes))
                            ? 'terlambat'
                            : 'hadir';
                    }
                }
            } else {
                // Regular shift logic
                $shiftStart = Carbon::parse($shift->waktu_mulai);
                $toleranceMinutes = config('app.toleransi_sesudah_absen_masuk');

                return $actualTime->greaterThan($shiftStart->addMinutes($toleranceMinutes))
                    ? 'terlambat'
                    : 'hadir';
            }

            return 'hadir';
        } catch (\Exception $e) {
            Log::warning('Failed to determine attendance status: ' . $e->getMessage());
            return 'hadir';
        }
    }
}
