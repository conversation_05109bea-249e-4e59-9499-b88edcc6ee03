<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DeliveryOrder;

class UpdateDeliveryOrderStatusSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Updating delivery order status...');

        $updated = DeliveryOrder::whereNull('status')
            ->orWhere('status', '')
            ->update(['status' => 'pending']);

        $this->command->info("Updated {$updated} delivery orders with default status 'pending'");
    }
}
