<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class DeliveryEvidence extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $table = 'delivery_evidence';

    protected $fillable = [
        'id_pengiriman_driver',
        'evidence_type',
        'file_name',
        'file_path',
        'original_name',
        'mime_type',
        'file_size',
        'description',
        'sequence_number',
        'uploaded_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'file_size' => 'integer',
        'sequence_number' => 'integer',
    ];

    // Evidence type constants
    public const EVIDENCE_TYPES = [
        'foto_pemutusan_segel_atas' => 'Foto Pemutusan Segel Atas',
        'foto_pemutusan_segel_bawah' => 'Foto Pemutusan Segel Bawah',
        'foto_tera' => 'Foto Tera',
        'foto_sample_bbm' => 'Foto Sample BBM',
        'foto_tangki_mt_kosong' => 'Foto Tangki MT Kosong Setelah Selesai Bongkar',
        'foto_mt' => 'Foto MT',
        'foto_pembongkaran' => 'Foto Pembongkaran/Proses Bongkar dan Tangki Bongkar',
    ];

    // Relationships
    public function pengirimanDriver(): BelongsTo
    {
        return $this->belongsTo(PengirimanDriver::class, 'id_pengiriman_driver');
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    // Media Collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('evidence_photos')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->performOnCollections('evidence_photos');

        $this->addMediaConversion('preview')
            ->width(300)
            ->height(300)
            ->performOnCollections('evidence_photos');
    }

    // Helper methods
    public function getEvidenceTypeLabel(): string
    {
        return self::EVIDENCE_TYPES[$this->evidence_type] ?? $this->evidence_type;
    }

    public function getFileUrl(): string
    {
        return $this->getFirstMediaUrl('evidence_photos');
    }

    public function getThumbUrl(): string
    {
        return $this->getFirstMediaUrl('evidence_photos', 'thumb');
    }

    public function getPreviewUrl(): string
    {
        return $this->getFirstMediaUrl('evidence_photos', 'preview');
    }

    // Scopes
    public function scopeByType($query, string $type)
    {
        return $query->where('evidence_type', $type);
    }

    public function scopeByPengirimanDriver($query, int $pengirimanDriverId)
    {
        return $query->where('id_pengiriman_driver', $pengirimanDriverId);
    }
}
