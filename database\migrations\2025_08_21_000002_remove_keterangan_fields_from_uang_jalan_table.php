<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Remove unused keterangan fields from uang_jalan table
     */
    public function up(): void
    {
        Schema::table('uang_jalan', function (Blueprint $table) {
            $table->dropColumn([
                'keterangan_depot',
                'keterangan_jalan',
                'keterangan_bongkar',
                'keterangan_pas',
                'keterangan_lembur',
                'keterangan_bbm',
                'keterangan_tol',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     * Add back the columns if rollback is needed
     */
    public function down(): void
    {
        Schema::table('uang_jalan', function (Blueprint $table) {
            $table->text('keterangan_depot')->nullable()->after('uang_tol');
            $table->text('keterangan_jalan')->nullable()->after('keterangan_depot');
            $table->text('keterangan_bongkar')->nullable()->after('keterangan_jalan');
            $table->text('keterangan_pas')->nullable()->after('keterangan_bongkar');
            $table->text('keterangan_lembur')->nullable()->after('keterangan_pas');
            $table->text('keterangan_bbm')->nullable()->after('keterangan_lembur');
            $table->text('keterangan_tol')->nullable()->after('keterangan_bbm');
        });
    }
};
