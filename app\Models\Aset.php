<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Aset extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aset';

    protected $fillable = [
        'kode_aset',
        'nama_aset',
        'deskripsi_aset',
        'kategori_aset',
        'nilai_aset',
        'useful_life_months',
        'salvage_value',
        'depreciation_start_date',
        'last_depreciation_date',
        'is_depreciable',
        'depreciation_notes',
        'tanggal_akuisisi',
        'id_akun_aset',
        'id_akun_akuisisi',
        'id_akun_akumulasi',
        'id_akun_beban',
        'created_by',
    ];

    protected $casts = [
        'nilai_aset' => 'decimal:2',
        'salvage_value' => 'decimal:2',
        'useful_life_months' => 'integer',
        'is_depreciable' => 'boolean',
        'tanggal_akuisisi' => 'date',
        'depreciation_start_date' => 'date',
        'last_depreciation_date' => 'date',
    ];

    // Relationships
    public function akunAset()
    {
        return $this->belongsTo(Akun::class, 'id_akun_aset');
    }

    public function akunAkumulasi()
    {
        return $this->belongsTo(Akun::class, 'id_akun_akumulasi');
    }

    public function akunBeban()
    {
        return $this->belongsTo(Akun::class, 'id_akun_beban');
    }

    public function akunAkuisisi()
    {
        return $this->belongsTo(Akun::class, 'id_akun_akuisisi');
    }

    // Asset Categories
    const ASSET_CATEGORIES = [
        'Tanah' => 'Tanah',
        'Bangunan' => 'Bangunan',
        'Peralatan' => 'Peralatan',
        'Transportasi' => 'Transportasi',
        'Lainnya' => 'Lainnya',
    ];

    public function depreciations()
    {
        return $this->hasMany(AsetDepreciation::class, 'aset_id')->latest();
    }

    public function accumulatedDepreciations()
    {
        return $this->depreciations()->sum('amount');
    }

    public function mustBeDepreciated()
    {
        return $this->nilai_aset - $this->salvage_value;
    }

    // Helper Methods
    public function calculateDepreciation()
    {
        if (!$this->is_depreciable || $this->kategori_aset === 'Tanah') {
            return 0;
        }

        $depreciableAmount = $this->mustBeDepreciated();

        return $depreciableAmount / $this->useful_life_months;
    }

    public function calculateCurrentBookValue()
    {
        return $this->nilai_aset - $this->accumulatedDepreciations();
    }

    public function getYearsElapsed()
    {
        if (!$this->depreciation_start_date) {
            return 0;
        }

        return now()->diffInYears($this->depreciation_start_date);
    }

    public function getMonthsElapsed()
    {
        if (!$this->depreciation_start_date) {
            return 0;
        }

        return now()->diffInMonths($this->depreciation_start_date);
    }

    public function processDepreciation()
    {
        if (!$this->is_depreciable || $this->kategori_aset === 'Tanah') {
            return [
                "message" => "Aset ini tidak dapat didepresiasi.",
                "amount" => 0
            ];
        }

        $monthlyDepreciation = $this->calculateDepreciation();
        if ($monthlyDepreciation <= 0) {
            return [
                "message" => "Tidak ada depresiasi yang perlu diproses.",
                "amount" => 0
            ];
        }

        // Titik awal depresiasi
        $lastDate = $this->last_depreciation_date
            ? Carbon::parse($this->last_depreciation_date)
            : Carbon::parse($this->depreciation_start_date);

        $totalDepreciated = 0;
        $messages = [];

        // Loop sampai "jatuh tempo" depresiasi berikutnya belum tercapai
        while (now()->greaterThanOrEqualTo($lastDate->copy()->addMonth())) {
            if ($this->isFullyDepreciated()) {
                $messages[] = "Aset sudah fully depreciated.";
                break;
            }

            $nextDepDate = $lastDate->copy()->addMonth();

            $amount = $monthlyDepreciation;
            if ($this->accumulatedDepreciations() + $amount > $this->mustBeDepreciated()) {
                $amount = $this->mustBeDepreciated() - $this->accumulatedDepreciations();
            }

            if ($amount <= 0) {
                break;
            }

            // Simpan record depresiasi
            $this->depreciations()->create([
                'depreciation_date' => $nextDepDate,
                'amount' => $amount,
            ]);

            $this->last_depreciation_date = $nextDepDate;
            $this->save();

            // Buat jurnal akuntansi
            $journal = Journal::create([
                'transaction_date' => $nextDepDate,
                'reference_number' => $this->kode_aset,
                'source_type' => class_basename($this),
                'description' => 'Depresiasi Aset: ' . $this->nama_aset . ' - ' . $this->kode_aset,
                'status' => 'Posted',
                'created_by' => $this->created_by,
            ]);

            $journal->journalEntries()->createMany([
                [
                    'account_id' => $this->id_akun_beban,
                    'credit' => 0,
                    'debit' => $amount,
                    'description' => 'Debit Akun Beban Aset: ' . $this->nama_aset,
                ],
                [
                    'account_id' => $this->id_akun_akumulasi,
                    'credit' => $amount,
                    'debit' => 0,
                    'description' => 'Kredit Akumulasi Depresiasi Aset: ' . $this->nama_aset,
                ],
            ]);

            $totalDepreciated += $amount;
            $messages[] = "Depresiasi berhasil untuk periode " . $nextDepDate->format('j F Y') . " sebesar Rp " . number_format($amount, 2, ',', '.');

            // Geser lastDate ke bulan berikutnya
            $lastDate = $nextDepDate;
        }

        if ($totalDepreciated == 0) {
            $messages[] = "Belum waktunya depresiasi berikutnya.";
        }

        return [
            "message" => implode(" | ", $messages),
            "amount" => $totalDepreciated
        ];
    }

    public function isFullyDepreciated()
    {
        return $this->accumulatedDepreciations() >= $this->mustBeDepreciated();
    }

    public function getRemainingDepreciableAmount()
    {
        return max(0, $this->mustBeDepreciated() - $this->accumulatedDepreciations());
    }

    public function getDepreciationPercentage()
    {
        if ($this->nilai_aset == 0) return 0;
        return ($this->accumulatedDepreciations() / $this->nilai_aset) * 100;
    }

    // Scopes
    public function scopeDepreciable($query)
    {
        return $query->where('is_depreciable', true)->where('kategori_aset', '!=', 'Tanah');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('kategori_aset', $category);
    }

    public function getLastDepreciationDateFormattedAttribute()
    {
        return $this->last_depreciation_date ? Carbon::parse($this->last_depreciation_date)->format('d F Y') : 'Belum Ada';
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($asset) {
            // Set depreciation start date if not set
            if (!$asset->depreciation_start_date && $asset->tanggal_akuisisi) {
                $asset->depreciation_start_date = $asset->tanggal_akuisisi;
            }
        });
    }

    protected static function booted()
    {
        static::created(function ($asset) {
            if (!empty($asset->id_akun_aset) && !empty($asset->id_akun_akuisisi)) {
                $journal = Journal::create([
                    'transaction_date' => now(),
                    'reference_number' => $asset->kode_aset,
                    'source_type' => class_basename($asset),
                    'description' => 'Pencatatan Aset: ' . $asset->nama_aset . ' - ' . $asset->kode_aset,
                    'status' => 'Posted',
                    'created_by' => $asset->created_by,
                ]);

                $journal->journalEntries()->createMany([
                    [
                        'account_id' => $asset->id_akun_aset,
                        'debit' => $asset->nilai_aset,
                        'credit' => 0,
                        'description' => 'Debit Aset: ' . $asset->nama_aset,
                    ],
                    [
                        'account_id' => $asset->id_akun_akuisisi,
                        'debit' => 0,
                        'credit' => $asset->nilai_aset,
                        'description' => 'Kredit untuk pembelian aset: ' . $asset->nama_aset,
                    ],
                ]);
            }
        });
    }
}
