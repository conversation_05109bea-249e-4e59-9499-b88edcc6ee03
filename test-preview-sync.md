# Invoice Preview Template Sync - FIXED

## Problem Identified
Template preview ketinggalan dari template PDF yang sudah benar.

## Fixes Applied

### 1. Variable Scope Fix
**Problem**: `$subtotalAmount` undefined karena hanya didefinisikan di dalam blok `@else`
**Solution**: Pindahkan definisi variabel ke luar blok kondisional

```php
// BEFORE (WRONG)
@if ($hasDetails)
    // ... 
@else
    @php
        $subtotalAmount = $finalTotalPenjualan; // Only defined here
    @endphp
@endif
// $subtotalAmount used here - UNDEFINED!

// AFTER (CORRECT)
@php
    // Calculate totals for display - MOVED OUTSIDE CONDITIONAL
    $totalVolume = 0;
    $subtotalAmount = 0;
    
    if ($hasDetails) {
        foreach ($details as $detail) {
            $totalVolume += $detail->volume_item ?? 0;
            $subtotalAmount += ($detail->harga_jual ?? 0) * ($detail->volume_item ?? 0);
        }
    } else {
        $totalVolume = 10000; // Default volume
        $subtotalAmount = $finalTotalPenjualan;
    }
@endphp

@if ($hasDetails)
    // ...
@else
    @php
        // Reset item number for fallback display
        $itemNumber = 1;
    @endphp
@endif
// $subtotalAmount available here - DEFINED!
```

### 2. Template Synchronization
**Problem**: Preview template structure berbeda dengan PDF template
**Solution**: Sinkronkan struktur dan logic

#### Key Changes:
1. **Variable Definition**: Moved outside conditional blocks
2. **PBBKB Fix**: Changed `$record->biaya_pbbkb` to `$finalBiayaPbbkb`
3. **Consistent Logic**: Same calculation logic as PDF template

### 3. Current Template Structure (Both PDF & Preview)

```php
@php
    // Variables available globally
    $totalVolume = 0;
    $subtotalAmount = 0;
    
    if ($hasDetails) {
        // Use actual transaction data
        foreach ($details as $detail) {
            $totalVolume += $detail->volume_item ?? 0;
            $subtotalAmount += ($detail->harga_jual ?? 0) * ($detail->volume_item ?? 0);
        }
    } else {
        // Use fallback data
        $totalVolume = 10000;
        $subtotalAmount = $finalTotalPenjualan;
    }
@endphp

<!-- BBM Item -->
<tr>
    <td>{{ $itemNumber++ }}.</td>
    <td class="text-left">
        BBM BIOSOLAR INDUSTRI {{ number_format($totalVolume, 0, ',', '.') }} liter<br>
        wilayah Kab. Siak Polongan, Polongan
    </td>
    <td class="text-right">
        @php
            $hargaSatuan = 0;
            if ($hasDetails) {
                $hargaSatuan = $details->first()->harga_jual ?? 10000; // From database
            } else {
                $hargaSatuan = $subtotalAmount > 0 && $totalVolume > 0 ? $subtotalAmount / $totalVolume : 10000;
            }
        @endphp
        Rp. {{ number_format($hargaSatuan, 0, ',', '.') }}
    </td>
    <td class="text-right">{{ number_format($totalVolume, 0, ',', '.') }} Liter</td>
    <td class="text-right">
        @if ($includePpn)
            Rp. {{ number_format($finalTotalPajak, 0, ',', '.') }}
        @else
            --
        @endif
    </td>
    <td class="text-right">Rp. {{ number_format($subtotalAmount, 0, ',', '.') }}</td>
</tr>

<!-- Single Polongan Item -->
@if ($includeOperasional && $finalBiayaOperasional > 0)
    <tr>
        <td>{{ $itemNumber++ }}.</td>
        <td class="text-left">
            Operasional Kerja<br>
            - Polongan
        </td>
        <td class="text-right">
            @php
                $hargaOperasional = $finalBiayaOperasional > 0 && $totalVolume > 0 ? $finalBiayaOperasional / $totalVolume : 968;
            @endphp
            {{ number_format($hargaOperasional, 0, ',', '.') }}
        </td>
        <td class="text-right">{{ number_format($totalVolume, 0, ',', '.') }} liter</td>
        <td class="text-right">--</td>
        <td class="text-right">Rp. {{ number_format($finalBiayaOperasional, 0, ',', '.') }}</td>
    </tr>
@endif

<!-- PPN Row -->
@if ($includePpn)
    <tr>
        <td>{{ $itemNumber++ }}.</td>
        <td class="text-left">PPN</td>
        <td class="text-right"></td>
        <td class="text-right"></td>
        <td class="text-right"></td>
        <td class="text-right">Rp. {{ number_format($finalTotalPajak, 0, ',', '.') }}</td>
    </tr>
@endif

<!-- PBBKB Row -->
@if ($includePbbkb && $finalBiayaPbbkb > 0)
    <tr>
        <td>{{ $itemNumber++ }}.</td>
        <td class="text-left">
            PBBKB BBM Solar Industri Pertamina<br>
            {{ number_format($finalBiayaPbbkb / 2545, 1, ',', '.') }} x {{ number_format($totalVolume, 0, ',', '.') }} Liter
        </td>
        <td class="text-right"></td>
        <td class="text-right"></td>
        <td class="text-right"></td>
        <td class="text-right">Rp. {{ number_format($finalBiayaPbbkb, 0, ',', '.') }}</td>
    </tr>
@endif

<!-- Totals -->
<tr class="totals-row">
    <td colspan="5" style="text-align: right; font-weight: bold;">Total Penjualan</td>
    <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($subtotalAmount + $finalBiayaOperasional, 0, ',', '.') }}</td>
</tr>

<tr class="totals-row">
    <td colspan="5" style="text-align: right; font-weight: bold;">Total Pajak</td>
    <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($finalTotalPajak, 0, ',', '.') }}</td>
</tr>

<tr class="final-total-row">
    <td colspan="5" style="text-align: right; font-weight: bold;">Total Invoice Termasuk Pajak</td>
    <td class="text-right" style="font-weight: bold;">Rp. {{ number_format($finalTotalInvoice, 0, ',', '.') }}</td>
</tr>
```

## Status
✅ **Preview template now synchronized with PDF template**
✅ **No more undefined variable errors**
✅ **Consistent data flow and calculations**
✅ **Single Polongan item with database prices**
✅ **Proper fallback handling**

Template preview sekarang sudah sinkron dengan template PDF yang benar!
