<x-filament-panels::page>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <div class="space-y-6">
        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter Customer Analytics</h3>
            {{ $this->form }}
        </div>

        <!-- Customer KPI Cards -->
        @php
            $kpiData = $this->getCustomerKpiData();
        @endphp

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Total Customers -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 border-2 border-blue-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.3), 0 4px 6px -2px rgba(59, 130, 246, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-users class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Total Customers
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['total_customers']) }}
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    {{ number_format($kpiData['active_customers']) }} active ({{ $kpiData['customer_activation_rate'] }}%)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Customers -->
            <div class="bg-gradient-to-r from-green-600 to-green-700 border-2 border-green-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(34, 197, 94, 0.3), 0 4px 6px -2px rgba(34, 197, 94, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-user-plus class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    New Customers
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ number_format($kpiData['new_customers']) }}
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    dalam periode ini
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Retention -->
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 border-2 border-purple-400 overflow-hidden shadow-xl rounded-lg" style="box-shadow: 0 10px 25px -3px rgba(147, 51, 234, 0.3), 0 4px 6px -2px rgba(147, 51, 234, 0.1) !important;">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-heart class="h-8 w-8 text-gray-800 dark:text-white" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-semibold text-gray-800 dark:text-white truncate">
                                    Retention Rate
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ $kpiData['retention_rate'] }}%
                                </dd>
                                <dd class="text-sm text-gray-700 dark:text-gray-200">
                                    vs periode sebelumnya
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Average CLV -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-banknotes class="h-6 w-6 text-green-500 dark:text-green-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Average Customer Lifetime Value
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($kpiData['avg_clv'], 0, ',', '.') }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Customers -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <x-heroicon-o-chart-pie class="h-6 w-6 text-blue-500 dark:text-blue-400" />
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
                                    Customer Activation Rate
                                </dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $kpiData['customer_activation_rate'] }}%
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Customer Segmentation Chart -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Segmentation (RFM)</h3>
                <div class="h-64">
                    <canvas id="customerSegmentationChart"></canvas>
                </div>
            </div>

            <!-- Customer Type Distribution -->
            <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Type Distribution</h3>
                <div class="h-64">
                    <canvas id="customerTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Customer Growth Trend Chart -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Growth Trend</h3>
            <div class="h-80">
                <canvas id="customerGrowthChart"></canvas>
            </div>
        </div>

        <!-- Top Customers Table -->
        <div class="bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Top 20 Customers by Value</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Customer
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Type
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Orders
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Revenue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Avg Order Value
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Last Order
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($this->getTopCustomersByValueData() as $customer)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $customer->customer_name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($customer->customer_type === 'corporate') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($customer->customer_type) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ number_format($customer->total_orders) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($customer->total_revenue, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    Rp {{ number_format($customer->avg_order_value, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ Carbon\Carbon::parse($customer->last_order_date)->format('d/m/Y') }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            let customerSegmentationChart;
            let customerTypeChart;
            let customerGrowthChart;

            function initializeCharts() {
                // Customer Segmentation Chart
                const segmentationData = @json($this->getCustomerSegmentationData());
                const segmentationCtx = document.getElementById('customerSegmentationChart').getContext('2d');
                
                if (customerSegmentationChart) {
                    customerSegmentationChart.destroy();
                }
                
                customerSegmentationChart = new Chart(segmentationCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Champions', 'Loyal Customers', 'Potential Loyalists', 'New Customers', 'At Risk', 'Cannot Lose Them', 'Hibernating'],
                        datasets: [{
                            data: [
                                segmentationData.champions,
                                segmentationData.loyal_customers,
                                segmentationData.potential_loyalists,
                                segmentationData.new_customers,
                                segmentationData.at_risk,
                                segmentationData.cannot_lose_them,
                                segmentationData.hibernating
                            ],
                            backgroundColor: [
                                '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B',
                                '#EF4444', '#F97316', '#6B7280'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });

                // Customer Type Chart
                const typeData = @json($this->getCustomerTypeDistributionData());
                const typeCtx = document.getElementById('customerTypeChart').getContext('2d');
                
                if (customerTypeChart) {
                    customerTypeChart.destroy();
                }
                
                customerTypeChart = new Chart(typeCtx, {
                    type: 'bar',
                    data: {
                        labels: typeData.map(d => d.type.charAt(0).toUpperCase() + d.type.slice(1)),
                        datasets: [{
                            label: 'Customer Count',
                            data: typeData.map(d => d.customer_count),
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgb(59, 130, 246)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        }, {
                            label: 'Total Revenue',
                            data: typeData.map(d => d.total_revenue),
                            type: 'line',
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });

                // Customer Growth Chart
                const growthData = @json($this->getCustomerGrowthTrendData());
                const growthCtx = document.getElementById('customerGrowthChart').getContext('2d');
                
                if (customerGrowthChart) {
                    customerGrowthChart.destroy();
                }
                
                customerGrowthChart = new Chart(growthCtx, {
                    type: 'line',
                    data: {
                        labels: growthData.map(d => d.month),
                        datasets: [{
                            label: 'New Customers',
                            data: growthData.map(d => d.new_customers),
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1,
                            fill: true
                        }, {
                            label: 'Active Customers',
                            data: growthData.map(d => d.active_customers),
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                });
            }

            // Initialize charts on page load
            document.addEventListener('DOMContentLoaded', function() {
                initializeCharts();
            });

            // Listen for refresh events from Livewire
            document.addEventListener('livewire:init', () => {
                Livewire.on('refresh-charts', () => {
                    setTimeout(() => {
                        location.reload();
                    }, 100);
                });
            });
        </script>
    @endpush
</x-filament-panels::page>
