<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration removes legacy fields from invoice table after ensuring data integrity.
     * These fields should be handled by separate models (Receipt, TaxInvoice, etc.)
     */
    public function up(): void
    {
        // First, migrate any existing data from legacy fields to proper fields
        $this->migrateExistingData();
        
        // Then remove the legacy fields
        Schema::table('invoice', function (Blueprint $table) {
            // Remove legacy status fields (use main 'status' field instead)
            if (Schema::hasColumn('invoice', 'status_invoice')) {
                $table->dropColumn('status_invoice');
            }
            
            if (Schema::hasColumn('invoice', 'status_kirim')) {
                $table->dropColumn('status_kirim');
            }
            
            if (Schema::hasColumn('invoice', 'status_arsip')) {
                $table->dropColumn('status_arsip');
            }
            
            if (Schema::hasColumn('invoice', 'status_konfirmasi')) {
                $table->dropColumn('status_konfirmasi');
            }
            
            if (Schema::hasColumn('invoice', 'status_bayar')) {
                $table->dropColumn('status_bayar');
            }
            
            // Remove delivery/shipping fields (these belong to DeliveryOrder)
            if (Schema::hasColumn('invoice', 'tanggal_kirim')) {
                $table->dropColumn('tanggal_kirim');
            }
            
            if (Schema::hasColumn('invoice', 'metode_kirim')) {
                $table->dropColumn('metode_kirim');
            }
            
            if (Schema::hasColumn('invoice', 'penerima')) {
                $table->dropColumn('penerima');
            }
            
            // Remove archive fields (these should be in separate archive system)
            if (Schema::hasColumn('invoice', 'lokasi_arsip')) {
                $table->dropColumn('lokasi_arsip');
            }
            
            if (Schema::hasColumn('invoice', 'catatan_arsip')) {
                $table->dropColumn('catatan_arsip');
            }
            
            if (Schema::hasColumn('invoice', 'tanggal_arsip')) {
                $table->dropColumn('tanggal_arsip');
            }
            
            if (Schema::hasColumn('invoice', 'tanggal_konfirmasi_diterima')) {
                $table->dropColumn('tanggal_konfirmasi_diterima');
            }
            
            // Remove payment fields (these belong to Receipt model)
            if (Schema::hasColumn('invoice', 'nominal_bayar')) {
                $table->dropColumn('nominal_bayar');
            }
            
            if (Schema::hasColumn('invoice', 'tanggal_bayar')) {
                $table->dropColumn('tanggal_bayar');
            }
            
            if (Schema::hasColumn('invoice', 'metode_bayar')) {
                $table->dropColumn('metode_bayar');
            }
            
            if (Schema::hasColumn('invoice', 'referensi_bayar')) {
                $table->dropColumn('referensi_bayar');
            }
            
            // Remove duplicate total field (use total_invoice instead)
            if (Schema::hasColumn('invoice', 'total_amount')) {
                $table->dropColumn('total_amount');
            }
        });
    }

    /**
     * Migrate existing data from legacy fields to proper fields
     */
    private function migrateExistingData(): void
    {
        // Migrate status_bayar to main status field if needed
        DB::statement("
            UPDATE invoice 
            SET status = CASE 
                WHEN status_bayar = 'lunas' THEN 'paid'
                WHEN status_bayar = 'sebagian' THEN 'sent' 
                WHEN status_bayar = 'belum_dibayar' THEN 'sent'
                ELSE status
            END
            WHERE status_bayar IS NOT NULL AND status IN ('draft', 'sent')
        ");
        
        // Migrate nominal_bayar to total_terbayar if needed
        DB::statement("
            UPDATE invoice 
            SET total_terbayar = COALESCE(nominal_bayar, 0)
            WHERE nominal_bayar IS NOT NULL AND total_terbayar = 0
        ");
        
        // Migrate total_amount to total_invoice if needed
        DB::statement("
            UPDATE invoice 
            SET total_invoice = total_amount
            WHERE total_amount IS NOT NULL AND total_invoice = 0
        ");
        
        // Update sisa_tagihan calculation
        DB::statement("
            UPDATE invoice 
            SET sisa_tagihan = total_invoice - total_terbayar
            WHERE sisa_tagihan != (total_invoice - total_terbayar)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Re-add the legacy fields
            $table->string('status_invoice')->nullable()->after('status');
            $table->string('status_kirim')->nullable()->after('status_invoice');
            $table->string('status_arsip')->nullable()->after('status_kirim');
            $table->string('status_konfirmasi')->nullable()->after('status_arsip');
            $table->string('status_bayar')->nullable()->after('status_konfirmasi');
            $table->datetime('tanggal_kirim')->nullable()->after('tanggal_jatuh_tempo');
            $table->string('metode_kirim')->nullable()->after('tanggal_kirim');
            $table->string('penerima')->nullable()->after('metode_kirim');
            $table->string('lokasi_arsip')->nullable()->after('penerima');
            $table->text('catatan_arsip')->nullable()->after('lokasi_arsip');
            $table->datetime('tanggal_arsip')->nullable()->after('catatan_arsip');
            $table->datetime('tanggal_konfirmasi_diterima')->nullable()->after('tanggal_arsip');
            $table->decimal('nominal_bayar', 15, 2)->nullable()->after('tanggal_konfirmasi_diterima');
            $table->date('tanggal_bayar')->nullable()->after('nominal_bayar');
            $table->string('metode_bayar')->nullable()->after('tanggal_bayar');
            $table->string('referensi_bayar')->nullable()->after('metode_bayar');
            $table->decimal('total_amount', 15, 2)->nullable()->after('total_invoice');
        });
    }
};
