<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_order_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_delivery_order');
            $table->unsignedBigInteger('id_penjualan_detail');
            $table->unsignedBigInteger('id_item');
            $table->string('item_name');
            $table->text('item_description')->nullable();
            $table->decimal('volume_ordered', 15, 2)->comment('Volume dari penjualan detail');
            $table->decimal('volume_delivered', 15, 2)->comment('Volume yang dikirim dalam DO ini');
            $table->string('unit', 50)->default('Liter');
            $table->decimal('unit_price', 15, 2)->default(0);
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_delivery_order')->references('id')->on('delivery_order')->onDelete('cascade');
            $table->foreign('id_penjualan_detail')->references('id')->on('penjualan_detail')->onDelete('cascade');
            $table->foreign('id_item')->references('id')->on('item')->onDelete('cascade');

            // Indexes
            $table->index(['id_delivery_order']);
            $table->index(['id_penjualan_detail']);
            $table->index(['id_item']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_order_details');
    }
};
