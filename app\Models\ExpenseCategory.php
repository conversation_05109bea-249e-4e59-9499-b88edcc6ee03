<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExpenseCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'default_account_id',
        'is_active',
        'is_vehicle_expense',
        'attributes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_vehicle_expense' => 'boolean',
        'deleted_at' => 'date',
        'attributes' => 'json',
    ];

    // Relationships
    public function defaultAccount()
    {
        return $this->belongsTo(Akun::class, 'default_account_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeVehicleExpense($query)
    {
        return $query->where('is_vehicle_expense', true);
    }

    public function scopeNonVehicleExpense($query)
    {
        return $query->where('is_vehicle_expense', false);
    }
}
