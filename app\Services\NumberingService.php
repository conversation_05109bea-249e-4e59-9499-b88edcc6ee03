<?php

namespace App\Services;

use App\Models\NumberingSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NumberingService
{
    /**
     * Generates a new unique number based on settings in the database.
     *
     * @param string $type The entity type (e.g., 'sph', 'transaksi_penjualan', 'expense_request')
     * @return string The generated number string.
     */
    public function generateNumber(string $type): string
    {
        // Use a transaction with locking to prevent two users from getting the same number.
        return DB::transaction(function () use ($type) {
            // 1. Find the setting for the given type and lock it for update.
            $setting = NumberingSetting::where('type', $type)->lockForUpdate()->first();

            // Fallback if no setting is found in the database.
            if (!$setting) {
                Log::error("Numbering setting for type '{$type}' not found.");
                return strtoupper($type) . '-' . time();
            }

            // 2. Check if we need to reset the sequence based on the reset frequency.
            $this->checkAndResetSequence($setting);

            // 3. Increment the sequence.
            $setting->increment('last_sequence');

            // 4. Generate the formatted number.
            $number = $this->formatNumber($setting);

            Log::info("Generated number for type '{$type}': {$number}");

            return $number;
        });
    }

    /**
     * Check if the sequence needs to be reset based on the reset frequency.
     *
     * @param NumberingSetting $setting
     * @return void
     */
    private function checkAndResetSequence(NumberingSetting $setting): void
    {
        $now = Carbon::now();
        $lastReset = $setting->last_reset_date ? Carbon::parse($setting->last_reset_date) : null;

        $shouldReset = match ($setting->reset_frequency) {
            'daily' => !$lastReset || !$lastReset->isSameDay($now),
            'monthly' => !$lastReset || !$lastReset->isSameMonth($now),
            'yearly' => !$lastReset || !$lastReset->isSameYear($now),
            'never' => false,
            default => false,
        };

        if ($shouldReset) {
            $setting->update([
                'last_sequence' => 0,
                'last_reset_date' => $now->toDateString(),
            ]);
            Log::info("Reset sequence for type '{$setting->type}' due to {$setting->reset_frequency} frequency.");
        }
    }

    /**
     * Format the number based on the setting's format template.
     *
     * @param NumberingSetting $setting
     * @return string
     */
    private function formatNumber(NumberingSetting $setting): string
    {
        $now = Carbon::now();
        $sequence = str_pad($setting->last_sequence, $setting->sequence_digits, '0', STR_PAD_LEFT);

        $replacements = [
            '{PREFIX}' => $setting->prefix,
            '{SUFFIX}' => $setting->suffix ?? '',
            '{YEAR}' => $now->format('Y'),
            '{YEAR_SHORT}' => $now->format('y'),
            '{MONTH}' => $now->format('m'),
            '{MONTH_ROMAN}' => $this->toRoman($now->month),
            '{DAY}' => $now->format('d'),
            '{SEQUENCE}' => $sequence,
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $setting->format);
    }

    /**
     * Convert a number to Roman numerals.
     *
     * @param int $number
     * @return string
     */
    private function toRoman(int $number): string
    {
        $map = [
            12 => 'XII', 11 => 'XI', 10 => 'X', 9 => 'IX', 8 => 'VIII',
            7 => 'VII', 6 => 'VI', 5 => 'V', 4 => 'IV', 3 => 'III',
            2 => 'II', 1 => 'I'
        ];

        return $map[$number] ?? (string)$number;
    }

    /**
     * Get the next number that would be generated without actually generating it.
     * Useful for previews.
     *
     * @param string $type
     * @return string
     */
    public function previewNextNumber(string $type): string
    {
        $setting = NumberingSetting::where('type', $type)->first();

        if (!$setting) {
            return 'SETTING_NOT_FOUND';
        }

        // Create a temporary setting with incremented sequence for preview
        $tempSetting = clone $setting;
        $tempSetting->last_sequence = $setting->last_sequence + 1;

        return $this->formatNumber($tempSetting);
    }

    /**
     * Check if a numbering setting exists for the given type.
     *
     * @param string $type
     * @return bool
     */
    public function hasNumberingSetting(string $type): bool
    {
        return NumberingSetting::where('type', $type)->exists();
    }

    /**
     * Create a default numbering setting for a type if it doesn't exist.
     *
     * @param string $type
     * @param array $defaults
     * @return NumberingSetting
     */
    public function createDefaultSetting(string $type, array $defaults = []): NumberingSetting
    {
        $defaultSettings = [
            'prefix' => strtoupper($type),
            'suffix' => null,
            'sequence_digits' => 4,
            'format' => '{PREFIX}/{YEAR}/{MONTH}/{SEQUENCE}',
            'reset_frequency' => 'monthly',
            'last_sequence' => 0,
            'last_reset_date' => now()->toDateString(),
        ];

        $settings = array_merge($defaultSettings, $defaults, ['type' => $type]);

        return NumberingSetting::create($settings);
    }
}
