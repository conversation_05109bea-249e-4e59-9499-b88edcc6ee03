<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Kendaraan;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NewKendaraanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate existing data
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Kendaraan::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->createKendaraanData();
    }

    private function createKendaraanData()
    {
        $kendaraanData = [
            [
                'no_pol_kendaraan' => 'BM 9252 AO',
                'merk' => 'Mitsubishi',
                'tipe' => 'Colt Diesel',
                'kapasitas' => 5000,
                'kapasitas_satuan' => 1, // Assuming Liter unit ID is 1
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2019'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2029'),
                'deskripsi' => 'Kendaraan tangki 5000L - Mitsubishi Colt Diesel 2019',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 9621 AU',
                'merk' => 'Mitsubishi',
                'tipe' => 'Colt Diesel',
                'kapasitas' => 5000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2021'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2031'),
                'deskripsi' => 'Kendaraan tangki 5000L - Mitsubishi Colt Diesel 2021',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 9594 AU',
                'merk' => 'Mitsubishi',
                'tipe' => 'Colt Diesel',
                'kapasitas' => 5000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2021'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2031'),
                'deskripsi' => 'Kendaraan tangki 5000L - Mitsubishi Colt Diesel 2021',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 8288 JO',
                'merk' => 'Mitsubishi',
                'tipe' => 'Fuso',
                'kapasitas' => 5000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2023'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2033'),
                'deskripsi' => 'Kendaraan tangki 5000L - Mitsubishi Fuso 2023',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 8939 AU',
                'merk' => 'Hino',
                'tipe' => 'Ranger',
                'kapasitas' => 16000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2020'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/12/2030'),
                'deskripsi' => 'Kendaraan tangki 16000L - Hino Ranger 2020',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 9817 NU',
                'merk' => 'Hino',
                'tipe' => 'Ranger',
                'kapasitas' => 16000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2022'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/01/2032'),
                'deskripsi' => 'Kendaraan tangki 16000L - Hino Ranger 2022',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 8524 JO',
                'merk' => 'Hino',
                'tipe' => 'Ranger',
                'kapasitas' => 16000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2023'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/12/2033'),
                'deskripsi' => 'Kendaraan tangki 16000L - Hino Ranger 2023',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 9447 JU',
                'merk' => 'Isuzu',
                'tipe' => 'Giga',
                'kapasitas' => 10000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2015'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/01/2025'),
                'deskripsi' => 'Kendaraan tangki 10000L - Isuzu Giga 2015',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 9384 JU',
                'merk' => 'Isuzu',
                'tipe' => 'Giga',
                'kapasitas' => 10000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => Carbon::createFromFormat('d/m/Y', '01/08/2015'),
                'tanggal_akhir_valid' => Carbon::createFromFormat('d/m/Y', '01/12/2025'),
                'deskripsi' => 'Kendaraan tangki 10000L - Isuzu Giga 2015',
                'created_by' => 1,
            ],
            [
                'no_pol_kendaraan' => 'BM 8952 NO',
                'merk' => 'Hino',
                'tipe' => 'Dutro',
                'kapasitas' => 15000,
                'kapasitas_satuan' => 1,
                'tanggal_awal_valid' => null, // Tidak ada tanggal mulai valid
                'tanggal_akhir_valid' => null, // Tidak ada tanggal berakhir valid
                'deskripsi' => 'Kendaraan tangki 15000L - Hino Dutro 2020 (belum ada validitas)',
                'created_by' => 1,
            ],
        ];

        foreach ($kendaraanData as $data) {
            Kendaraan::create($data);
        }

        $this->command->info('✅ Kendaraan data seeded successfully!');
        $this->command->info('📊 Total kendaraan created: ' . count($kendaraanData));
        $this->command->info('🚛 Breakdown by capacity:');
        $this->command->info('   - 5000L: 4 units (Mitsubishi Colt Diesel & Fuso)');
        $this->command->info('   - 10000L: 2 units (Isuzu Giga)');
        $this->command->info('   - 15000L: 1 unit (Hino Dutro)');
        $this->command->info('   - 16000L: 3 units (Hino Ranger)');
        $this->command->info('');
        $this->command->info('📋 Status breakdown:');
        $this->command->info('   - Aktif: 9 units');
        $this->command->info('   - Maintenance: 1 unit (8952 NO - no validity dates)');
    }
}
