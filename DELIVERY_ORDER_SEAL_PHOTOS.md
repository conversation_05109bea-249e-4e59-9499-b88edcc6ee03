# Fitur Upload Foto Segel - Delivery Order

## Overview

Fitur ini memungkinkan pengguna untuk mengunggah foto untuk setiap segel yang ada di Delivery Order. Foto-foto ini akan ditampilkan dalam view detail Delivery Order dengan tampilan yang menarik dan responsif.

## Implementasi

### 1. Model Changes

#### DeliveryOrderSeal Model
- **File:** `app/Models/DeliveryOrderSeal.php`
- **Changes:**
  - Menambahkan `HasMedia` interface dan `InteractsWithMedia` trait
  - Menambahkan `registerMediaCollections()` method untuk collection 'foto_segel'
  - Menambahkan `registerMediaConversions()` method untuk thumbnail, preview, dan large conversions
  - Menambahkan helper methods `getFotoSegelUrlAttribute()` dan `getFotoSegelThumbUrlAttribute()`

```php
// Media Collections
public function registerMediaCollections(): void
{
    $this->addMediaCollection('foto_segel')
        ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
        ->singleFile();
}

// Media Conversions
public function registerMediaConversions(?Media $media = null): void
{
    $this->addMediaConversion('thumb')
        ->width(150)->height(150)->quality(80)
        ->performOnCollections('foto_segel')->nonQueued();
        
    $this->addMediaConversion('preview')
        ->width(400)->height(400)->quality(85)
        ->performOnCollections('foto_segel')->nonQueued();
        
    $this->addMediaConversion('large')
        ->width(800)->height(800)->quality(90)
        ->performOnCollections('foto_segel')->nonQueued();
}
```

### 2. Form Changes

#### DeliveryOrderResource Form
- **File:** `app/Filament/Resources/DeliveryOrderResource.php`
- **Changes:**
  - Menambahkan import `SpatieMediaLibraryFileUpload`
  - Menambahkan field upload foto segel dalam repeater seals

```php
SpatieMediaLibraryFileUpload::make('foto_segel')
    ->label('Foto Segel')
    ->collection('foto_segel')
    ->image()
    ->imageEditor()
    ->imageResizeTargetWidth('800')
    ->imageResizeTargetHeight('800')
    ->helperText('Upload foto segel (maksimal 2MB, format: JPG, PNG)')
    ->maxSize(2048)
    ->columnSpanFull(),
```

### 3. View Changes

#### ViewDeliveryOrder Page
- **File:** `app/Filament/Resources/DeliveryOrderResource/Pages/ViewDeliveryOrder.php`
- **Changes:**
  - Menambahkan section "Foto Segel" terpisah
  - Menggunakan custom view component untuk menampilkan foto-foto segel

```php
// Foto Segel
Section::make('Foto Segel')
    ->schema([
        \Filament\Infolists\Components\ViewEntry::make('seals')
            ->label('')
            ->view('components.delivery-order-seal-photos')
            ->columnSpanFull(),
    ])
    ->visible(fn($record) => $record->seals->filter(fn($seal) => $seal->getFirstMedia('foto_segel'))->count() > 0)
    ->collapsible(),
```

### 4. Custom View Component

#### Delivery Order Seal Photos Component
- **File:** `resources/views/components/delivery-order-seal-photos.blade.php`
- **Features:**
  - Grid layout responsif (1 kolom mobile, 2 kolom tablet, 3 kolom desktop)
  - Card design dengan header informasi segel
  - Preview foto dengan modal untuk full-size viewing
  - Badge untuk jenis segel dengan color coding
  - Informasi file (nama dan ukuran)
  - Support untuk keterangan segel
  - Modal dengan JavaScript untuk preview gambar
  - Keyboard navigation (ESC untuk close modal)

## Features

### Upload Foto Segel
- **Format:** JPG, PNG, GIF, WebP
- **Ukuran maksimal:** 2MB per foto
- **Satu foto per segel:** Single file upload
- **Image editor:** Built-in image editor untuk crop dan resize
- **Auto resize:** Target 800x800px untuk optimasi

### Tampilan Foto Segel
- **Grid responsif:** Otomatis menyesuaikan jumlah kolom berdasarkan ukuran layar
- **Card design:** Setiap segel ditampilkan dalam card terpisah
- **Color coding:** Badge jenis segel dengan warna berbeda:
  - Segel Atas: Hijau
  - Segel Bawah: Biru  
  - Segel Samping: Kuning
  - Lainnya: Abu-abu
- **Modal preview:** Klik foto untuk melihat ukuran penuh
- **File info:** Menampilkan nama file dan ukuran
- **Keterangan:** Menampilkan keterangan segel jika ada

### Conversions
- **Thumbnail:** 150x150px (untuk grid display)
- **Preview:** 400x400px (untuk modal preview)
- **Large:** 800x800px (untuk full-size viewing)
- **Quality optimization:** Kompresi otomatis untuk performa

## Usage

### Menambah Foto Segel
1. Buka form edit/create Delivery Order
2. Scroll ke bagian "Nomor Segel"
3. Untuk setiap segel, klik field "Foto Segel"
4. Upload foto segel (max 2MB)
5. Gunakan image editor jika perlu untuk crop/resize
6. Simpan Delivery Order

### Melihat Foto Segel
1. Buka view detail Delivery Order
2. Scroll ke section "Foto Segel"
3. Foto-foto akan ditampilkan dalam grid
4. Klik foto untuk melihat ukuran penuh dalam modal
5. Gunakan tombol X atau ESC untuk menutup modal

## Technical Notes

### Media Storage
- **Location:** `storage/app/public/`
- **Access:** Via `/storage/` URL
- **Naming:** UUID-based untuk mencegah konflik
- **Collections:** Setiap segel memiliki collection 'foto_segel' terpisah

### Performance
- **Lazy loading:** Foto hanya dimuat saat section dibuka
- **Optimized conversions:** Multiple ukuran untuk berbagai kebutuhan
- **Non-queued:** Conversions diproses langsung untuk response cepat

### Security
- **File validation:** Hanya menerima format gambar yang aman
- **Size limits:** Maksimal 2MB per file
- **MIME type checking:** Validasi tipe file di level server

## Future Enhancements

1. **Bulk upload:** Upload multiple foto sekaligus
2. **Drag & drop:** Interface drag and drop untuk upload
3. **Image annotations:** Menambah keterangan pada area tertentu di foto
4. **Comparison view:** Membandingkan foto segel sebelum dan sesudah
5. **Export functionality:** Export semua foto segel dalam ZIP
6. **Watermarking:** Otomatis menambah watermark pada foto
