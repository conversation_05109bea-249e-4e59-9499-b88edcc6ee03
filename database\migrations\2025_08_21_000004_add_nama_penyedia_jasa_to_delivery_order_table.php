<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add nama_penyedia_jasa field for external service providers
     */
    public function up(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            // Nama penyedia jasa untuk external services
            $table->string('nama_penyedia_jasa', 255)
                ->nullable()
                ->after('biaya_sewa_jasa')
                ->comment('Nama penyedia jasa untuk pelaksana external');
            
            // Index untuk performance
            $table->index(['tipe_pelaksana', 'nama_penyedia_jasa']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['tipe_pelaksana', 'nama_penyedia_jasa']);
            
            // Drop column
            $table->dropColumn('nama_penyedia_jasa');
        });
    }
};
