<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationSetting;
use App\Services\NotificationTemplateService;
use Illuminate\Support\Facades\Log;

class NotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Updates existing notification settings with default message templates.
     */
    public function run(): void
    {
        Log::info('Starting NotificationTemplateSeeder...');

        $defaultTemplates = NotificationTemplateService::getDefaultTemplates();
        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($defaultTemplates as $eventName => $templateData) {
            // Find all notification settings for this event
            $notificationSettings = NotificationSetting::where('event_name', $eventName)->get();

            foreach ($notificationSettings as $setting) {
                // Only update if template is not already set
                if (empty($setting->message_template)) {
                    $setting->update([
                        'message_template' => $templateData['template'],
                        'template_variables' => $templateData['variables'],
                        'message_preview' => NotificationTemplateService::generatePreview($eventName, $templateData['template']),
                    ]);

                    $updatedCount++;
                    Log::info("Updated template for event '{$eventName}' for user " . ($setting->user->name ?? 'Unknown'));
                } else {
                    $skippedCount++;
                    Log::info("Skipped event '{$eventName}' for user " . ($setting->user->name ?? 'Unknown') . " - template already exists");
                }
            }
        }

        Log::info("NotificationTemplateSeeder completed. Updated {$updatedCount} settings, skipped {$skippedCount} existing templates.");

        // Tampilkan ringkasan
        $this->command->info("✅ Updated {$updatedCount} notification templates");
        if ($skippedCount > 0) {
            $this->command->info("ℹ️  {$skippedCount} templates already existed");
        }

        $this->command->info("📝 Templates configured for events:");
        foreach ($defaultTemplates as $eventName => $templateData) {
            $count = NotificationSetting::where('event_name', $eventName)->count();
            $this->command->info("   - {$eventName}: {$count} users");
        }

        // Show total templates
        $totalWithTemplates = NotificationSetting::whereNotNull('message_template')->count();
        $totalSettings = NotificationSetting::count();
        $this->command->info("📊 Total notification settings with templates: {$totalWithTemplates}/{$totalSettings}");
    }
}
