@php
    use Illuminate\Support\Facades\Storage;

    // Get record and supporting documents from field (not media library)
    try {
        $record = $getRecord();
        $supportingDocuments = $record->supporting_documents ?? [];

        // Convert file paths to file objects with metadata
        $fileItems = collect($supportingDocuments)
            ->map(function ($filePath) {
                if (Storage::disk('public')->exists($filePath)) {
                    $fullPath = Storage::disk('public')->path($filePath);
                    $url = Storage::disk('public')->url($filePath);
                    $pathInfo = pathinfo($filePath);
                    $size = Storage::disk('public')->size($filePath);

                    // Format file size
                    $units = ['B', 'KB', 'MB', 'GB'];
                    $formattedSize = $size;
                    $unitIndex = 0;
                    while ($formattedSize > 1024 && $unitIndex < count($units) - 1) {
                        $formattedSize /= 1024;
                        $unitIndex++;
                    }
                    $humanReadableSize = round($formattedSize, 2) . ' ' . $units[$unitIndex];

                    return (object) [
                        'name' => $pathInfo['basename'],
                        'extension' => strtolower($pathInfo['extension'] ?? ''),
                        'path' => $filePath,
                        'url' => $url,
                        'size' => $size,
                        'human_readable_size' => $humanReadableSize,
                    ];
                }
                return null;
            })
            ->filter();
    } catch (Exception $e) {
        $fileItems = collect();
        $debugError = $e->getMessage();
    }

@endphp

@if ($fileItems->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        @foreach ($fileItems as $file)
            <div class="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
                <!-- File Header -->
                <div class="p-3 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            @if (strtolower($file->extension) === 'pdf')
                                <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 18h12V6l-4-4H4v16zm8-14v3h3l-3-3z" />
                                </svg>
                            @elseif(in_array(strtolower($file->extension), ['jpg', 'jpeg', 'png', 'gif']))
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 18h12V6l-4-4H4v16zm8-14v3h3l-3-3z" />
                                </svg>
                            @endif
                            <span class="text-sm font-medium text-gray-700 truncate">
                                {{ $file->name }}
                            </span>
                        </div>
                        <span class="text-xs text-gray-500 uppercase">
                            {{ $file->extension }}
                        </span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ $file->human_readable_size }}
                    </div>
                </div>

                <!-- File Preview -->
                <div class="p-3">
                    @if (strtolower($file->extension) === 'pdf')
                        <!-- PDF Preview -->
                        <div class="aspect-[3/4] bg-gray-100 rounded border overflow-hidden">
                            <iframe src="{{ $file->url }}#toolbar=0&navpanes=0&scrollbar=0&view=FitH"
                                class="w-full h-full" frameborder="0">
                                <p class="p-4 text-center text-gray-500">
                                    Browser tidak mendukung preview PDF.
                                </p>
                            </iframe>
                        </div>
                    @elseif(in_array(strtolower($file->extension), ['jpg', 'jpeg', 'png', 'gif']))
                        <!-- Image Preview -->
                        <div class="aspect-[3/4] bg-gray-100 rounded border overflow-hidden">
                            <img src="{{ $file->url }}" alt="{{ $file->name }}"
                                class="w-full h-full object-cover hover:object-contain transition-all duration-200 cursor-pointer"
                                onclick="openImageModal('{{ $file->url }}', '{{ $file->name }}')">
                        </div>
                    @else
                        <!-- Other File Types -->
                        <div class="aspect-[3/4] bg-gray-100 rounded border flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="currentColor"
                                    viewBox="0 0 20 20">
                                    <path d="M4 18h12V6l-4-4H4v16zm8-14v3h3l-3-3z" />
                                </svg>
                                <p class="text-sm text-gray-500">Preview tidak tersedia</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- File Actions -->
                <div class="p-3 bg-gray-50 border-t border-gray-200">
                    <div class="flex space-x-2">
                        <a href="{{ $file->url }}" target="_blank"
                            class="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            Buka
                        </a>
                        <a href="{{ $file->url }}" download="{{ $file->name }}"
                            class="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Download
                        </a>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4"
        onclick="closeImageModal()">
        <div class="relative max-w-4xl max-h-full">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain">
            <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    </div>

    <script>
        function openImageModal(src, alt) {
            document.getElementById('modalImage').src = src;
            document.getElementById('modalImage').alt = alt;
            document.getElementById('imageModal').classList.remove('hidden');
        }

        function closeImageModal() {
            document.getElementById('imageModal').classList.add('hidden');
        }

        // Close modal on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
@else
    <div class="text-center py-8 text-gray-500">
        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p>Tidak ada dokumen pendukung</p>
    </div>
@endif
