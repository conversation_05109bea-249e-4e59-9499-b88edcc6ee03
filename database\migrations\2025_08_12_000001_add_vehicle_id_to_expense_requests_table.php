<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expense_requests', function (Blueprint $table) {
            // Add vehicle ID field for tank truck maintenance expenses
            $table->unsignedBigInteger('id_kendaraan')->nullable()->after('account_id');
            
            // Add foreign key constraint
            $table->foreign('id_kendaraan')->references('id')->on('kendaraans')->onDelete('set null');
            
            // Add index for better performance
            $table->index(['id_kendaraan', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expense_requests', function (Blueprint $table) {
            // Drop foreign key and index first
            $table->dropForeign(['id_kendaraan']);
            $table->dropIndex(['id_kendaraan', 'category']);
            
            // Drop the column
            $table->dropColumn('id_kendaraan');
        });
    }
};
