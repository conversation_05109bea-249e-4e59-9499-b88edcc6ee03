<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LaporanHarian;
use App\Models\KontrolHarianMT;
use App\Models\User;
use App\Models\Kendaraan;
use App\Models\Jabatan;
use App\Models\Item;
use Carbon\Carbon;

class OperationalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding operational data...');

        // Get users for created_by
        $users = User::all();
        if ($users->isEmpty()) {
            $this->command->warn('No users found. Please run user seeder first.');
            return;
        }

        // Seed Laporan Harian
        $this->seedLaporanHarian($users);

        // Seed Kontrol Harian MT
        $this->seedKontrolHarianMT($users);

        $this->command->info('Operational data seeded successfully!');
    }

    private function seedLaporanHarian($users): void
    {
        $this->command->info('Seeding Laporan Harian...');

        for ($i = 0; $i < 10; $i++) {
            LaporanHarian::create([
                'tanggal_laporan' => Carbon::now()->subDays(rand(0, 30)),
                'catatan' => $this->generateLaporanCatatan(),
                'created_by' => $users->random()->id,
            ]);
        }
    }

    private function seedKontrolHarianMT($users): void
    {
        $this->command->info('Seeding Kontrol Harian MT...');

        // Get actual vehicle plates from database
        $platKendaraan = Kendaraan::pluck('no_pol_kendaraan')->toArray();
        if (empty($platKendaraan)) {
            $platKendaraan = [
                'B 1234 ABC',
                'B 5678 DEF',
                'B 9012 GHI',
                'D 3456 JKL',
                'D 7890 MNO',
            ];
        }

        // Get actual drivers from database
        $driverJabatan = Jabatan::where('nama', 'like', '%driver%')->first();
        $awakMT = [];
        if ($driverJabatan) {
            $awakMT = User::where('id_jabatan', $driverJabatan->id)
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();
        }
        if (empty($awakMT)) {
            $awakMT = [
                'Ahmad Supardi',
                'Budi Santoso',
                'Candra Wijaya',
                'Dedi Kurniawan',
                'Eko Prasetyo',
            ];
        }

        // Get actual security personnel from database
        $securityJabatan = Jabatan::where('nama', 'like', '%security%')
            ->orWhere('nama', 'like', '%sekuriti%')
            ->first();
        $petugasSekuriti = [];
        if ($securityJabatan) {
            $petugasSekuriti = User::where('id_jabatan', $securityJabatan->id)
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();
        }
        if (empty($petugasSekuriti)) {
            $petugasSekuriti = [
                'Joko Susilo',
                'Andi Rahman',
                'Sari Indah',
                'Rini Astuti',
            ];
        }

        $tujuanMT = [
            'SPBU Sudirman',
            'SPBU Gatot Subroto',
            'SPBU Thamrin',
            'SPBU Kuningan',
            'SPBU Senayan',
        ];

        // Get actual items from database
        $jenisMuatan = Item::pluck('name')->toArray();
        if (empty($jenisMuatan)) {
            $jenisMuatan = [
                'Solar',
                'Pertalite',
                'Pertamax',
                'Pertamax Turbo',
                'Dexlite',
                'Pertamina Dex',
            ];
        }

        for ($i = 0; $i < 15; $i++) {
            $jamBerangkat = Carbon::createFromTime(rand(6, 10), rand(0, 59));
            $jamPulang = $jamBerangkat->copy()->addHours(rand(6, 12))->addMinutes(rand(0, 59));

            KontrolHarianMT::create([
                'tanggal_kontrol' => Carbon::now()->subDays(rand(0, 30)),
                'plat_kendaraan' => $platKendaraan[array_rand($platKendaraan)],
                'awak_mobil_tangki' => $awakMT[array_rand($awakMT)],
                'petugas_sekuriti' => $petugasSekuriti[array_rand($petugasSekuriti)],
                'tujuan_mt' => $tujuanMT[array_rand($tujuanMT)],
                'jam_keberangkatan' => $jamBerangkat->format('H:i:s'),
                'jam_pulang' => rand(0, 1) ? $jamPulang->format('H:i:s') : null,
                'jenis_muatan' => $jenisMuatan[array_rand($jenisMuatan)],

                // Pre Trip Inspection MT (random true/false with higher chance of true)
                'perlengkapan_bongkar_muatan' => rand(1, 10) > 2,
                'ban_serap_ada' => rand(1, 10) > 1,
                'lampu_rotari_menyala' => rand(1, 10) > 2,
                'apar_ada' => rand(1, 10) > 1,
                'kondisi_kebersihan' => rand(1, 10) > 3 ? 'bersih' : 'kurang_bersih',
                'surat_kendaraan_dibawa' => rand(1, 10) > 1,

                // AMT Inspection (random true/false with higher chance of true)
                'awak_mt_sehat' => rand(1, 10) > 1,
                'awak_mt_safety' => rand(1, 10) > 2,
                'kelengkapan_surat_awak_ready' => rand(1, 10) > 2,

                'created_by' => $users->random()->id,
            ]);
        }
    }

    private function generateLaporanCatatan(): string
    {
        $catatan = [
            'Semua kegiatan berjalan normal. Tidak ada kendala berarti.',
            'Ada sedikit keterlambatan pengiriman karena cuaca hujan.',
            'Mobil tangki mengalami masalah kecil pada ban, sudah diperbaiki.',
            'Tamu dari kantor pusat berkunjung untuk inspeksi rutin.',
            'Kegiatan bongkar muat berjalan lancar sesuai jadwal.',
            'Ada maintenance rutin pada fasilitas depot.',
            'Koordinasi dengan driver berjalan dengan baik.',
            'Semua prosedur keselamatan telah dijalankan dengan benar.',
            'Tidak ada insiden keselamatan hari ini.',
            'Cuaca mendukung untuk operasional normal.',
        ];

        return $catatan[array_rand($catatan)];
    }
}
