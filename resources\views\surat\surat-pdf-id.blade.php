@php
    use App\Support\Formatter;
@endphp

@php
    $locale = 'id';
    $letterSetting = $record->letterSetting;

    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();
@endphp

<!DOCTYPE html>
<html lang="id">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ strip_tags($record->title) }}</title>
    <style>
        @page {
            margin: 20mm 15mm 20mm 15mm;
            size: A4;
        }

        * {
            margin: 5px 10px;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
        }

        .header {
            margin-bottom: 0px;
            /* border-bottom: 2px solid #000; */
            padding-bottom: 0px;
        }

        .header table {
            width: 100%;
            border-collapse: collapse;
        }

        .logo {
            height: 80px;
            width: auto;
        }

        .company-info {
            text-align: center;
            vertical-align: middle;
        }

        .company-info h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #000;
        }

        .company-info p {
            font-size: 10px;
            margin: 2px 0;
        }

        .partner-text {
            text-align: right;
            vertical-align: middle;
        }

        .partner-text h2 {
            font-size: 14px;
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
        }

        .partner-text p {
            font-size: 10px;
            color: #666;
        }

        .doc-info {
            margin-bottom: 10px;
        }

        .date {
            text-align: right;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .doc-info table {
            width: 100%;
            border-collapse: collapse;
        }

        .doc-info td {
            padding: 2px 0;
            vertical-align: top;
        }

        .recipient {
            margin-bottom: 10px;
        }

        .recipient p {
            margin: 5px 0;
        }

        .body-content {
            margin-bottom: 10px;
            text-align: justify;
            line-height: 1.6;
        }

        .body-content p {
            margin-bottom: 5px;
        }

        .signature-section {
            margin-top: 20px;
            text-align: right;
        }

        .signature-box {
            display: inline-block;
            text-align: center;
            min-width: 200px;
        }

        .signature-name {
            margin-top: 60px;
            border-top: 1px solid #000;
            padding-top: 5px;
            font-weight: bold;
        }

        .signature-title {
            font-size: 11px;
            margin-top: 2px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            font-size: 10px;
            text-align: center;
        }

        .iso-certifications {
            margin-top: 10px;
            text-align: center;
            font-size: 9px;
        }

        .iso-certifications span {
            margin: 0 10px;
            font-weight: bold;
        }
    </style>
</head>

<body>
    {{-- Header Section --}}
    <div class="header">
        <table>
            <tr>
                <td style="width: 25%;">
                    @php
                        $logoPath = public_path('images/lrp.png');
                        $logoExists = file_exists($logoPath);
                    @endphp

                    @if ($logoExists)
                        <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                            alt="PT. Lintas Riau Prima" class="logo">
                    @else
                        <div
                            style="height: 80px; width: 150px; border: 1px solid #ccc; text-align: center; padding-top: 30px; font-size: 12px; color: #666;">
                            PT. LINTAS RIAU PRIMA
                        </div>
                    @endif
                </td>
                {{-- <td style="width: 50%;" class="company-info">
                    <h1>PT. LINTAS RIAU PRIMA</h1>
                    <p>{{ $letterSetting?->address ?? 'Jl. Raya Lintas Timur KM 16, Kelurahan Tuah Karya, Kecamatan Tampan, Kota Pekanbaru, Riau 28293' }}
                    </p>
                    <p>Telp: {{ $letterSetting?->phone_number ?? '0761-123456' }} | Email:
                        {{ $letterSetting?->email ?? '<EMAIL>' }}</p>
                    <p>Website: {{ $letterSetting?->website ?? 'www.lintasriauprima.com' }}</p>
                </td> --}}
                <td style="width: 25%;" class="partner-text">
                    <h2>TRUSTED & RELIABLE PARTNER</h2>
                    <p>Fuel Agent – Fuel Transportation – Bunker Service</p>
                </td>
            </tr>
        </table>
    </div>

    {{-- Document Info Section --}}
    <div class="doc-info">
        <div class="date">
            {{ $letterSetting?->city ?? 'Pekanbaru' }},
            {{ $record->surat_date->locale('id')->translatedFormat('d F Y') }}
        </div>

        <table>
            <tr>
                <td style="width: 80px;">No</td>
                <td style="width: 10px;">:</td>
                <td><strong>{{ $record->surat_number }}</strong></td>
            </tr>
            <tr>
                <td>Lampiran</td>
                <td>:</td>
                <td>-</td>
            </tr>
            <tr>
                <td style="vertical-align: top;">Perihal</td>
                <td style="vertical-align: top;">:</td>
                <td><strong>{{ $record->title }}</strong></td>
            </tr>
        </table>
    </div>

    {{-- Body Content --}}
    <div class="body-content">
        {!! mb_convert_encoding($record->content, 'UTF-8', 'UTF-8') !!}
    </div>

    {{-- Signature Section --}}
    <div class="signature-section">
        @if ($record->signedBy)
            <div class="signature-box">
                <p>Hormat kami,</p>

                {{-- Logo and signature container --}}
                <div style="width: 250px; height: auto; text-align: left; position: relative;">
                    @if ($record->signedBy->signature_path)
                        @php
                            $logoPath = public_path('images/lrp-logo-noBG.png');
                            $signaturePath = storage_path('app/public/' . $record->signedBy->signature_path);
                        @endphp

                        {{-- Container for both the signature and the logo --}}
                        <div style="position: relative; width: 100%; height: auto;">
                            {{-- The company stamp/logo is positioned at the bottom layer --}}
                            @if (file_exists($logoPath))
                                <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                                    alt="Company Stamp"
                                    style="width: 120px; height: auto; position: absolute; top: -30; left: 0; z-index: 1;">
                            @endif

                            {{-- The user's signature is positioned on top --}}
                            @if (file_exists($signaturePath))
                                @php
                                    $signatureExtension = pathinfo($signaturePath, PATHINFO_EXTENSION);
                                    $mimeType = match (strtolower($signatureExtension)) {
                                        'png' => 'image/png',
                                        'jpg', 'jpeg' => 'image/jpeg',
                                        'gif' => 'image/gif',
                                        default => 'image/png',
                                    };
                                @endphp
                                <img src="data:{{ $mimeType }};base64,{{ base64_encode(file_get_contents($signaturePath)) }}"
                                    alt="Signature"
                                    style="width: auto; height: 100px; mix-blend-mode: darken; position: absolute; top: -40px; left: 40px; z-index: 2;">
                            @endif
                        </div>
                    @endif
                </div>

                <div class="signature-name">{{ $record->signedBy->name }}</div>
                <div class="signature-title">{{ $record->signedBy->jabatan?->nama ?? 'Direktur' }}</div>
            </div>
        @else
            <div class="signature-box">
                <p>Hormat kami,</p>
                <br><br><br>
                <div class="signature-name">_____________________</div>
                <div class="signature-title">Direktur</div>
            </div>
        @endif
    </div>

    {{-- Footer --}}
   <div class="footer">
        <table>
            <tr>
                <td style="width: 33%;" class="iso-logos">
                    @foreach ($isoCertifications as $cert)
                        @php $logoPath = public_path('storage/' . $cert->logo_path); @endphp
                        @if (file_exists($logoPath))
                            <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                                alt="{{ $cert->name }}" style="height: 40px; margin-right: 10px;">
                        @endif
                    @endforeach
                </td>
                <td style="width: 34%;" class="center">
                    <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                    <p>{{ $letterSetting?->address }}</p>
                </td>
                <td style="width: 33%;" class="right">
                    <p>Tel: 0761-22369</p>
                    <p>Email: <EMAIL></p>
                    <p>Web: www.lintasriauprima.com</p>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>
