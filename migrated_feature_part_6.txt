# AKUNTANSI MODULE MIGRATION - PART 6: INCOME STATEMENT, BALANC<PERSON> SHEET, AND SERVICES
# This file contains Income Statement, Balance Sheet Pages, and Services for the Accounting Module

# ============================================================================
# FILE: app/Filament/Pages/IncomeStatement.php
# ============================================================================
<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Models\Akun;
use App\Models\JournalEntry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class IncomeStatement extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Laporan Laba Rugi';

    protected static ?string $title = 'Laporan Laba Rugi';

    protected static ?string $navigationGroup = 'Akuntansi';

    protected static ?int $navigationSort = 31;

    protected static string $view = 'filament.pages.income-statement';

    public ?array $data = [];
    public $start_date = null;
    public $end_date = null;

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth(),
            'end_date' => now()->endOfMonth(),
        ]);

        $this->start_date = now()->startOfMonth()->format('Y-m-d');
        $this->end_date = now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Periode Laporan')
                    ->schema([
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->start_date = $state;
                            }),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('Tanggal Akhir')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->end_date = $state;
                            }),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    public function getIncomeStatementData(): array
    {
        if (!$this->start_date || !$this->end_date) {
            return [
                'revenues' => [],
                'expenses' => [],
                'total_revenue' => 0,
                'total_expense' => 0,
                'net_income' => 0,
            ];
        }

        // Get revenue accounts (Pendapatan)
        $revenueAccounts = Akun::where('kategori_akun', 'Pendapatan')->get();
        $revenues = [];
        $totalRevenue = 0;

        foreach ($revenueAccounts as $account) {
            $balance = $this->getAccountBalance($account, $this->start_date, $this->end_date);
            if ($balance != 0) {
                $revenues[] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
                $totalRevenue += $balance;
            }
        }

        // Get expense accounts (Beban)
        $expenseAccounts = Akun::where('kategori_akun', 'Beban')->get();
        $expenses = [];
        $totalExpense = 0;

        foreach ($expenseAccounts as $account) {
            $balance = $this->getAccountBalance($account, $this->start_date, $this->end_date);
            if ($balance != 0) {
                $expenses[] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
                $totalExpense += $balance;
            }
        }

        $netIncome = $totalRevenue - $totalExpense;

        return [
            'revenues' => $revenues,
            'expenses' => $expenses,
            'total_revenue' => $totalRevenue,
            'total_expense' => $totalExpense,
            'net_income' => $netIncome,
        ];
    }

    private function getAccountBalance($account, $startDate, $endDate)
    {
        $entries = JournalEntry::where('account_id', $account->id)
            ->whereHas('journal', function (Builder $query) use ($startDate, $endDate) {
                $query->where('status', 'Posted')
                    ->whereBetween('transaction_date', [
                        Carbon::parse($startDate)->startOfDay(),
                        Carbon::parse($endDate)->endOfDay(),
                    ]);
            })
            ->get();

        $totalDebit = $entries->sum('debit');
        $totalCredit = $entries->sum('credit');

        // For revenue accounts (credit normal balance), return credit - debit
        // For expense accounts (debit normal balance), return debit - credit
        if ($account->kategori_akun === 'Pendapatan') {
            return $totalCredit - $totalDebit;
        } else {
            return $totalDebit - $totalCredit;
        }
    }
}

# ============================================================================
# FILE: app/Filament/Pages/BalanceSheet.php
# ============================================================================
<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Models\Akun;
use App\Models\JournalEntry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class BalanceSheet extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationLabel = 'Neraca';

    protected static ?string $title = 'Laporan Neraca';

    protected static ?string $navigationGroup = 'Akuntansi';

    protected static ?int $navigationSort = 32;

    protected static string $view = 'filament.pages.balance-sheet';

    public ?array $data = [];
    public $report_date = null;

    public function mount(): void
    {
        $this->form->fill([
            'report_date' => now(),
        ]);

        $this->report_date = now()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Tanggal Laporan')
                    ->schema([
                        Forms\Components\DatePicker::make('report_date')
                            ->label('Tanggal Laporan')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->report_date = $state;
                            }),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    public function getBalanceSheetData(): array
    {
        if (!$this->report_date) {
            return [
                'assets' => [],
                'liabilities' => [],
                'equity' => [],
                'total_assets' => 0,
                'total_liabilities' => 0,
                'total_equity' => 0,
                'is_balanced' => false,
            ];
        }

        // Get asset accounts
        $assetAccounts = Akun::where('kategori_akun', 'Aset')->orderBy('kode_akun')->get();
        $assets = [];
        $totalAssets = 0;

        foreach ($assetAccounts as $account) {
            $balance = $this->getAccountBalance($account, $this->report_date);
            if ($balance != 0) {
                $assets[] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
                $totalAssets += $balance;
            }
        }

        // Get liability accounts
        $liabilityAccounts = Akun::where('kategori_akun', 'Kewajiban')->orderBy('kode_akun')->get();
        $liabilities = [];
        $totalLiabilities = 0;

        foreach ($liabilityAccounts as $account) {
            $balance = $this->getAccountBalance($account, $this->report_date);
            if ($balance != 0) {
                $liabilities[] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
                $totalLiabilities += $balance;
            }
        }

        // Get equity accounts
        $equityAccounts = Akun::where('kategori_akun', 'Ekuitas')->orderBy('kode_akun')->get();
        $equity = [];
        $totalEquity = 0;

        foreach ($equityAccounts as $account) {
            $balance = $this->getAccountBalance($account, $this->report_date);
            if ($balance != 0) {
                $equity[] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
                $totalEquity += $balance;
            }
        }

        // Calculate retained earnings (accumulated net income)
        $retainedEarnings = $this->getRetainedEarnings($this->report_date);
        if ($retainedEarnings != 0) {
            $equity[] = [
                'account' => (object) [
                    'kode_akun' => '3999',
                    'nama_akun' => 'Laba Ditahan',
                ],
                'balance' => $retainedEarnings,
            ];
            $totalEquity += $retainedEarnings;
        }

        $isBalanced = abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01;

        return [
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'total_assets' => $totalAssets,
            'total_liabilities' => $totalLiabilities,
            'total_equity' => $totalEquity,
            'total_liabilities_equity' => $totalLiabilities + $totalEquity,
            'is_balanced' => $isBalanced,
        ];
    }

    private function getAccountBalance($account, $reportDate)
    {
        $entries = JournalEntry::where('account_id', $account->id)
            ->whereHas('journal', function (Builder $query) use ($reportDate) {
                $query->where('status', 'Posted')
                    ->where('transaction_date', '<=', Carbon::parse($reportDate)->endOfDay());
            })
            ->get();

        $totalDebit = $entries->sum('debit');
        $totalCredit = $entries->sum('credit');

        $balance = $account->saldo_awal ?? 0;

        if ($account->tipe_akun === 'Debit') {
            $balance += $totalDebit - $totalCredit;
        } else {
            $balance += $totalCredit - $totalDebit;
        }

        return $balance;
    }

    private function getRetainedEarnings($reportDate)
    {
        // Calculate accumulated net income up to report date
        $revenueAccounts = Akun::where('kategori_akun', 'Pendapatan')->get();
        $expenseAccounts = Akun::where('kategori_akun', 'Beban')->get();

        $totalRevenue = 0;
        $totalExpense = 0;

        foreach ($revenueAccounts as $account) {
            $entries = JournalEntry::where('account_id', $account->id)
                ->whereHas('journal', function (Builder $query) use ($reportDate) {
                    $query->where('status', 'Posted')
                        ->where('transaction_date', '<=', Carbon::parse($reportDate)->endOfDay());
                })
                ->get();

            $totalRevenue += $entries->sum('credit') - $entries->sum('debit');
        }

        foreach ($expenseAccounts as $account) {
            $entries = JournalEntry::where('account_id', $account->id)
                ->whereHas('journal', function (Builder $query) use ($reportDate) {
                    $query->where('status', 'Posted')
                        ->where('transaction_date', '<=', Carbon::parse($reportDate)->endOfDay());
                })
                ->get();

            $totalExpense += $entries->sum('debit') - $entries->sum('credit');
        }

        return $totalRevenue - $totalExpense;
    }
}
