<?php

namespace App\Filament\Resources\TransaksiJasaResource\Pages;

use App\Filament\Resources\TransaksiJasaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Forms;
use Filament\Notifications\Notification;
use App\Services\TransaksiJasaService;
use App\Models\TransaksiPenjualan;
use App\Models\NotificationSetting;

class ViewTransaksiJasa extends ViewRecord
{
    protected static string $resource = TransaksiJasaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            // create do if approve

            Actions\Action::make('create_do')
                ->label('Buat DO')
                ->icon('heroicon-o-truck')
                ->color('success')
                ->url(fn() => route('filament.admin.resources.delivery-orders.create', [
                    'id_transaksi' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if transaction is approved
                    return $this->record->status === 'approved';
                })
                ->tooltip('Buat delivery order untuk transaksi ini'),

            // invoice
            Actions\Action::make('create_invoice')
                ->label('Buat Invoice')
                ->icon('heroicon-o-document-plus')
                ->color('primary')
                ->url(fn() => route('filament.admin.resources.invoices.create', [
                    'id_transaksi' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if transaction is approved and no invoice exists yet
                    return $this->record->status === 'approved' && !$this->record->invoices()->exists();
                })
                ->tooltip('Buat invoice untuk transaksi ini'),
            Actions\Action::make('view_invoices')
                ->label('Lihat Invoice')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn() => route('filament.admin.resources.invoices.index', [
                    'tableFilters[transaksi_jasa][value]' => $this->record->id
                ]))
                ->visible(function () {
                    // Only show if invoices exist
                    return $this->record->invoices()->exists();
                })
                ->tooltip('Lihat invoice yang sudah dibuat'),

            // Action untuk menampilkan status rejected
            Actions\Action::make('rejected_status')
                ->label('Transaksi Ditolak')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->disabled()
                ->visible(function () {
                    // Show if transaction is rejected
                    return $this->record->status === 'rejected';
                })
                ->tooltip(function () {
                    // Get the latest rejection note
                    $latestApproval = $this->record->approvals()
                        ->where('status', 'rejected')
                        ->latest()
                        ->first();

                    $note = $latestApproval?->note ?? 'Tidak ada catatan';
                    return "Transaksi ini ditolak. Alasan: {$note}";
                }),

            // Action untuk menampilkan status needs revision
            Actions\Action::make('needs_revision_status')
                ->label('Perlu Perbaikan')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->disabled()
                ->visible(function () {
                    // Show if transaction needs revision
                    return $this->record->status === 'needs_revision';
                })
                ->tooltip(function () {
                    // Get the latest revision note
                    $latestApproval = $this->record->approvals()
                        ->where('status', 'reject_with_perbaikan')
                        ->latest()
                        ->first();

                    $note = $latestApproval?->note ?? 'Tidak ada catatan';
                    return "Transaksi ini perlu diperbaiki. Catatan: {$note}";
                }),
            // Action untuk menampilkan status "Belum Ada Approval" (untuk authorized approver)
            Actions\Action::make('no_approval_yet')
                ->label('Belum Ada Approval')
                ->icon('heroicon-o-exclamation-circle')
                ->color('info')
                ->disabled()
                ->visible(function () {
                    // Show if pending approval, no approvals exist yet, and user is authorized approver
                    return $this->record->status === 'pending_approval'
                        && !$this->record->approvals()->exists()
                        && $this->isCurrentUserAuthorizedApprover();
                })
                ->tooltip('Transaksi ini belum diproses approval. Klik tombol "Proses Approval" untuk memulai.'),

            // Action untuk menampilkan status "Tidak Berhak Approval" (untuk non-authorized user)
            Actions\Action::make('not_authorized_approver')
                ->label('Tidak Berhak Approval')
                ->icon('heroicon-o-shield-exclamation')
                ->color('gray')
                ->disabled()
                ->visible(function () {
                    // Show if pending approval and user is NOT authorized approver
                    return $this->record->status === 'pending_approval' && !$this->isCurrentUserAuthorizedApprover();
                })
                ->tooltip('Anda tidak memiliki hak untuk memproses approval transaksi ini. Hanya user yang terdaftar sebagai approver yang dapat melakukan approval.'),

            // Approval action for service transactions
            Actions\Action::make('approval')
                ->label('Proses Approval')
                ->color('primary')
                ->icon('heroicon-o-check-badge')
                ->visible(function () {
                    // Only show if transaction is pending approval AND current user is authorized approver
                    return $this->record->status === 'pending_approval' && $this->isCurrentUserAuthorizedApprover();
                })
                ->form([
                    Forms\Components\Select::make('status')
                        ->label('Keputusan Approval')
                        ->options([
                            'approved' => 'Setujui',
                            'rejected' => 'Tolak',
                            'reject_with_perbaikan' => 'Tolak dengan Perbaikan',
                        ])
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Forms\Set $set, $state) {
                            if ($state === 'approved') {
                                $set('note', null);
                            }
                        }),

                    Forms\Components\Textarea::make('note')
                        ->label('Catatan')
                        ->placeholder('Berikan catatan untuk keputusan approval...')
                        ->visible(fn(Forms\Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan']))
                        ->required(fn(Forms\Get $get) => in_array($get('status'), ['rejected', 'reject_with_perbaikan']))
                        ->rows(3),
                ])
                ->action(function (TransaksiPenjualan $record, array $data, TransaksiJasaService $jasaService) {
                    $jasaService->processApproval(
                        $record,
                        \Illuminate\Support\Facades\Auth::user(),
                        $data['status'],
                        $data['note'] ?? null
                    );

                    Notification::make()
                        ->title('Proses approval berhasil disimpan')
                        ->success()
                        ->send();
                }),
        ];
    }

    /**
     * Check if the current user is authorized to approve service transactions
     */
    private function isCurrentUserAuthorizedApprover(): bool
    {
        $user = auth()->user();

        if (!$user) {
            return false;
        }

        // Check if user has notification settings for service transaction approval events
        // This indicates they are authorized to receive and act on approval notifications
        $hasApprovalNotificationSettings = NotificationSetting::where('user_id', $user->id)
            ->whereIn('event_name', [
                'transaksi_jasa_approved',
                'transaksi_jasa_rejected',
                'transaksi_jasa_needs_revision'
            ])
            ->where('is_active', true)
            ->exists();

        return $hasApprovalNotificationSettings;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Transaksi Jasa')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('kode')
                                    ->label('Nomor SO')
                                    ->icon('heroicon-o-document-text')
                                    ->color('primary')
                                    ->weight('bold'),

                                TextEntry::make('tanggal')
                                    ->label('Tanggal Transaksi')
                                    ->date('d/m/Y H:i')
                                    ->icon('heroicon-o-calendar'),

                                TextEntry::make('pelanggan.nama')
                                    ->label('Pelanggan')
                                    ->icon('heroicon-o-user')
                                    ->color('success'),

                                TextEntry::make('nomor_po')
                                    ->label('Nomor PO')
                                    ->placeholder('Tidak ada PO')
                                    ->icon('heroicon-o-document'),

                                TextEntry::make('nomor_sph')
                                    ->label('Nomor SPH')
                                    ->placeholder('Tidak ada SPH')
                                    ->icon('heroicon-o-document-text'),

                                TextEntry::make('letterSetting.name')
                                    ->label('Format Surat')
                                    ->placeholder('Tidak Ada')
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('letterSetting.locale')
                                    ->label('Mata Uang')
                                    ->placeholder('Tidak Ada')
                                    ->formatStateUsing(fn($state) => match ($state) {
                                        'id' => 'Rupiah (IDR)',
                                        'en' => 'Dollar (USD)',
                                        default => $state
                                    })
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        'id' => 'success',
                                        'en' => 'warning',
                                        default => 'gray'
                                    }),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Informasi Pelanggan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('pelanggan.kode')
                                    ->label('Kode Pelanggan')
                                    ->icon('heroicon-o-identification'),

                                TextEntry::make('pelanggan.type')
                                    ->label('Tipe Pelanggan')
                                    ->badge()
                                    ->color(fn($state) => match ($state) {
                                        'individual' => 'info',
                                        'corporate' => 'success',
                                        default => 'gray'
                                    }),

                                TextEntry::make('pelanggan.pic_nama')
                                    ->label('PIC')
                                    ->icon('heroicon-o-user-circle'),

                                TextEntry::make('pelanggan.pic_phone')
                                    ->label('Telepon PIC')
                                    ->icon('heroicon-o-phone'),

                                TextEntry::make('alamatPelanggan.alamat')
                                    ->label('Alamat Pelanggan')
                                    ->icon('heroicon-o-map-pin')
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Detail Jasa')
                    ->schema([
                        TextEntry::make('penjualanDetails')
                            ->label('Daftar Jasa')
                            ->listWithLineBreaks()
                            ->formatStateUsing(function ($record) {
                                return $record->penjualanDetails->map(function ($detail) use ($record) {
                                    $total = number_format($detail->volume_item * $detail->harga_jual, 0, ',', '.');

                                    $currency = 'IDR';
                                    if ($record->letterSetting && $record->letterSetting->locale === 'en') {
                                        $currency = 'USD';
                                    }

                                    return "• {$detail->item->name} - Qty: {$detail->volume_item} - Harga: {$currency} " . number_format($detail->harga_jual, 0, ',', '.') . " - Total: {$currency} {$total}";
                                })->implode("\n");
                            })
                            ->columnSpanFull(),
                    ]),


                Section::make('Status Transaksi')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'draft' => 'gray',
                                'pending' => 'warning',
                                'approved' => 'success',
                                'rejected' => 'danger',
                                default => 'gray'
                            }),
                    ]),

                Section::make('Informasi Sistem')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('createdBy.name')
                                    ->label('Dibuat Oleh')
                                    ->icon('heroicon-o-user'),

                                TextEntry::make('created_at')
                                    ->label('Dibuat Pada')
                                    ->dateTime('d/m/Y H:i')
                                    ->icon('heroicon-o-clock'),

                                TextEntry::make('updated_at')
                                    ->label('Terakhir Diupdate')
                                    ->dateTime('d/m/Y H:i')
                                    ->icon('heroicon-o-arrow-path'),
                            ]),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public function getTitle(): string
    {
        return 'Detail Transaksi Jasa';
    }
}
