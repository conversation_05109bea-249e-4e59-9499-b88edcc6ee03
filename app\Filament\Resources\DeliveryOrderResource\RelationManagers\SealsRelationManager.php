<?php

namespace App\Filament\Resources\DeliveryOrderResource\RelationManagers;

use App\Models\DeliveryOrderSeal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class SealsRelationManager extends RelationManager
{
    protected static string $relationship = 'seals';

    protected static ?string $title = 'Segel Delivery Order';

    protected static ?string $modelLabel = 'Segel';

    protected static ?string $pluralModelLabel = 'Segel';

    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Segel')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('nomor_segel')
                                    ->label('Nomor Segel')
                                    ->placeholder('Contoh: SGL-000001')
                                    ->required()
                                    ->maxLength(100),

                                Forms\Components\Select::make('jenis_segel')
                                    ->label('Jenis Segel')
                                    ->options(DeliveryOrderSeal::JENIS_SEGEL)
                                    ->default('atas')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('urutan')
                                    ->label('Urutan')
                                    ->numeric()
                                    ->default(function () {
                                        return DeliveryOrderSeal::getNextUrutan($this->ownerRecord->id);
                                    })
                                    ->minValue(1)
                                    ->maxValue(99),

                                Forms\Components\TextInput::make('keterangan')
                                    ->label('Keterangan')
                                    ->placeholder('Keterangan tambahan (opsional)')
                                    ->maxLength(255),
                            ]),

                        SpatieMediaLibraryFileUpload::make('foto_segel')
                            ->label('Foto Segel')
                            ->collection('foto_segel')
                            ->image()
                            ->imageEditor()
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('800')
                            ->helperText('Upload foto segel (maksimal 2MB, format: JPG, PNG)')
                            ->maxSize(2048)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nomor_segel')
            ->columns([
                Tables\Columns\TextColumn::make('urutan')
                    ->label('Urutan')
                    ->sortable()
                    ->width('80px'),

                Tables\Columns\TextColumn::make('nomor_segel')
                    ->label('Nomor Segel')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->icon('heroicon-o-lock-closed'),

                Tables\Columns\BadgeColumn::make('jenis_segel')
                    ->label('Jenis Segel')
                    ->formatStateUsing(
                        fn(string $state): string =>
                        DeliveryOrderSeal::JENIS_SEGEL[$state] ?? $state
                    )
                    ->colors([
                        'success' => 'atas',
                        'info' => 'bawah',
                        'warning' => 'samping',
                        'gray' => 'lainnya',
                    ]),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(30)
                    ->placeholder('Tidak ada keterangan')
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\ImageColumn::make('foto_segel')
                    ->label('Foto')
                    ->getStateUsing(function ($record) {
                        return $record->getFirstMediaUrl('foto_segel', 'thumb');
                    })
                    ->height(50)
                    ->width(50)
                    ->defaultImageUrl(url('/images/no-image.png'))
                    ->tooltip('Klik untuk melihat foto'),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_segel')
                    ->label('Jenis Segel')
                    ->options(DeliveryOrderSeal::JENIS_SEGEL),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function () {
                        Notification::make()
                            ->title('Segel berhasil ditambahkan')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Segel')
                    ->modalContent(function ($record) {
                        $fotoUrl = $record->getFirstMediaUrl('foto_segel', 'large');

                        return view('filament.components.seal-detail-modal', [
                            'record' => $record,
                            'fotoUrl' => $fotoUrl
                        ]);
                    }),

                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['updated_by'] = auth()->id();
                        return $data;
                    }),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('urutan', 'asc')
            ->reorderable('urutan');
    }
}
